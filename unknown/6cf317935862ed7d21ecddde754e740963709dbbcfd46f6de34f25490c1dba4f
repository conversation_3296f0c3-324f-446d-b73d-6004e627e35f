<?php

namespace App\Repository;

use App\Entity\NpsQuestionDetail;
use App\Entity\NpsQuestion;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<NpsQuestionDetail>
 *
 * @method NpsQuestionDetail|null find($id, $lockMode = null, $lockVersion = null)
 * @method NpsQuestionDetail|null findOneBy(array $criteria, array $orderBy = null)
 * @method NpsQuestionDetail[]    findAll()
 * @method NpsQuestionDetail[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class NpsQuestionDetailRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, NpsQuestionDetail::class);
    }

    public function add(NpsQuestionDetail $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(NpsQuestionDetail $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function getQuestionsBySurvey(NpsQuestion $npsQuestion){
        return $this->createQueryBuilder('nqd')
        ->select('nqd.id', 'nqd.value')
        ->where('nqd.npsQuestion = :question')
        ->setParameter('question', $npsQuestion)
        ->getQuery()
        ->getResult();
    }

    public function getNpsQuestionDetailTranslation(NpsQuestionDetail $npsQuestionDetail, $locale){

        $nameQuestion = $npsQuestionDetail->getValue();
        if ($locale) {
            /** @var NpsQuestionDetailTranslation $translation */
            $translation = $npsQuestionDetail->translate($locale);
            $nameQuestion = $translation->getValue();
        }

        return $nameQuestion;
       
    }


}
