<?php

declare(strict_types=1);

namespace App\Security\Integrations\Clients;

use App\Entity\IntegrationGroup;
use App\Security\Integrations\IntegrationMappingService;
use App\Security\Integrations\IntegrationService;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class ChefBurgerClientConfigurator extends BaseClientConfigurator
{
    private UserPasswordHasherInterface $userPasswordHasher;

    public function __construct(
        HttpClientInterface $httpClient,
        IntegrationMappingService $integrationMappingService,
        IntegrationService $integrationService,
        EntityManagerInterface $em,
        SettingsService $settingsService,
        LoggerInterface $logger,
        UserPasswordHasherInterface $userPasswordHasher
    ) {
        parent::__construct($httpClient, $integrationMappingService, $integrationService, $em, $settingsService, $logger);
        $this->userPasswordHasher = $userPasswordHasher;
    }

    public function __invoke(ClientInterface $client): void
    {
        if (!($client instanceof ChefBurgerClient)) {
            throw new \InvalidArgumentException('Client must be an instance of ChefBurgerClient');
        }
        parent::__invoke($client);

        $integrationGroup = $this->em->getRepository(IntegrationGroup::class)->findOneBy(['name' => ChefBurgerClient::CLIENT_NAME, 'active' => true]);

        $client->setUserPasswordHasher($this->userPasswordHasher);
        $client->setIntegrationGroupId($integrationGroup ? $integrationGroup->getId() : -1);
        $client->setIdCia((int) ($_ENV['CHEF_BURGER_ID_CIA'] ?? -1));
        $client->setConniKey($_ENV['CHEF_BURGER_CONNI_KEY'] ?? '');
        $client->setConniToken($_ENV['CHEF_BURGER_CONNI_TOKEN'] ?? '');
    }
}
