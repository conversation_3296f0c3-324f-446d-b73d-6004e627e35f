<?php

declare(strict_types=1);

namespace App\Controller\Apiv1;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Exception\ExcededApiRequestsException;
use App\Exception\InvalidApiKeyException;
use App\Exception\InvalidDateFormatException;
use App\Exception\InvalidDateRangeException;
use App\Service\Apiv1\AnnouncementService;
use App\Service\Catalog\ListCatalog\Announcement\AnnouncementModalityServices;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * @Route("/api/v1")
 */
class AnnouncementsController extends ApiBaseController
{
    /**
     * @Route("/announcements", methods={"POST"})
     *
     * @throws \Exception
     */
    public function __invoke(AnnouncementService $announcementService): Response
    {
        $apiKey = $this->requestStack->getCurrentRequest()->headers->get(self::API_KEY_FIELD);

        try {
            $this->checkAccess($apiKey);
        } catch (InvalidApiKeyException $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_UNAUTHORIZED,
                'data' => ['error' => 'X-API-KEY header is missing or invalid.'],
            ]);
        } catch (ExcededApiRequestsException $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_TOO_MANY_REQUESTS,
                'data' => ['error' => 'Too many requests.'],
            ]);
        }

        $requestContent = json_decode($this->requestStack->getCurrentRequest()->getContent(), true);

        $from = $requestContent['date_from'] ?? '';
        $to = $requestContent['date_to'] ?? '';

        try {
            $this->checkDates($from, $to);
        } catch (InvalidDateFormatException $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_BAD_REQUEST,
                'data' => ['error' => 'The date format is not valid.'],
            ]);
        } catch (InvalidDateRangeException $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_REQUESTED_RANGE_NOT_SATISFIABLE,
                'data' => ['error' => 'The date range is not valid.'],
            ]);
        }

        $this->saveRequest($apiKey, 'announcements');

        $announcementRepository = $this->entityManager->getRepository(Announcement::class);
        $announcements = $announcementRepository->createQueryBuilder('announcement')
            ->addOrderBy('announcement.startAt', 'ASC')
            ->andWhere('announcement.startAt BETWEEN :from AND :to')
            ->orWhere('announcement.finishAt BETWEEN :from AND :to')
            ->setParameter('from', $from)
            ->setParameter('to', $to)
            ->getQuery()
            ->getResult();

        $data = [];
        foreach ($announcements as $announcement) {
            /* @var Announcement $announcement */

            $data[] = $announcementService->getAnnouncementData($announcement);
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'data' => $data,
        ]);
    }

    private function getUsers(Announcement $announcement): array
    {
        $users = [];
        foreach ($announcement->getCalled() as $announcementUser) {
            $users[] = [
                'id' => $announcementUser->getUser()->getId(),
                'firstName' => $announcementUser->getUser()->getFirstName(),
                'lastName' => $announcementUser->getUser()->getLastName(),
                'email' => $announcementUser->getUser()->getEmail(),
                'assistanceConfirmation' => $announcementUser->isIsConfirmationAssistance(),
                'external' => $announcementUser->isExternal(),
            ];
        }

        return $users;
    }

    private function getGroups(Announcement $announcement): array
    {
        $groups = [];
        foreach ($announcement->getAnnouncementGroups() as $group) {
            $groups[] = [
                'id' => $group->getId(),
                'code' => $group->getCode(),
                'sessions' => $this->getSessions($group),
            ];
        }

        return $groups;
    }

    private function getSessions(AnnouncementGroup $announcementGroup): array
    {
        $sessions = [];
        foreach ($announcementGroup->getAnnouncementGroupSessions() as $session) {
            $sessions[] = [
                'id' => $session->getId(),
                'startAt' => $session->getStartAt(),
                'finishAt' => $session->getFinishAt(),
                'assistance' => $this->formatAssistance($session->getAssistance()),
                'timezone' => $session->getTimezone(),
                'place' => $session->getPlace(),
                'type' => $session->getType(),
            ];
        }

        return $sessions;
    }

    private function formatAssistance(array $assistance): array
    {
        // change id key to userId without changing the rest of the keys
        return array_map(function ($item) {
            $item['userId'] = $item['id'];
            unset($item['id']);

            return $item;
        }, $assistance);
    }

    /**
     * @Route("/announcement_modalities", methods={"GET"})
     *
     * @throws \Exception
     */
    public function getAnnouncementModalities(AnnouncementModalityServices $announcementModalityServices): Response
    {
        $apiKey = $this->requestStack->getCurrentRequest()->headers->get(self::API_KEY_FIELD);

        try {
            $this->checkAccess($apiKey);
        } catch (InvalidApiKeyException $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_UNAUTHORIZED,
                'data' => ['error' => 'X-API-KEY header is missing or invalid.'],
            ]);
        } catch (ExcededApiRequestsException $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_TOO_MANY_REQUESTS,
                'data' => ['error' => 'Too many requests.'],
            ]);
        }

        $this->saveRequest($apiKey, 'user');

        $activeModalities = $announcementModalityServices->getActiveModalities();

        if (!$activeModalities) {
            return $this->sendResponse([
                'status' => Response::HTTP_NOT_FOUND,
                'data' => ['error' => 'No types/modalities found.'],
            ]);
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'data' => $activeModalities,
        ]);
    }
}
