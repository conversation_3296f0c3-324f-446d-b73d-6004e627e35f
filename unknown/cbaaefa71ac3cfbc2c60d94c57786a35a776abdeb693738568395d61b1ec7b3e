<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\Announcement;
use App\Entity\Course;
use App\Entity\EmailNotification;
use App\Entity\Itinerary;
use App\Entity\User;
use App\Entity\UserNotification;
use App\Enum\EmailNotificacion;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<EmailNotification>
 *
 * @method EmailNotification|null find($id, $lockMode = null, $lockVersion = null)
 * @method EmailNotification|null findOneBy(array $criteria, array $orderBy = null)
 * @method EmailNotification[]    findAll()
 * @method EmailNotification[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class EmailNotificationRepository extends ServiceEntityRepository implements EmailNotificacion
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, EmailNotification::class);
    }

    public function add(EmailNotification $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(EmailNotification $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function listAllPending($user, $request)
    {
        $from = new \DateTime();
        $from->sub(new \DateInterval('P30D'));

        $query = $this->createQueryBuilder('e')
            ->select('e.id, e.type, e.sent, e.createdAt, e.title, e.message, e.attributes, e.translationText, e.translationTitle')
            /*
            ->innerJoin(
                UserNotification::class,
                'un',
                \Doctrine\ORM\Query\Expr\Join::WITH,
                'e.user = un.user'
            )*/
            ->andWhere('e.user = :user')
            ->setParameter('user', $user)
            // ->andWhere('un.isActive = 1')
            ->andWhere('e.deletedAt is null');

        if ('' != $request->get('seeLast30')) {
            $query
                ->andWhere('e.createdAt >= :from')
                ->setParameter(':from', $from->format('Y-m-d H:i'));
        }

        // ordenar por fecha de manera ascedente
        $query->orderBy('e.createdAt', 'DESC');

        return $query->getQuery()->getArrayResult();
    }

    public function findItineraryUser($lang, $id = null)
    {
        $query = $this->createQueryBuilder('e')
            ->select('e.id, u.email, u.firstName, u.lastName, i.name')
            ->innerJoin(
                User::class,
                'u',
                \Doctrine\ORM\Query\Expr\Join::WITH,
                'e.user = u.id'
            )
            ->innerJoin(
                Itinerary::class,
                'i',
                \Doctrine\ORM\Query\Expr\Join::WITH,
                'e.itinerary = i.id'
            )->distinct()
            ->andWhere('e.deletedAt is null')
            ->andWhere('e.sent = false')
            ->andWhere('e.type = :type')
            ->setParameter('type', self::ITINERARY)
            ->andWhere('u.locale = :lang')
            ->setParameter('lang', $lang);

        if (!\is_null($id)) {
            $query
                ->andWhere('e.id = :id')
                ->setParameter('id', $id);
        }

        return $query->getQuery()->getArrayResult();
    }

    public function findAnnouncementUser($lang, $id = null)
    {
        $query = $this->createQueryBuilder('e')
            ->select('e.id, u.email, u.firstName, u.lastName, c.name')
            ->innerJoin(
                User::class,
                'u',
                \Doctrine\ORM\Query\Expr\Join::WITH,
                'e.user = u.id'
            )
            ->innerJoin(
                Announcement::class,
                'a',
                \Doctrine\ORM\Query\Expr\Join::WITH,
                'e.announcement = a.id'
            )
            ->innerJoin(
                Course::class,
                'c',
                \Doctrine\ORM\Query\Expr\Join::WITH,
                'a.course = c.id'
            )
            ->distinct()
            ->andWhere('e.deletedAt is null')
            ->andWhere('e.sent = false')
            ->andWhere('e.type = :type')
            ->setParameter('type', self::ANNOUNCED)
            ->andWhere('u.locale = :lang')
            ->setParameter('lang', $lang);

        if ($_ENV['SEND_NOTIFICATIONS_EVERYONE']) {
            $query
                ->innerJoin(
                    UserNotification::class,
                    'un',
                    \Doctrine\ORM\Query\Expr\Join::WITH,
                    'a.course = c.id'
                )
                ->andWhere('not un.isActive is null');
        }

        if (!\is_null($id)) {
            $query
                ->andWhere('e.id = :id')
                ->setParameter('id', $id);
        }

        return $query->getQuery()->getArrayResult();
    }

    public function findAnnouncementUserRemenber($lang)
    {
        $to = new \DateTime();
        $now = new \DateTime();
        $now->add(new \DateInterval('PT4320M'));
        $to->add(new \DateInterval('PT4321M'));

        $query = $this->createQueryBuilder('e')
            ->select('e.id, u.email, u.firstName, u.lastName, c.name')
            ->innerJoin(
                User::class,
                'u',
                \Doctrine\ORM\Query\Expr\Join::WITH,
                'e.user = u.id'
            )
            ->innerJoin(
                Announcement::class,
                'a',
                \Doctrine\ORM\Query\Expr\Join::WITH,
                'e.announcement = a.id'
            )
            ->innerJoin(
                Course::class,
                'c',
                \Doctrine\ORM\Query\Expr\Join::WITH,
                'a.course = c.id'
            )
            ->distinct()
            ->andWhere('e.type = :type')
            ->setParameter('type', 'announced')
            ->andWhere('u.locale = :lang')
            ->setParameter('lang', $lang)
            ->andWhere('e.deletedAt is null')
            ->andWhere('a.finishAt between :from and :to')
            ->setParameter(':from', $now->format('Y-m-d H:i'))
            ->setParameter(':to', $to->format('Y-m-d H:i'));

        print_r($now->format('Y-m-d H:i'));
        print_r('    ');
        print_r($to->format('Y-m-d H:i'));

        return $query->getQuery()->getArrayResult();
    }

    public function markSentItems($data, $newStatus = true)
    {
        $query = $this->createQueryBuilder('e')
            ->update(EmailNotification::class, 'e')
            ->set('e.sent', ':status')
            ->setParameter('status', $newStatus);

        if ($newStatus) {
            $query->set('e.updatedAt', "'" . (string) date('Y-m-d H:i:s') . "'");
        } else {
            $query->set('e.updatedAt', 'e.createdAt');
        }

        foreach ($data as $item) {
            $query = $query->orWhere('e.id = ' . $item['id']);
        }

        return $query->getQuery()->getSQL();
    }

    public function markDeletedItems($data)
    {
        $query = $this->createQueryBuilder('e')
            ->update(EmailNotification::class, 'e');

        $query->set('e.deletedAt', "'" . (string) date('Y-m-d H:i:s') . "'");

        foreach ($data as $item) {
            $query = $query->orWhere('e.id = ' . $item);
        }

        return $query->getQuery()->getArrayResult();
    }

    public function findEmailNotificationByExtra($extra)
    {
        return $this->createQueryBuilder('e')
        ->where('e.extra LIKE :extraPattern')
        ->setParameter('extraPattern', '%' . json_encode($extra) . '%')
        ->getQuery()
        ->getResult();
    }
}
