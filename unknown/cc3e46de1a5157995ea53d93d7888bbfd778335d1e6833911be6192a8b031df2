<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250428141115 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE announcement_manager (
                id INT AUTO_INCREMENT NOT NULL,
                announcement_id INT NOT NULL,
                manager_id INT NOT NULL,
                INDEX IDX_6857873913AEA17 (announcement_id),
                INDEX IDX_6857873783E3463 (manager_id),
                PRIMARY KEY(id)
            ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE announcement_manager ADD CONSTRAINT FK_6857873913AEA17 FOREIGN KEY (announcement_id)
                REFERENCES announcement (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE announcement_manager ADD CONSTRAINT FK_6857873783E3463 FOREIGN KEY (manager_id)
                REFERENCES user (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE lti_chapter ADD CONSTRAINT FK_D6C02C34A03ABBD6 FOREIGN KEY (lti_tool_id)
                REFERENCES lti_tool (id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_D6C02C34A03ABBD6 ON lti_chapter (lti_tool_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user_course
                ADD created_by_id INT DEFAULT NULL,
                ADD updated_by_id INT DEFAULT NULL,
                ADD deleted_by_id INT DEFAULT NULL,
                ADD created_at DATETIME DEFAULT NULL,
                ADD updated_at DATETIME DEFAULT NULL,
                ADD deleted_at DATETIME DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user_course ADD CONSTRAINT FK_73CC7484B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user_course ADD CONSTRAINT FK_73CC7484896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user_course ADD CONSTRAINT FK_73CC7484C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_73CC7484B03A8386 ON user_course (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_73CC7484896DBBDE ON user_course (updated_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_73CC7484C76F1F52 ON user_course (deleted_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user_course_chapter
                ADD created_by_id INT DEFAULT NULL,
                ADD updated_by_id INT DEFAULT NULL,
                ADD deleted_by_id INT DEFAULT NULL,
                ADD created_at DATETIME DEFAULT NULL,
                ADD deleted_at DATETIME DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user_course_chapter ADD CONSTRAINT FK_9AC76F9BB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user_course_chapter ADD CONSTRAINT FK_9AC76F9B896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user_course_chapter ADD CONSTRAINT FK_9AC76F9BC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_9AC76F9BB03A8386 ON user_course_chapter (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_9AC76F9B896DBBDE ON user_course_chapter (updated_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_9AC76F9BC76F1F52 ON user_course_chapter (deleted_by_id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE announcement_manager DROP FOREIGN KEY FK_6857873913AEA17
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE announcement_manager DROP FOREIGN KEY FK_6857873783E3463
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE announcement_manager
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user_course DROP FOREIGN KEY FK_73CC7484B03A8386
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user_course DROP FOREIGN KEY FK_73CC7484896DBBDE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user_course DROP FOREIGN KEY FK_73CC7484C76F1F52
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_73CC7484B03A8386 ON user_course
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_73CC7484896DBBDE ON user_course
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_73CC7484C76F1F52 ON user_course
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user_course
                DROP created_by_id,
                DROP updated_by_id,
                DROP deleted_by_id,
                DROP created_at,
                DROP updated_at,
                DROP deleted_at
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user_course_chapter DROP FOREIGN KEY FK_9AC76F9BB03A8386
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user_course_chapter DROP FOREIGN KEY FK_9AC76F9B896DBBDE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user_course_chapter DROP FOREIGN KEY FK_9AC76F9BC76F1F52
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_9AC76F9BB03A8386 ON user_course_chapter
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_9AC76F9B896DBBDE ON user_course_chapter
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_9AC76F9BC76F1F52 ON user_course_chapter
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user_course_chapter
                DROP created_by_id,
                DROP updated_by_id,
                DROP deleted_by_id,
                DROP created_at,
                DROP deleted_at
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE lti_chapter DROP FOREIGN KEY FK_D6C02C34A03ABBD6
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_D6C02C34A03ABBD6 ON lti_chapter
        SQL);
    }
}
