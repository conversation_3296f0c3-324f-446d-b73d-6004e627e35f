<?php

declare(strict_types=1);

namespace App\Command;

use App\Entity\IntegrationUsersData;
use App\Security\Integrations\Clients\IntegrationClientException;
use App\Security\Integrations\Clients\IntegrationClients;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

class IntegrationsCommand extends Command
{
    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly SettingsService $settingsService,
        private readonly IntegrationClients $integrationClients,
        private readonly EntityManagerInterface $em
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this->setName('app:integrations:execute')
            ->setDescription('Execute n enabled clients configured for integrations');

        $this->addOption(
            'execute',
            'a:i:e',
            InputOption::VALUE_OPTIONAL,
            "Override parameter configuration defined in 'integrations.cron'",
            false
        );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $execute = filter_var($input->getOption('execute'), FILTER_VALIDATE_BOOLEAN);
        /** @var IntegrationUsersData|null $integrationData */
        $integrationData = null;

        try {
            $enabled = filter_var($this->settingsService->get('integrations.execute'), FILTER_VALIDATE_BOOLEAN);
            if (!$enabled && !$execute) {
                $io->note("'integrations.execute' is set to false. Execution not enabled.");

                return Command::FAILURE;
            }

            $inicio = new \DateTime('today');
            $fin = new \DateTime('tomorrow');

            /** @var IntegrationUsersData|null $integrationData */
            $integrationData = $this->em->getRepository(IntegrationUsersData::class)->createQueryBuilder('i')
                ->where('i.created_at >= :inicio')
                ->andWhere('i.created_at < :fin')
                ->setParameter('inicio', $inicio)
                ->setParameter('fin', $fin)
                ->getQuery()
                ->getOneOrNullResult();

            if ($integrationData && $integrationData->isRunning()) {
                $io->warning('app:integrations:execute already running');

                return Command::SUCCESS;
            }

            if (!$integrationData) {
                $integrationData = new IntegrationUsersData();
                $integrationData->setCreatedAt(new \DateTimeImmutable())
                    ->setParams([])
                    ->setCompleted(false)
                ;
            }
            $integrationData->setRunning(true);
            $this->em->persist($integrationData);
            $this->em->flush();

            $enabledClients = $this->integrationClients->getEnabledClients();
            $io->info('Calling ' . \count($enabledClients) . ' clients');
            $io->info('start: ' . (new \DateTimeImmutable())->format('c'));
            foreach ($enabledClients as $clientTreeName => $configuration) {
                try {
                    $this->executeClient($clientTreeName, $configuration, $integrationData);
                } catch (IntegrationClientException $e) {
                    $io->error(
                        'Client ' . empty($configuration['name']) ? $clientTreeName : $configuration['name'] . ' not found'
                    );
                }
            }

            $io->info('Finish: ' . (new \DateTimeImmutable())->format('c'));

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage(), $e->getTrace());
            $io->error($e->getMessage());

            return Command::FAILURE;
        } finally {
            if ($integrationData) {
                $integrationData->setRunning(false);
                $this->em->persist($integrationData);
                $this->em->flush();
            }
        }
    }

    /**
     * @param array<string, mixed> $configuration
     *
     * @throws IntegrationClientException
     */
    private function executeClient(
        string $clientTreeName,
        array $configuration,
        IntegrationUsersData $integrationData
    ): void {
        if (!empty($configuration['name'])) {
            $client = $this->integrationClients->getClient($configuration['name']);
        } else {
            $client = $this->integrationClients->getClient($clientTreeName);
        }

        if ($integrationData->getCompleted()) {
            return;
        }

        $params = !empty($integrationData->getParams()) ? $integrationData->getParams() : [];
        $params = $client->getUsers($params);

        $integrationData->setParams($params);
        $integrationData->setUpdatedAt(new \DateTime());
        $integrationData->setCompleted($params['completed']);
        $this->em->persist($integrationData);
        $this->em->flush();
    }
}
