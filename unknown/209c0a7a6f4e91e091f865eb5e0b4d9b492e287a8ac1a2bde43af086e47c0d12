<?php

declare(strict_types=1);

namespace App\Controller\Admin\Api;

use App\Admin\Traits\SerializerTrait;
use App\Admin\Traits\UserManagerTrait;
use App\Entity\Course;
use App\Entity\User;
use App\Entity\ZipFileTask;
use App\Enum\StatsReportType;
use App\Service\Course\Common\UserCourseService;
use App\Service\Course\GlobalFilter;
use App\Service\Course\Stats\General\ContextStatsService;
use App\Service\Course\Stats\General\CourseStatsService;
use App\Service\Course\Stats\Persons\CoursePersonService;
use App\Service\Course\Stats\Persons\CoursePersonStrategy;
use App\Service\Diploma\DiplomaService;
use App\Service\SettingsService;
use App\Service\StatsUser\ResultGameService;
use App\Service\ZipFileTask\ZipFileTaskService;
use App\StatsAndExcel\Enum\StatsExportEnum;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\Annotations as Rest;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * @Route("/admin/api/v1/course-stats")
 */
class CourseStatsController extends AbstractController
{
    use SerializerTrait;
    use UserManagerTrait;

    private EntityManagerInterface $em;
    private ResultGameService $resultGameService;
    private LoggerInterface $logger;
    private ParameterBagInterface $params;
    private SettingsService $settings;
    private GlobalFilter $globalFilter;
    private CoursePersonService $coursePersonService;
    private CourseStatsService $generalCourseStatsService;
    private UserCourseService $userCourseService;
    private DiplomaService $diplomaService;
    private ZipFileTaskService $zipFileTaskService;

    public function __construct(
        EntityManagerInterface $em,
        ResultGameService $resultGameService,
        LoggerInterface $logger,
        ParameterBagInterface $params,
        SettingsService $settings,
        GlobalFilter $globalFilter,
        CoursePersonService $coursePersonService,
        CourseStatsService $generalCourseStatsService,
        UserCourseService $userCourseService,
        DiplomaService $diplomaService,
        ZipFileTaskService $zipFileTaskService
    ) {
        $this->em = $em;
        $this->resultGameService = $resultGameService;
        $this->logger = $logger;
        $this->params = $params;
        $this->settings = $settings;
        $this->globalFilter = $globalFilter;
        $this->coursePersonService = $coursePersonService;
        $this->generalCourseStatsService = $generalCourseStatsService;
        $this->userCourseService = $userCourseService;
        $this->diplomaService = $diplomaService;
        $this->zipFileTaskService = $zipFileTaskService;
    }

    /**
     * @Rest\Route("/{id}/users", methods={"GET","POST"})
     */
    public function getUsers(Request $request, Course $course): Response
    {
        $enableTab = $this->settings->get('app.course.tab.person');
        $tabCall = 'CourseStatsUsers' === $request->get('tabCall');
        $sendData = !(!$enableTab && $tabCall);

        /** @var User $user */
        $user = $this->getUser();

        if ($sendData) {
            $persons = new CoursePersonStrategy($this->coursePersonService);
            $users = $persons->getPersons($course);

            return $this->sendResponse($users);
        } else {
            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => [
                    'totalUsers' => 0,
                    'totalStarted' => 0,
                    'totalFinished' => 0,
                    'data' => [],
                ],
            ]);
        }
    }

    /**
     * @Rest\Route("/{id}/chapters-stats", methods={"GET","POST"})
     */
    public function getChaptersStats(Request $request, Course $course): Response
    {
        try {
            $enableTab = $this->settings->get('app.course.tab.stats');
            $tabCall = 'CourseStatsDetails' === $request->get('tabCall') ? true : false;
            $sendData = !$enableTab && $tabCall ? false : true;

            if (!$sendData) {
                return $this->sendResponse([
                    'status' => Response::HTTP_OK,
                    'error' => false,
                    'data' => [
                        'data' => [],
                        'totalUsers' => 0,
                        'totalStarted' => 0,
                        'totalFinished' => 0,
                    ],
                ]);
            }

            $strategy = new ContextStatsService($this->generalCourseStatsService);

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $strategy->getStats($course),
                'pathImage' => $this->params->get('app.chapter_uploads_path') . '/',
                'announcement' => $request->get('announcementId') ?? null,
            ]);
        } catch (\Exception $e) {
            $this->logger->error('Error in getChaptersStatsV2', [
                'exception' => $e,
                'courseId' => $course->getId()
            ]);

            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'message' => 'An unexpected error occurred. Please try again later.',
                'trace' => 'dev' === $_ENV['APP_ENV'] ? $e->getTrace() : [],
            ]);
        }
    }

    /**
     * @Rest\Post("/{id}/xlsx")
     */
    public function generateReport(Request $request, Course $course): Response
    {
        $content = json_decode($request->getContent(), true);
        $announcementId = $request->get('announcementId');
        $content['announcementId'] = is_numeric($announcementId) ? (int) $announcementId : null;
        $fileName = '';
        if (\array_key_exists('userId', $content)) {
            $user = $this->em->getRepository(User::class)->find($content['userId']);
            $fileName .= $user->getFullName() . ' - ';
        }

        $fileName .= $course->getName();
        // $this->em->getRepository(ZipFileTask::class)->newZipFileTask(
        //     'course_perCourseStatsReport',
        //     $course->getId(),
        //     StatsReportType::COURSE,
        //     $content,
        //     $fileName . ' - ' . (new \DateTimeImmutable())->format('d-m-Y H:i:s')
        // );

        $this->zipFileTaskService->enqueueZipFileTask(
            $this->getUser(),
            'course_perCourseStatsReport',
            $content,
            StatsReportType::COURSE,
            $fileName . ' - ' . (new \DateTimeImmutable())->format('d-m-Y H:i:s'),
            $course->getId()
        );

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $content,
        ]);
    }

    /**
     * @Rest\Get("/{id}/diploma/{userId}")
     */
    public function generateDiploma(Course $course, int $userId): Response
    {
        try {
            $request = ['idCourse' => $course->getId()];
            $user = $this->em->getRepository(User::class)->find($userId);
            $output = $this->diplomaService->generateDiplomaDefault($request, $user);
            $response = [
                'status' => 200,
                'error' => false,
                'data' => $output['diploma'],
                'nombre' => $output['nombre']
            ];
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'Ha ocurrido un error al descargar el pdf {' . $e->getMessage() . '}'
            ];
        }

        return $this->sendResponse($response);
    }

    /**
     * @Rest\Post("/diplomas")
     */
    public function exportReportDiplomas(
        Request $request
    ) {
        $content = json_decode($request->getContent(), true);
        if (!$content) {
            throw new \Exception('No se recibieron datos en la solicitud.');
        }

        $conditions['activeUsers'] = $content['active'] ?? null;
        $conditions['courseId'] = $content['courseId'] ?? null;
        $filtersCompletedCourseAndSurvey = $this->settings->get('app.course.filters.diploma');

        if ($filtersCompletedCourseAndSurvey) {
            $conditions['courseCompleted'] = $content['finished'] ?? null;
            $conditions['surveyCompleted'] = $content['surveyCompleted'] ?? null;
        }

        $conditions['courseFilters'] = $content['selectFilterCourse'] ?? [];
        $default_name = StatsExportEnum::FILENAME_DIPLOMAS;
        $filename = !empty($content['filename']) ? $content['filename'] : $default_name;

        $dataExist = $this->diplomaService->generateDiplomas($conditions);

        if (\is_string($dataExist) && 15 == \strlen($dataExist)) {
            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => 'No Data found',
                'data' => '',
            ]);
        }

        $this->zipFileTaskService->enqueueZipFileTask(
            $this->getUser(),
            'diploma_courses_report',
            $conditions,
            StatsReportType::DIPLOMAS,
            $filename . ' - ' . (new \DateTimeImmutable())->format('d-m-Y H:i:s'),
            0
        );

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $conditions,
        ]);
    }

    /**
     * @Rest\Post("/diplomas-user")
     */
    public function exportReportDiplomasUser(
        Request $request
    ) {
        $content = json_decode($request->getContent(), true);
        if (!$content) {
            throw new \Exception('No se recibieron datos en la solicitud.');
        }

        $conditions['userId'] = $content['userId'] ?? null;
        $conditions['typeOfDiplomas'] = $content['typeOfDiplomas'] ?? null;
        $conditions['filename'] = $content['filename'] ?? null;

        $default_name = StatsExportEnum::FILENAME_DIPLOMAS_USER;
        $filename = !empty($content['filename']) ? $content['filename'] : $default_name;

        $dataExist = $this->diplomaService->generateUserDiplomas($conditions);

        if (\is_string($dataExist) && 20 === \strlen($dataExist)) {
            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => 'No Data found',
                'data' => '',
            ]);
        }

        $this->zipFileTaskService->enqueueZipFileTask(
            $this->getUser(),
            'diplomas_user_report',
            $conditions,
            StatsReportType::DIPLOMAS_USER,
            $filename . ' - ' . (new \DateTimeImmutable())->format('d-m-Y H:i:s'),
            0
        );

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $conditions,
        ]);
    }
}
