<?php

declare(strict_types=1);

namespace App\Service\Task;

use App\Entity\Export;
use App\Entity\Task;
use App\Entity\User;
use App\Exception\TaskLimitExceededException;
use App\Exception\TaskNotFoundException;
use App\Service\SettingsService;
use App\Service\SlotManagerService;
use App\Service\TaskLimitService;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class TaskService
{
    private const DEFAULT_SLOT_QUANTITY = 3;
    public const DEFAULT_LONG_RUNNING_TYPE_TASKS = [];
    private const DEFAULT_TASK_TIMEOUT = 1200;

    private EntityManagerInterface $em;
    protected LoggerInterface $logger;
    public $translator;
    private SettingsService $settings;
    private TaskLimitService $taskLimitService;
    private SlotManagerService $slotManagerService;

    public function __construct(
        EntityManagerInterface $em,
        LoggerInterface $logger,
        TranslatorInterface $translator,
        SettingsService $settings,
        TaskLimitService $taskLimitService,
        SlotManagerService $slotManagerService
    ) {
        $this->em = $em;
        $this->logger = $logger;
        $this->translator = $translator;
        $this->settings = $settings;
        $this->taskLimitService = $taskLimitService;
        $this->slotManagerService = $slotManagerService;
    }

    public function getTaskTimeout(): int
    {
        return (int) $this->settings->get('app.export.task.timeout', self::DEFAULT_TASK_TIMEOUT);
    }

    public function getLongRunningTypeTasks(): array
    {
        return (array) $this->settings->get('app.export.task.long_running_type_tasks', self::DEFAULT_LONG_RUNNING_TYPE_TASKS);
    }

    public function getSlotQuantity(): int
    {
        return (int) $this->settings->get('app.export.task.slot_quantity', self::DEFAULT_SLOT_QUANTITY);
    }

    public function getExpiredTaskWithExport(): array
    {
        $timeThresholds = [
            'startedBefore' => new \DateTime('-' . $this->getTaskTimeout() . ' seconds')
        ];

        $qb = $this->em->getRepository(Export::class)->createQueryBuilder('e');

        $query = $qb
            ->select('e')
            ->join('e.task', 't')
            ->where('t.status IN (:statusList)')
            ->andWhere('t.deletedAt IS NULL')
            ->andWhere(
                $qb->expr()->orX(
                    't.startedAt IS NULL',
                    't.startedAt < :startedBefore'
                )
            )
            ->setParameter('statusList', [Task::TASK_INPROGRESS])
            ->setParameter('startedBefore', $timeThresholds['startedBefore'])
            ->orderBy('t.id', 'DESC')
            ->getQuery()
        ;

        try {
            return $query->getResult();
        } catch (\Exception $e) {
            $this->logger->error('Error getting expired tasks: ' . $e->getMessage());

            return [];
        }
    }

    public function getExpiredTaskWithoutExport(): array
    {
        $timeThresholds = [
            'startedBefore' => new \DateTime('-' . $this->getTaskTimeout() . ' seconds')
        ];

        $qb = $this->em->getRepository(Task::class)->createQueryBuilder('t');

        $query = $qb
            ->leftJoin('t.export', 'e')
            ->where('e.id IS NULL')
            ->andWhere('t.deletedAt IS NULL')
            ->andWhere('t.status IN (:statusList)')
            ->andWhere(
                $qb->expr()->orX(
                    't.startedAt IS NULL',
                    't.startedAt < :startedBefore'
                )
            )
            ->setParameter('statusList', [Task::TASK_INPROGRESS])
            ->setParameter('startedBefore', $timeThresholds['startedBefore'])
            ->orderBy('t.id', 'DESC')
            ->getQuery()
        ;

        try {
            return $query->getResult();
        } catch (\Exception $e) {
            $this->logger->error('Error getting pending tasks: ' . $e->getMessage());

            return [];
        }
    }

    private function validateUserTaskLimit(?User $user): void
    {
        if (!$user) {
            return;
        }

        $taskRepository = $this->em->getRepository(Task::class);

        $pendingTasksCount = $taskRepository->countPendingTasksByUser($user);

        if ($pendingTasksCount >= $this->taskLimitService->getLimit()) {
            throw TaskLimitExceededException::forUser($user, $pendingTasksCount, $this->translator, $this->taskLimitService);
        }
    }

    public function enqueueTask(
        ?User $user,
        string $taskType,
        array $conditions = [],
        string $type = '',
        string $filename = ''
    ) {
        $this->validateUserTaskLimit($user);

        $exportRepository = $this->em->getRepository(Export::class);

        return $exportRepository->newExportTask($taskType, $type, $conditions, $filename);
    }

    public function getNextTask(bool $includeLongRunningTasks = true): Task
    {
        // Check slot availability through SlotManagerService
        $executionSlot = $this->slotManagerService->getAvailableTaskExecutionSlot();

        // We respect the input parameter, but if the slot does not allow long-running tasks,
        // we force it to false
        if (!$executionSlot->allowsLongRunningTasks()) {
            $includeLongRunningTasks = false;
        }

        $qb = $this->em->getRepository(Task::class)->createQueryBuilder('t')
            ->where('t.status = :status')
            ->andWhere('t.deletedAt IS NULL')
            ->setParameter('status', Task::TASK_PENDING)
            ->orderBy('t.createdAt', 'ASC')
            ->setMaxResults(1);

        if (!$includeLongRunningTasks) {
            $qb->andWhere('t.type NOT IN (:longRunningTypeTasks)')
                ->setParameter('longRunningTypeTasks', $this->slotManagerService->getTaskLongRunningTypes());
        }

        $task = $qb->getQuery()->getOneOrNullResult();

        if (null === $task) {
            throw new TaskNotFoundException('No pending tasks available');
        }

        return $task;
    }

    public function isLongRunningTask(Task $task): bool
    {
        return $this->slotManagerService->isLongRunningTask($task);
    }
}
