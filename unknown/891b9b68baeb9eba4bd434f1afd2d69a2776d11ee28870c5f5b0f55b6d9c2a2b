<?php

namespace App\Entity;

use App\Repository\LibraryRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Gedmo;
use <PERSON>ymfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass=LibraryRepository::class)
 * @Gedmo\SoftDeleteable(fieldName="deletedAt", timeAware=false, hardDelete=false)
 * @ORM\HasLifecycleCallbacks
 */
class Library
{
    public const TYPE_FILE = 'PDF';
    public const TYPE_VIDEO = 'VIDEO';
    public const TYPE_AUDIO = 'AUDIO';
    public const TYPE_EXTERN_RESOURCE = "URL";

    public const SUBTYPE_FILE = 1;
    public const SUBTYPE_URL = 2;

    public const SUBTYPE_URL_NAME = "URL";

    use AtAndBy;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups("library")
     */
    private ?int $id;

    /**
     * @ORM\Column(type="string", length=100)
     * @Groups("library")
     */
    private ?string $name;

    /**
     * @ORM\Column(type="text", nullable=true)
     * @Groups("library")
     */
    private ?string $description;

    /**
     * @ORM\Column(type="string", length=10)
     * @Groups("library")
     */
    private ?string $type;

    /**
     * @ORM\Column(type="boolean")
     * @Groups("library")
     */
    private bool $enableRating = false;

    /**
     * @ORM\Column(type="boolean")
     * @Groups("library")
     */
    private bool $enableComments = false;

    /**
     * @ORM\ManyToOne(targetEntity=LibraryCategory::class, inversedBy="libraries")
     * @ORM\JoinColumn(nullable=false)
     * @Groups("library")
     */
    private ?LibraryCategory $category;

    /**
     * @ORM\ManyToMany(targetEntity=Filter::class, inversedBy="libraries")
     */
    private $filters;

    /**
     * @ORM\OneToMany(targetEntity=LibraryComment::class, mappedBy="library", orphanRemoval=true)
     */
    private $libraryComments;

    /**
     * @ORM\OneToOne(targetEntity=LibraryFile::class, mappedBy="library", cascade={"persist", "remove"})
     * @Groups("library")
     */
    private $libraryFile;

    /**
     * @ORM\OneToOne(targetEntity=LibraryVideo::class, mappedBy="library", cascade={"persist", "remove"})
     * @Groups("library")
     */
    private $libraryVideo;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Groups("library")
     */
    private $thumbnail;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Groups("library")
     */
    private $url;

    public function __construct()
    {
        $this->filters = new ArrayCollection();
        $this->libraryComments = new ArrayCollection();
        $this->libraryViews = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function isEnableRating(): ?bool
    {
        return $this->enableRating;
    }

    public function setEnableRating(bool $enableRating): self
    {
        $this->enableRating = $enableRating;

        return $this;
    }

    public function isEnableComments(): ?bool
    {
        return $this->enableComments;
    }

    public function setEnableComments(bool $enableComments): self
    {
        $this->enableComments = $enableComments;

        return $this;
    }

    public function getCategory(): ?LibraryCategory
    {
        return $this->category;
    }

    public function setCategory(?LibraryCategory $category): self
    {
        $this->category = $category;

        return $this;
    }

    /**
     * @param Filter[] $filters
     */
    public function setFilters(array $filters): self
    {
        $oldFilters = $this->getFilters();
        foreach ($oldFilters as $old) {
            if (!in_array($old, $filters)) {
                $this->removeFilter($old);
            }
        }
        foreach ($filters as $filter) $this->addFilter($filter);
        return $this;
    }

    /**
     * @return Collection<int, Filter>
     */
    public function getFilters(): Collection
    {
        return $this->filters;
    }

    public function addFilter(Filter $filter): self
    {
        if (!$this->filters->contains($filter)) {
            $this->filters[] = $filter;
        }

        return $this;
    }

    public function removeFilter(Filter $filter): self
    {
        $this->filters->removeElement($filter);

        return $this;
    }

    /**
     * @return Collection<int, LibraryComment>
     */
    public function getLibraryComments(): Collection
    {
        return $this->libraryComments;
    }

    public function addLibraryComment(LibraryComment $libraryComment): self
    {
        if (!$this->libraryComments->contains($libraryComment)) {
            $this->libraryComments[] = $libraryComment;
            $libraryComment->setLibrary($this);
        }

        return $this;
    }

    public function removeLibraryComment(LibraryComment $libraryComment): self
    {
        if ($this->libraryComments->removeElement($libraryComment)) {
            // set the owning side to null (unless already changed)
            if ($libraryComment->getLibrary() === $this) {
                $libraryComment->setLibrary(null);
            }
        }

        return $this;
    }

    public function getLibraryFile(): ?LibraryFile
    {
        return $this->libraryFile;
    }

    public function setLibraryFile(LibraryFile $libraryFile): self
    {
        // set the owning side of the relation if necessary
        if ($libraryFile->getLibrary() !== $this) {
            $libraryFile->setLibrary($this);
        }

        $this->libraryFile = $libraryFile;

        return $this;
    }

    public function getLibraryVideo(): ?LibraryVideo
    {
        return $this->libraryVideo;
    }

    public function setLibraryVideo(LibraryVideo $libraryVideo): self
    {
        // set the owning side of the relation if necessary
        if ($libraryVideo->getLibrary() !== $this) {
            $libraryVideo->setLibrary($this);
        }

        $this->libraryVideo = $libraryVideo;

        return $this;
    }

    public function getThumbnail(): ?string
    {
        return $this->thumbnail;
    }

    public function setThumbnail(?string $thumbnail): self
    {
        $this->thumbnail = $thumbnail;

        return $this;
    }

    public function __toString(){
        return $this->name;
    }

    public function getUrl(): ?string
    {
        return $this->url;
    }

    public function setUrl(?string $url): self
    {
        $this->url = $url;

        return $this;
    }

    /**
     * @ORM\Column(type="string", length=4)
     */
    private ?string $locale;

    /**
     * @Groups("library")
     * @return int
     */
    public function getRating(): int {
        $totalElements = 0;
        $sumRating = 0;
        /** @var LibraryComment $comment */
        foreach ($this->libraryComments as $comment) {
            $sumRating += $comment->getRating();
            $totalElements++;
        }
        return $totalElements > 0 ? $sumRating / $totalElements : 0;
    }

    public function getLocale(): ?string
    {
        return $this->locale;
    }

    public function setLocale(string $locale): self
    {
        $this->locale = $locale;

        return $this;
    }

    /**
     * @ORM\OneToMany(targetEntity=LibraryViews::class, mappedBy="library", orphanRemoval=true)
     */
    private $libraryViews;

    /**
     * @Groups("library")
     * @return int
     */
    public function getTotalComments(): int
    {
        return $this->libraryComments->count();
    }

    /**
     * @return Collection<int, LibraryViews>
     */
    public function getLibraryViews(): Collection
    {
        return $this->libraryViews;
    }

    public function addLibraryView(LibraryViews $libraryView): self
    {
        if (!$this->libraryViews->contains($libraryView)) {
            $this->libraryViews[] = $libraryView;
            $libraryView->setLibrary($this);
        }

        return $this;
    }

    public function removeLibraryView(LibraryViews $libraryView): self
    {
        if ($this->libraryViews->removeElement($libraryView)) {
            // set the owning side to null (unless already changed)
            if ($libraryView->getLibrary() === $this) {
                $libraryView->setLibrary(null);
            }
        }

        return $this;
    }

    /**
     * @Groups("library")
     * @return int
     */
    public function getTotalViews() {
        return $this->libraryViews->count();
    }

    /**
     * @Groups("library")
     * @var string|null
     */
    private ?string $createdAtText;

    /**
     * @param string|null $createdAtText
     * @return Library
     */
    public function setCreatedAtText(?string $createdAtText): self
    {
        $this->createdAtText = $createdAtText;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getCreatedAtText(): ?string
    {
        return $this->createdAtText;
    }
}
