<template>
  <div class="copyInput">
    <div class="inputContainer">
      <p class="label text-black">{{ label }}</p>
      <p class="inputValue cursor-pointer" @click="copyOnClipboard">{{ inputValue }}</p>
    </div>
    <div class="d-flex justify-content-center align-items-center">
      <i class="fa fa-copy cursor-pointer" @click="copyOnClipboard"></i>
    </div>
  </div>
</template>

<script>
export default {
  name: "copyInput",
  props: {
    label: {
      type: String,
      default: ''
    },
    input : {
      type: String,
      default: ''
    },
    maskInput: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    inputValue() {
      return this.maskInput ? this.input.replace(/./g, '*') : this.input;
    }
  },
  methods: {
    copyOnClipboard() {
      if ((this.input || '').length) {
        this.$toast.clear();
        this.$toast.info('Texto copiado en portapapeles');
        navigator.clipboard.writeText(this.input);
      }
    }
  }
}
</script>

 <style scoped lang="scss"> 
.copyInput {
  display: grid;
  grid-template-columns: auto 3rem;
  position: relative;

  .label {
    font-size: 1.1rem;
    margin: 0 auto;
    text-align: center;
  }
  .inputValue {
    display: block;
    font-size: 1rem;
    white-space: nowrap;
    border: solid;
    border-width: 0 0 1px;
    min-height: 1.5rem;
  }
  .inputContainer {
    overflow: hidden !important;
  }
}
</style>
