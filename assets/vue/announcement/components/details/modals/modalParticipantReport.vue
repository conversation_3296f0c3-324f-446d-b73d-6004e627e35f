<template>
    <div class="modalParticipantReport">
        <BaseModalInspector
            :identifier="'modalParticipantReport'"
            :title="$t('ANNOUNCEMENT.PARTICIPANT-REPORTS')"
            padding="1rem"
            size="modal-lg"
        >
            <div class="form-group">
                <label for="startDate">{{ $t("FILTER.START_AT") }}</label>
                <input type="date" id="startDate" v-model="startDate" class="form-control" />
            </div>
            <div class="form-group">
                <label for="endDate">{{ $t("FILTER.FINISH_AT") }}</label>
                <input type="date" id="endDate" v-model="endDate" class="form-control" />
            </div>
            <div class="form-group">
                <label for="status">{{ $t("ANNOUNCEMENT.STATUS.NAME") }}</label>
                <select id="status" v-model="selectedStatus" class="form-control">
                    <option :value="null">{{ $t("ANNOUNCEMENT.STATUS.ALL") }}</option>
                    <option v-for="status in statusOptions" :key="status" :value="status">
                        {{ $t(`ANNOUNCEMENT.STATUS.${status}`) }}
                    </option>
                    <option :value="'OTHERS'">{{ $t("ANNOUNCEMENT.STATUS.OTHERS") }}</option>
                </select>
            </div>
            <div v-for="option in extraOptions" :key="option.id">
                <label :for="option.id">{{ option.name }}</label>
                <select :id="option.id" v-model="selectedExtras[option.id]" class="form-control">
                    <option :value="null">{{ $t("ANNOUNCEMENT.EXTRA.ALL") }}</option>
                    <option v-for="(value, index) in option.options" :key="index">{{ value }}</option>
                </select>
            </div>
            
            
            <div class="text-center mt-4">
                <button type="button" class="btn btn-secondary" @click="submit" data-bs-toggle="modal" data-bs-target="#modalParticipantReport">
                    <i class="fa fa-file mr-2"></i>{{ $t("GENERATE_REPORT_AND_CLOSE") }}
                </button>
                <button type="button" class="btn btn-primary" @click="submit">
                    <i class="fa fa-file mr-2"></i>{{ $t("GENERATE_REPORT") }}
                </button>
            </div>
        </BaseModalInspector>
    </div>
</template>

<script>
    import BaseModalInspector from '/assets/vue/inspectorFundae/components/BaseModalInspector.vue';
    import axios from 'axios';

    export default {
        components: { BaseModalInspector },

        data() {
            return {
                startDate: '',
                endDate: '',
                selectedStatus: null,
            };
        },
        props: {
            statusOptions: {
                type: Array,
                required: true,
                default: () => [],
            },
            extraOptions: {
                type: Array,
                required: true,
                default: () => [],
            },
            selectedExtras: {
                type: Object,
                required: true,
                default: () => ({}),
            },
        },
        methods: {
            submit() {
                const payload = {
                    startDate: this.startDate || null,
                    endDate: this.endDate || null,
                    status: this.selectedStatus,
                    extras: this.selectedExtras,
                };

                this.$emit('download', payload);
            },
        },
    };
</script>

<style>
    #staticBackdropLabel.modal-title {
        color: white;
    }

    .btn-close[data-modal-id="#modalParticipantReport"] {
        filter: invert(1) grayscale(100%) brightness(200%);
    }
</style>
