<script>
import {sync, get } from "vuex-pathify";
import Spinner from "../../admin/components/base/Spinner.vue";

export default {
  name: "UserFieldsFundaeModal",
  components: {Spinner},
  data() {
    return {
      loading: false,
      user: {
        socialSecurityNumber: '',
        gender: 'M',
        emailWork: '',
        birthdate: '',
        dni: '',
        contributionAccount: '',
        userCompany: null,
        userProfessionalCategory: null,
        userWorkCenter: null,
        userWorkDepartment: null,
        userStudyLevel: null,
      },
    }
  },
  computed: {
    selectedUserProfile: sync("announcementModule/selectedUserProfile"),
    companies: get("announcementModule/fundaeCatalogs@companies"),
    professionalCategories: get("announcementModule/fundaeCatalogs@professionalCategories"),
    workCenters: get("announcementModule/fundaeCatalogs@workCenters"),
    workDepartments: get("announcementModule/fundaeCatalogs@workDepartments"),
    studyLevels: get("announcementModule/fundaeCatalogs@studyLevels"),
  },
  watch: {
    selectedUserProfile: {
      deep: true,
      immediate: true,
      handler: function (val, oldVal) {
        if (val != null && val !== oldVal) {
          this.loadPreData();
        }
      }
    }
  },
  mounted() {

  },
  methods: {
    loadPreData() {
      this.loading = true;
      this.$store.dispatch('announcementModule/getUserFieldsFundae', this.selectedUserProfile.id).then(r => {
        const { data, error } = r;
        if (!error) this.user = data;
      }).finally(() => {
        this.loading = false;
      })
    },
    submit() {
      const formData = new FormData(document.forms['UserFieldsFundaeModal-fill-form']);

      this.$store.dispatch('announcementModule/saveUserFieldsFundae', {
        userId: this.selectedUserProfile.id,
        formData
      }).then(r => {
        const { error } = r;
        if (error) this.$toast.error('Failed to save');
        else {
          this.$toast.success(this.$t('CATALOG.SAVED'));
          this.selectedUserProfile = null;
          document.getElementById('UserFieldsFundaeModal-fill_close').click();
        }
      })
    }
  }
}
</script>

<template>
  <div class="UserFieldsFundaeModal">
    <BaseModal identifier="UserFieldsFundaeModal-fill" size="modal-lg" padding="2rem" :title="$t('USER.USER_FIELDS_FUNDAE.TITLE') + ''">
      <template v-slot:default>
        <div v-if="loading" class="d-flex align-items-center justify-content-center w-100">
          <spinner />
        </div>
        <form @submit.prevent="submit" id="UserFieldsFundaeModal-fill-form" v-else>
          <div class="form-group">
            <label>{{ $t('USER.USER_FIELDS_FUNDAE.SOCIAL_SECURITY_NUMBER') }}</label>
            <input type="text" class="form-control" name="socialSecurityNumber" id="socialSecurityNumber" v-model="user.socialSecurityNumber">
          </div>

          <div class="form-group">
            <label>{{ $t('USER.USER_FIELDS_FUNDAE.USER.GENDER') }}</label>
            <select id="gender" name="gender"
                    class="form-select" aria-label="Default select example"
                    v-model="user.gender">
              <option value="M">{{ $t('GENDER.M') }}</option>
              <option value="F">{{ $t('GENDER.F') }}</option>
            </select>
          </div>
          <div class="form-group">
            <label>{{ $t('USER.USER_FIELDS_FUNDAE.EMAIL_WORK') }}</label>
            <input type="email" class="form-control"
                   name="emailWork" id="emailWork"
                   v-model="user.emailWork">
          </div>
          <div class="form-group">
            <label>{{ $t('BIRTHDATE') }}</label>
            <input type="date" class="form-control"
                   name="birthdate" id="birthdate"
                   v-model="user.birthdate">
          </div>
          <div class="form-group">
            <label>{{ $t('USER.USER_FIELDS_FUNDAE.DNI') }}</label>
            <input type="text" class="form-control" name="dni" id="dni" v-model="user.dni">
          </div>
          <div class="form-group">
            <label>{{ $t('USER.USER_FIELDS_FUNDAE.CONTRIBUTION_ACCOUNT') }}</label>
            <input type="text" class="form-control" name="contributionAccount" id="contributionAccount" v-model="user.contributionAccount">
          </div>
          <div class="form-group">
            <label>{{ $t('USER.USER_FIELDS_FUNDAE.USER_COMPANY') }}</label>
            <select id="userCompany" name="userCompany"
                    class="form-select" aria-label="Default select example"
                    v-model="user.userCompany">
              <option v-for="c in companies" :value="c.id">{{ c.name }}</option>
            </select>
          </div>
          <div class="form-group">
            <label>{{ $t('USER.USER_FIELDS_FUNDAE.PROFESSIONAL_CATEGORY') }}</label>
            <select id="userProfessionalCategory" name="userProfessionalCategory"
                    class="form-select" aria-label="Default select example"
                    v-model="user.userProfessionalCategory">
              <option v-for="p in professionalCategories" :value="p.id">{{ p.name }}</option>
            </select>
          </div>
          <div class="form-group">
            <label>{{ $t('USER.USER_FIELDS_FUNDAE.WORK_CENTER') }}</label>
            <select id="userWorkCenter" name="userWorkCenter"
                    class="form-select" aria-label="Default select example"
                    v-model="user.userWorkCenter">
              <option v-for="wc in workCenters" :value="wc.id">{{ wc.name }}</option>
            </select>
          </div>
          <div class="form-group">
            <label>{{ $t('USER.USER_FIELDS_FUNDAE.WORK_DEPARTMENT') }}</label>
            <select id="userWorkDepartment" name="userWorkDepartment"
                    class="form-select" aria-label="Default select example"
                    v-model="user.userWorkDepartment">
              <option v-for="wd in workDepartments" :value="wd.id">{{ wd.name }}</option>
            </select>
          </div>
          <div class="form-group">
            <label>{{ $t('USER.USER_FIELDS_FUNDAE.STUDY_LEVEL') }}</label>
            <select id="userStudyLevel" name="userStudyLevel"
                    class="form-select" aria-label="Default select example"
                    v-model="user.userStudyLevel">
              <option v-for="sl in studyLevels" :value="sl.id">{{ sl.name }}</option>
            </select>
          </div>
          <div class="col-12 d-flex">
            <button type="submit" class="ml-auto btn btn-primary">{{ $t('SAVE') }}</button>
          </div>
        </form>
      </template>
    </BaseModal>
  </div>
</template>

<style scoped lang="scss">

</style>
