<template>
  <div class="modalMeetings">
    <BaseModal
      :identifier="tag + 'meetings'"
      :title="$t('VIDEO_PROVIDER.CREATE_SPACE')"
      modal-icon="fa-video-camera"
      padding="2rem"
      size="modal-md"
    >
      <p>{{ $t('VIDEO_PROVIDER.SELECT_VIRTUAL_CLASSROOM_PROVIDER') }}</p>
      <div v-if="loading" class="w-100 d-flex justify-content-center align-items-center">
        <spinner />
      </div>
      <div v-else class="buttonContainer d-flex flex-column align-items-center">
        <button v-for="provider in providers" :key="provider.id"
                type="button"
                :style="{ backgroundImage: 'url(' + provider.logo + ')' }"
                class="btn-meetings" :class="`provider-${provider.id}`"
                @click="$emit('input', provider.id)"></button>
      </div>
    </BaseModal>
  </div>
</template>

<script>

import Spinner from "../../../admin/components/base/Spinner.vue";
export default {
  name: "modalMeetings",
  components: {Spinner},
  props: {
    tag: { type: String, default: "" },
    providers: { type: Object|Array, default: () => ([])},
    loading: { type: Boolean, default: false}
  },
  methods: {
    getLink(index) {
    }
  }
};
</script>

 <style scoped lang="scss"> 
.modalMeetings {
  .userAvatar {
    width: 5rem;
    height: 5rem;
  }

  .container {
    padding: 1rem;
    border-radius: 7px;
    background-color: white;
  }

  .alertCounter {
    span {
      font-size: 0.8rem;
      vertical-align: super;
      font-weight: bold;
    }
  }

  ::v-deep {
    .modal-body {
      background-color: var(--color-neutral-lighter);
    }

    .modal-header {
      background-color: var(--color-neutral-darker);
      align-items: center;

      .modal-title {
        color: white;
      }
      .btn-close {
        filter: invert(1) grayscale(100%) brightness(200%);;
      }
    }
  }

  .buttonContainer {
    gap: 0.75rem;
  }

  .btn-meetings {
    height: 5rem;
    width: 15rem;
    border-radius: 0.5rem;
    border: 1px solid var(--color-neutral-mid-dark);
    background-color: white;
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;

 
    &:hover {
      background-color: var(--color-primary-light);
    }
  }
}
</style>
