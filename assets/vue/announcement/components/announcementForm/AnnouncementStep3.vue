<template>
  <div class="AnnouncementStep3 d-flex flex-row flex-wrap">
    <div class="form-group col-md-4">
      <label for="maxUsers">{{ $t('ANNOUNCEMENT.MAX_USERS') }}</label>
      <input name="maxUsers" id="maxUsers" type="number" class="form-control" min="0" v-model.number="data.maxUsers">
    </div>

    <div class="form-group col-md-4">
      <label for="formativeActionType">{{ $t('ANNOUNCEMENT.SELECTED_FORMATIVE_ACTION_TYPE') }}</label>
      <select name="formativeActionType" id="formativeActionType" class="custom-select"
              v-model="data.formativeActionType">
        <option value="">{{ $t("INPUT.PLACEHOLDER.ACTION_FORMATIVE") }}</option>
        <option
            v-for="(formative, index) in formativeActionType"
            :value="formative"
            :key="formative"
        >
          {{ $t('ANNOUNCEMENT.FORMATIVE_ACTION_TYPE.' + index) }}
        </option>
      </select>
    </div>

    <div class="form-group col-md-4">
      <label for="format">{{ $t('ANNOUNCEMENT.SELECTED_FORMAT') }}</label>
      <select name="format" id="format" class="custom-select" v-model="data.format">
        <option value="">{{ $t('ANNOUNCEMENT.PLACEHOLDER.SELECT_FORMAT') }}</option>
        <option v-for="(form, index) in format" :value="form" :key="form" >
          {{ $t('ANNOUNCEMENT.FORMAT.' + index) }}  </option>
      </select>
    </div>

    <div class="form-group col-md-4">
      <label for="totalHours">{{ $t('ANNOUNCEMENT.TOTAL_HOURS') }}</label>
      <input name="totalHours" id="totalHours" type="number" class="form-control" min="0" v-model.number="data.totalHours">
    </div>

    <div class="form-group col-md-4">
      <label for="place">{{ $t('ANNOUNCEMENT.PLACE') }}</label>
      <input name="place" id="place" type="text" class="form-control" v-model="data.place" :placeholder="$t('ANNOUNCEMENT.PLACEHOLDER.PLACE')">
    </div>

    <div class="form-group col-md-4">
      <label for="trainingCenter">{{ $t('ANNOUNCEMENT.TRAINING_CENTER') }}</label>
      <input name="trainingCenter" id="trainingCenter" type="text" class="form-control" v-model="data.trainingCenter" :placeholder="$t('ANNOUNCEMENT.PLACEHOLDER.TRAINING_CENTER')">
    </div>

    <div class="form-group col-md-4">
      <label for="trainingCenterAddress">{{ $t('ANNOUNCEMENT.TRAINING_CENTER_ADDRESS') }}</label>
      <input name="trainingCenterAddress" id="trainingCenterAddress" type="text" class="form-control" v-model="data.trainingCenterAddress"
             :placeholder="$t('ANNOUNCEMENT.PLACEHOLDER.TRAINING_CENTER_ADDRESS')">
    </div>

    <div class="form-group col-md-4">
      <label for="trainingCenterNif">{{ $t('ANNOUNCEMENT.TRAINING_CENTER_NIF') }}</label>
      <input name="trainingCenterNif" id="trainingCenterNif" type="text" class="form-control" v-model="data.trainingCenterNif"
             :placeholder="$t('ANNOUNCEMENT.PLACEHOLDER.TRAINING_CENTER_NIF')">
    </div>

    <div class="form-group col-md-4">
      <label for="trainingCenterPhone">{{ $t('ANNOUNCEMENT.TRAINING_CENTER_PHONE') }}</label>
      <input name="trainingCenterPhone" id="trainingCenterPhone" type="text" class="form-control" v-model="data.trainingCenterPhone"
             :placeholder="$t('ANNOUNCEMENT.PLACEHOLDER.TRAINING_CENTER_PHONE')">
    </div>

    <div class="form-group col-md-4">
      <label for="trainingCenterEmail">{{ $t('ANNOUNCEMENT.TRAINING_CENTER_EMAIL') }}</label>
      <input name="trainingCenterEmail" id="trainingCenterEmail" type="email" class="form-control" v-model="data.trainingCenterEmail"
             :placeholder="$t('ANNOUNCEMENT.PLACEHOLDER.TRAINING_CENTER_EMAIL')">
    </div>

    <div class="form-group col-md-4">
      <label for="trainingCenterTeacherDni">{{ $t('ANNOUNCEMENT.TRAINING_CENTER_TEACHER_DNI') }}</label>
      <input name="trainingCenterTeacherDni" id="trainingCenterTeacherDni" type="text" class="form-control" v-model="data.trainingCenterTeacherDni"
             :placeholder="$t('ANNOUNCEMENT.PLACEHOLDER.TRAINING_CENTER_TEACHER_DNI')">
    </div>
  </div>
</template>

<script>
export default {
  name: "AnnouncementStep3",
  props: {
    announcement: null,
    formativeActionType: {
      type: Array|Object,
      default: []
    },
    format: {
      type: Array|Object,
      default: []
    },
  },
  data() {
    return {
      initialized: false,
      data: {
        maxUsers: this.announcement?.maxUsers ?? 0,
        formativeActionType: this.announcement?.formativeActionType ?? '',
        format: this.announcement?.format ?? '',
        totalHours: this.announcement?.totalHours ?? 0,
        place: this.announcement?.place ?? '',
        trainingCenter: this.announcement?.trainingCenter ?? '',
        trainingCenterAddress: this.announcement?.trainingCenterAddress ?? '',
        trainingCenterNif: this.announcement?.trainingCenterNif ?? '',
        trainingCenterPhone: this.announcement?.trainingCenterPhone ?? '',
        trainingCenterEmail: this.announcement?.trainingCenterEmail ?? '',
        trainingCenterTeacherDni: this.announcement?.trainingCenterTeacherDni ?? ''
      }
    };
  },
  watch: {
    data: {
      handler: function (old, newVal) {
        this.$emit('updated', this.data);
      },
      deep: true
    }
  }
}
</script>

 <style scoped lang="scss"> 

</style>
