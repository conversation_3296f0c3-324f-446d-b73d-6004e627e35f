<script>
import ButtonWithDescription from "../../../common/components/ButtonWithDescription.vue";
import { get, sync } from "vuex-pathify";
import { configurationClientAnnouncement } from "../../mixins/configurationClientAnnouncement";
import LabelWithInfo from "../../../common/components/ui/LabelWithInfo.vue";

export default {
  name: "AnnouncementCertificate",
  components: { LabelWithInfo, ButtonWithDescription },
  mixins: [configurationClientAnnouncement],
  computed: {
    type: get("announcementFormModule/announcement@type"),// Announcement type
    stepsConfigurations: get("announcementFormModule/stepsConfigurations"),// All steps configurations
    stepInfo() {
      return this.stepsConfigurations[this.type].steps.find(s => s.type === 'AnnouncementCertificate') ?? {};
    },
    announcementConfigurationTypes() {
      return this.stepInfo?.configurations ?? [];
    },

    typeDiplomas: get("announcementFormModule/typeDiplomas"),
    configAnnouncement: sync(
      "announcementFormModule/announcement@configAnnouncement"
    ),

    typeDiploma: sync("announcementFormModule/announcement@typeDiploma"),
    isConfirmationRequiredDiploma: sync(
      "announcementFormModule/announcement@isConfirmationRequiredDiploma"
    ),

    previewCertificate() {
      let typeDiploma = this.typeDiplomas.find(
        (typeDiploma) => typeDiploma.id === this.typeDiploma
      );

      if (!typeDiploma) typeDiploma = this.typeDiplomas[0];
      let validJsonString = typeDiploma?.extra.replace(/'/g, '"');
      let parsedJson = JSON.parse(validJsonString);
      let previewCertificate = parsedJson?.previewDiploma;

      return previewCertificate;
    },
    isDiplomaEnabled() {
      return this.configAnnouncement[`configuration-7`]
    }
  },
};
</script>

<template>
  <div class="AnnouncementCertificate">
    <div class="AnnouncementCertificate--info">
      <button-with-description
        v-for="config in announcementConfigurationTypes"
        :key="config.id"
        :name="`configuration-${config.id}`"
        :title="config.name"
        :description="config.description"
        :image-url="config.image"
        v-model="configAnnouncement[`configuration-${config.id}`]"
        v-show="config.id === 7"
      />

      <button-with-description
        v-for="config in announcementConfigurationTypes"
        :key="config.id + 'certificate'"
        :name="`configuration-${config.id}`"
        :title="config.name"
        :description="config.description"
        :image-url="config.image"
        v-model="configAnnouncement[`configuration-${config.id}`]"
        size="s"
        v-show="
          configAnnouncement['configuration-7'] === true && config.id !== 7
        "
      />

      <div class="form-group info-icon mt-3 mb-0" v-show="isDiplomaEnabled">
        <label-with-info
          :info="$t('ANNOUNCEMENT.FORM.ENTITY.CERTIFICATE_SELECT_INFO') + ''"
        >
          {{ $t("ANNOUNCEMENT.FORM.ENTITY.CERTIFICATE_SELECT") }}
        </label-with-info>
        <select class="form-select" v-model="typeDiploma">
          <option
            v-for="typeDipl in typeDiplomas"
            :key="typeDipl.id"
            :value="typeDipl.id"
          >
            {{ typeDipl.name }}
          </option>
        </select>
      </div>

      <div
        class="form-check form-switch mt-1 mb-0"
        v-if="type === 'on_site' || type === 'virtual_classroom'"
      >
        <!--  <input
          class="form-check-input"
          type="checkbox"
          id="flexSwitchCheckChecked1"
          v-model="isConfirmationRequiredDiploma"
        />
        <label class="form-check-label" for="flexSwitchCheckChecked1"
          >Es necesaria confirmación del tutor o tutora para su obtencion</label
        > -->
      </div>
    </div>
    <div class="AnnouncementCertificate--certificate" v-show="isDiplomaEnabled">
      <p>Vista previa del diploma</p>
      <embed
        :src="`${previewCertificate}#toolbar=0&zoom=50`"
        width="100%"
        height="100%"
        type="application/pdf"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.AnnouncementCertificate {
  display: grid;
  padding: 1rem 3rem;
  gap: 3rem;

  @media #{min-medium-screen()} {
    grid-template-columns: repeat(2, 1fr);
  }

  &--info {
    display: flex;
    flex-flow: column;
  }

  &--certificate {
    display: flex;
    flex-flow: column;
    min-height: 30rem;
  }
}
</style>
