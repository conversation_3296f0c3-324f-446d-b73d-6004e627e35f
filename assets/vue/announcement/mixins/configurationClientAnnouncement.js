import { get, sync } from 'vuex-pathify';

export const configurationClientAnnouncement = {
  computed: {
    clientConfigurationAnnouncement: get('announcementFormModule/clientConfigurationAnnouncement'),
    configAnnouncement: get('announcementFormModule/announcement@configAnnouncement'),
  },

  methods: {
    fetchConfigurationByComponent(configurations) {
      return this.clientConfigurationAnnouncement
        ?.filter((config) => configurations.some((conf) => conf.type === config.type))
        .sort(
          (a, b) =>
            configurations.findIndex((conf) => conf.type === a.type) -
            configurations.findIndex((conf) => conf.type === b.type),
        )
        .map((config) => ({
          ...config,
          vModelName: 'configuration-' + config.id,
        }));
    },

    handleConfigChange(configId) {
      // const configIds = this.configAnnouncement.includes(configId)
      //   ? this.configAnnouncement.filter((id) => id !== configId)
      //   : [...this.configAnnouncement, configId];
      // const configAnnouncement = structuredClone(this.configAnnouncement);
      // configAnnouncement[`configuration-${configId}`] = !configAnnouncement[`configuration-${configId}`];
      //
      // this.$store.dispatch('announcementFormModule/setConfigAnnouncement', configAnnouncement);
      //
      // this.searchEnableChapterTiming();
    },

    searchEnableChapterTiming() {
      const temporalizationClient = this.configurationsClientInThisPage.find(
        (config) => config.type === 'TEMPORALIZATION',
      );

      const temporalizationInConfig = this.configAnnouncement.find(
        (config) => config === temporalizationClient?.id,
      );

      this.enableChapterTiming = !!temporalizationInConfig;
    },
  },
};
