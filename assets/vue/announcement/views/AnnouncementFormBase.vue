<script>
import StepForm from "../../common/views/StepForm.vue";
import Spinner from "../../admin/components/base/Spinner.vue";
import Loader from "../../admin/components/Loader.vue";
import {get, sync} from "vuex-pathify";
import AnnouncementGeneralInfoExtern from "../components/announcementForm/AnnouncementGeneralInfoExtern.vue";
import AnnouncementAlerts from "../components/announcementForm/AnnouncementAlerts.vue";
import AnnouncementSurvey from "../components/announcementForm/AnnouncementSurvey.vue";
import AnnouncementCertificate from "../components/announcementForm/AnnouncementCertificate.vue";
import AnnouncementCommunication from "../components/announcementForm/AnnouncementCommunication.vue";
import AnnouncementBonus from "../components/announcementForm/AnnouncementBonus.vue";
import AnnouncementPlace from "../components/announcementForm/AnnouncementPlace.vue";
import AnnouncementGroups from "../components/announcementForm/AnnouncementGroups.vue";
import AnnouncementStudents from "../components/announcementForm/AnnouncementStudents.vue";
import AnnouncementGeneralInfo from "../components/announcementForm/AnnouncementGeneralInfo.vue";
import AnnouncementCourse from "../components/announcementForm/AnnouncementCourse.vue";

export default {
  name: "AnnouncementFormBase",
  components: {Loader, Spinner, StepForm,
    AnnouncementGeneralInfoExtern,
    AnnouncementAlerts,
    AnnouncementSurvey,
    AnnouncementCertificate,
    AnnouncementCommunication,
    AnnouncementBonus,
    AnnouncementPlace,
    AnnouncementGroups,
    AnnouncementStudents,
    AnnouncementGeneralInfo,
    AnnouncementCourse},
  data() {
    return {
      warningSteps: {},
      displayErrors: [],
      errorsExtra: [],
      prevStep: null
    };
  },
  computed: {
    loading: get("announcementFormModule/loading"),
    announcement: sync("announcementFormModule/announcement"), // Main announcement object
    totalSteps: sync("announcementFormModule/steps@total"),
    currentStep: sync("announcementFormModule/steps@current"),
    errors: sync("announcementFormModule/errors"),
    type: get("announcementFormModule/announcement@type"),
    stepsConfigurations: get("announcementFormModule/stepsConfigurations"),
    stepsInformationValidation: sync("announcementFormModule/stepsInformation"),
    stepsInformation() {
      const steps = this.stepsConfigurations[this.type] ?? {
        titles: new Map(),
        steps: []
      };
      this.stepsInformationValidation = steps;
      this.totalSteps = steps.steps.length;
      return steps;
    },
    saving: get("announcementFormModule/saving"),
    course: get("announcementFormModule/announcement@course"),
    isConfigurable: get("announcementFormModule/isConfigurable"),
    stepInfo() {
      const availableSteps = Array.from(this.stepsInformation.titles.keys()).map((key) => ({
        id: key,
        title: this.stepsInformation.titles.get(key)
      }))
      return {
        available: availableSteps,
        current: availableSteps.find((step) => step.id === this.currentStep),
        prev: availableSteps.find((step) => step.id === this.prevStep)
      }
    },
  },

  watch: {
    errors: {
      deep: true,
      immediate: true,
      handler: function () {
        this.checkErrorStatus();
      },
    },

    currentStep(_, oldVal) {
      this.prevStep = oldVal
      this.checkErrorStatus();
    }
  },
  mounted() {
    this.prevStep = null
  },
  methods: {
    checkErrorStatus() {
      this.displayErrors = [];
      this.errorsExtra = [];
      const errors = structuredClone(this.errors);
      const keys = Object.keys(errors);

      if (keys.length === 0) return;
      let errorList = []
      const warningSteps = {};
      keys.forEach(key => {
        const stepInfo = this.stepsInformation.steps.find(step => step.type === key);
        const error = errors[key];

        if (stepInfo && stepInfo.step === this.currentStep && error.error && error.message && error.message.length > 0) {
          this.$toast.clear();
          if (error.i18n) this.$toast.error(this.$t(error.message) + "");
          else this.$toast.error(error.message);
          delete errors[key];
        }

        if (error.data) {
          if ('request' in error) {
            if (typeof error.data === 'string' || error.data instanceof String) errorList.push(error.data);
            else {
              error.data.forEach(e => errorList.push(e))
            }
          }
          else {
            if ('i18n' in error.data) {
              error.data.i18n.forEach(e => errorList.push(e));
              delete error.data.i18n;
            }

            if ('errorExtra' in error.data) {
              this.errorsExtra = error.data.errorExtra;
            }

            const ids = Object.keys(error.data);
            ids.forEach(id => {
              if (error.data[id]) {
                const key = (id.split('-')[1] || '').toUpperCase();
                if (this.$te(`ANNOUNCEMENT.ERROR.${key}`))
                  errorList.push(`ANNOUNCEMENT.ERROR.${key}`);
              }
            });
          }
        }
        if (stepInfo) {
          warningSteps[stepInfo.step] = error.error;
        }
      });
      
      errorList.forEach(e => this.$toast.error(`${this.$t(e)}`))
      this.warningSteps = warningSteps;
    },
    async next() {
      this.errorsExtra = [];
      await this.$store.dispatch("announcementFormModule/nextStep");
    },
    async prev() {
      this.errorsExtra = [];
      await this.$store.dispatch("announcementFormModule/prevStep");
    },
    async submit() {
      const isSuccess = await this.$store.dispatch('announcementFormModule/submitForm');
      if (isSuccess) {
        await this.$store.dispatch('contentTitleModule/removeRouteFromContentTitle', this.$route.name);
        await this.$store.dispatch('routerModule/setDeleteLastRoute', true);
        await this.$store.dispatch('announcementModule/reset');
        this.$emit('saved', this.announcement.id);
        //this.$router.replace({ name: 'ViewAnnouncement', params: { id: this.announcement.id } });
      }
    },
    async onStepChange(step) {
      this.errorsExtra = [];
      await this.$store.dispatch("announcementFormModule/onStepChange", step);
    },
  }
}
</script>

<template>
  <div class="d-flex align-items-center justify-content-center" v-if="loading">
    <spinner />
  </div>
  <div class="AnnouncementForm" v-else>
    <step-form
        id="form-announcement"
        :number-of-steps="totalSteps"
        :current-step="currentStep"
        header-prefix="ANNOUNCEMENT"
        :steps-custom-titles="stepsInformation.titles"
        @next="next"
        @prev="prev"
        @on-step-change="onStepChange"
        :warning="warningSteps"
        btn-save-text="COMPLETE"
        @submit="submit()"
        :i18n="false"
    >
      <template v-slot:form-content>
        <div>
          <div class="col-12 d-flex align-content-center justify-content-center" v-if="saving">
            <loader :is-loaded="true" style="padding: 0 !important;"/>
          </div>
          <div v-if="!isConfigurable" class="col-12 form--error">
            {{ $t('ANNOUNCEMENT.WARNING.ANNOUNCEMENT_NOT_CONFIGURABLE', [$t('ANNOUNCEMENT.STATUS.' + announcement.status )]) }}
          </div>
          <div class="col-12 form--error" v-if="displayErrors.length > 0">
            <i class="close" @click="displayErrors = [];errorsExtra=[];">&times;</i>
            <h4 v-for="(e, index) in displayErrors" :key="index">{{ e }}</h4>
          </div>
          <div class="col-12 form--error LimitedSize" v-if="errorsExtra.length > 0">
            <h4 v-for="(e, index) in errorsExtra" :key="`extra-${index}`">{{e}}</h4>
          </div>
          <div
              :class="`StepForm--${step.type}`"
              v-for="step in stepsInformation.steps"
              :key="step.step"
              v-show="currentStep === step.step"
          >
            <component :is="step.type"/>
          </div>
        </div>
      </template>
    </step-form>
  </div>
</template>

<style scoped lang="scss">
:deep(.form-group) {
  margin-bottom: 0;
  label {
    color: var(--color-neutral-darkest);
    font-size: 18px;
  }
}

:deep(textarea) {
  resize: none;
}

:deep(.modal-header) {
  background-color: #32383E;
  color: #ffffff;
  * {
    color: #ffffff;
  }
}

.form--error {
  border: 1px solid var(--color-secondary);
  background-color: var(--color-secondary-lightest);
  color: var(--color-neutral-darkest);
  padding: 1rem;
  text-align: center;
  position: relative;

  h4 {
    font-size: 16px;
  }

  .close {
    position: absolute;
    right: 5px;
    top: 0;
    cursor: pointer;
  }

  &.LimitedSize {
    max-height: 150px;
    overflow-y: auto;
  }
}

:deep(.form-group.warning) {
  label {
    position: relative;
    &:before {
      content: '!';
      position: absolute;
      top: 0;
      left: -.5rem;
      color: var(--color-secondary-light);
    }
  }
  input.form-control {
    border-color: var(--color-secondary-light);
    background-color: var(--color-secondary-lightest);
  }
  select.form-select {
    border-color: var(--color-secondary-light);
    background-color: var(--color-secondary-lightest);
  }
}

.form-group {

  &.info-icon {
    label {
      position: relative;
      i {
        margin-left: .5rem;
        color: var(--color-primary);
        margin-right: 1rem;
        font-size: 18px !important;
      }
    }
  }

  i.info-icon {
    margin-left: .5rem;
    color: var(--color-primary);
    margin-right: 1rem;
    font-size: 18px !important;
  }
}

div.errors {
  border: 1px solid var(--color-secondary);
  background-color: var(--color-secondary-lightest);
  color: var(--color-neutral-darkest);
  padding: 1rem;
  text-align: center;
}
</style>
