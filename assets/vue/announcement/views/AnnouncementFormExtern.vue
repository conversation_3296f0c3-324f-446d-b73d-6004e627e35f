<script>
  import AnnouncementFormBase from "./AnnouncementFormBase.vue";
  import {ANNOUNCEMENT_SOURCE} from "../store/module/announcementForm/common";
  
  export default {
    name: "UpdateAnnouncementExtern",
    components: {AnnouncementFormBase},
    async created() {
      const isUpdate = this.$route.name === 'UpdateAnnouncementExtern';
      let enableCourseFinder = !isUpdate;
      const params = this.$route.params;
      if (params.origin) {
        await this.$store.dispatch('contentTitleModule/addRoute', {
          routeName: 'Home',
          params: {
            linkName: this.$t('ANNOUNCEMENTS'),
            params: {}
          }
        });
        if (params.origin === 'course') {
          enableCourseFinder = false;
          await this.$store.dispatch('announcementFormModule/loadPreSelectedCourse', params.id);
        }
      }
  
      await this.$store.dispatch('contentTitleModule/addRoute', {
        routeName: this.$route.name,
        params: {
          linkName: isUpdate ? this.$t('ANNOUNCEMENT.UPDATE') : this.$t('ANNOUNCEMENT.CREATE'),
          params: this.$route.params
        }
      });

      await this.$store.dispatch('announcementFormModule/setEnableCourseFinder', enableCourseFinder);

      this.$store.dispatch('announcementFormModule/loadPreData', { id: isUpdate ? this.$route.params.id : null, source: ANNOUNCEMENT_SOURCE.EXTERN});
    },
    methods: {
      goToDetail(id) {
        this.$router.replace({ name: 'AnnouncementExternInfo', params: { id } });
      }
    }
  }
</script>
<template>
  <announcement-form-base @saved="goToDetail"/>
</template>

<style scoped lang="scss">

</style>
