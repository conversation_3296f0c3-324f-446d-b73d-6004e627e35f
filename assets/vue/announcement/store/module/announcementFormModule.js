import { make } from 'vuex-pathify';
import axios from 'axios';
import { STATUS_CONFIGURATION, STATUS_ACTIVE } from "../../mixins/constants";
import { validEmail } from "../../../common/utils/utils";
import {
  COURSE_TYPE_ONLINE,
  COURSE_TYPE_MIXED,
  COURSE_TYPE_ON_SITE,
  COURSE_TYPE_VIRTUAL_CLASSROOM,
  COURSE_TYPE_MIXED_EXTERN,
  COURSE_TYPE_ON_SITE_EXTERN,
  COURSE_TYPE_VIRTUAL_CLASSROOM_EXTERN,
} from "../../../course/mixins/constants";
// import {INTERN_COMPONENT_DEFAULT_CONFIGURATION} from "./announcementFormModuleUtils";
export const ANNOUNCEMENT_DENOMINATION_INTERN = 'intern';
export const ANNOUNCEMENT_DENOMINATION_EXTERN = 'extern';


export const EMPTY_ANNOUNCEMENT = {
  source: ANNOUNCEMENT_DENOMINATION_INTERN,

  id: null,
  status: STATUS_CONFIGURATION,
  notifiedAt: null,
  code: '',
  course: null,
  timezone: null,
  extra: null,
  startAt: null,
  finishAt: null,
  totalHours: 0,
  usersPerGroup: 0,// Value is used when
  didacticGuide: null,
  objectiveAndContent: '',
  chapterTiming: [],
  configAnnouncement: {},

  type: COURSE_TYPE_ONLINE,

  students: [],// based on context, it can be groups of students or a plain list of students

  subsidized: false,
  actionType: '',
  actionCode: '',
  denomination: '',
  contactPerson: '',
  contactPersonEmail: '',
  contactPersonTelephone: '',

  inspectorAccess: {
    user: '',
    password: '',
    url: '',
  },

  // enableCertificate: false,
  typeDiploma: null,
  isConfirmationRequiredDiploma: false,
  typeSurvey: null,

  approvedCriteriaValues: {},
  alertTypeTutorValues: {},
  typeMoney: null,
};

const state = {
  update: false,//
  loading: true,//
  saving: false,//
  enableCourseFinder: true,//
  confirmation: {//
    name: undefined,
    confirmed: false
  },

  codes: {
    code: undefined,
    actionCode: undefined
  },//

  /**
   * @param announcement Main object for creating/updating
   */
  announcement: EMPTY_ANNOUNCEMENT,//

  steps: {
    current: 1,
    total: 9,
  },//

  /** Based on current course type **/
  stepsInformation: {
    titles: new Map(),
    steps: [],
  },//
  stepsConfigurations: {}, // Configuration send by the back //

  validation: {//
    AnnouncementCourse: {
      enabled: true,
      validate: (announcement) => {
        let error = {
          error: false,
          type: 'AnnouncementCourse',
        };

        const isValid = announcement.course !== null;
        if (isValid) return error;

        return {
          ...error,
          error: true,
          i18n: true,
          message: 'ANNOUNCEMENT.COURSE_REQUIRED',
          data: [],
        };
      },
    },
    AnnouncementGeneralInfo: {
      enabled: true,
      validate: (announcement) => {
        let error = {
          type: 'AnnouncementGeneralInfo',
        };

        const data = {};
        data['AnnouncementGeneralInfo-code'] = !announcement.code || announcement.code.length < 1;
        data['AnnouncementGeneralInfo-startAt'] =
          !announcement.startAt || announcement.startAt.length < 1;
        data['AnnouncementGeneralInfo-finishAt'] =
          !announcement.finishAt || announcement.finishAt.length < 1;
        if (announcement.subsidized) {
          data['AnnouncementGeneralInfo-didacticGuide'] = !announcement.didacticGuide;
        }
        data['AnnouncementGeneralInfo-totalHours'] = announcement.totalHours <= 0;
        data['AnnouncementGeneralInfo-usersPerGroup'] = announcement.usersPerGroup < 1;
        data['AnnouncementGeneralInfo-timezone'] = announcement.timezone == null;
        data['AnnouncementGeneralInfo-extra'] = announcement.extra == [];

        let isError = false;
        const keys = Object.keys(data);
        for (let i = 0; i < keys.length; i++) {
          if (data[keys[i]]) {
            isError = true;
            break;
          }
        }

        data.i18n = [];
        if (announcement.timezone == null || announcement.timezone.length < 1) {
          data.i18n.push('ANNOUNCEMENT.FORM.TIMEZONE.REQUIRED');
          isError = true;
        }

        return { ...error, error: isError, data };
      },
    },

    AnnouncementGeneralInfoExtern: {
      enabled: true,
      validate: (announcement) => {
        let error = {
          type: 'AnnouncementGeneralInfoExtern',
        };

        const data = {};
        data['AnnouncementGeneralInfoExtern-code'] = !announcement.code || announcement.code.length < 1;
        data['AnnouncementGeneralInfoExtern-startAt'] =
          !announcement.startAt || announcement.startAt.length < 1;
        data['AnnouncementGeneralInfoExtern-finishAt'] =
          !announcement.finishAt || announcement.finishAt.length < 1;
        data['AnnouncementGeneralInfoExtern-totalHours'] = announcement.totalHours <= 0;
        data['AnnouncementGeneralInfoExtern-timezone'] = announcement.timezone == null;
        data['AnnouncementGeneralInfo-extra'] = announcement.extra == [];

        let isError = false;
        const keys = Object.keys(data);
        for (let i = 0; i < keys.length; i++) {
          if (data[keys[i]]) {
            isError = true;
            break;
          }
        }

        data.i18n = [];
        if (announcement.timezone == null || announcement.timezone.length < 1) {
          data.i18n.push('ANNOUNCEMENT.FORM.TIMEZONE.REQUIRED');
          isError = true;
        }

        return { ...error, error: isError, data };
      },
    },

    AnnouncementBonus: {
      enabled: true,
      validate: (announcement) => {
        let error = {
          type: 'AnnouncementBonus',
          error: false,
          data: {
            'AnnouncementBonus-actionType': false,
            'AnnouncementBonus-actionCode': false,
            // 'AnnouncementBonus-denomination': false,
            'AnnouncementBonus-contactPerson': false,
            'AnnouncementBonus-contactPersonEmail': false,
            'AnnouncementBonus-contactPersonTelephone': false,
            'AnnouncementBonus-usersPerGroup': false,
          },
        };
        if (!announcement.subsidized) return error;

        const data = {};
        // data['AnnouncementBonus-actionType'] =
        //   !announcement.actionType || announcement.actionType.length < 1;
        data['AnnouncementBonus-actionCode'] =
          !announcement.actionCode || announcement.actionCode.length < 1;
        // data['AnnouncementBonus-denomination'] =
        //   !announcement.denomination || announcement.denomination.length < 1;
        data['AnnouncementBonus-contactPerson'] =
          !announcement.contactPerson || announcement.contactPerson.length < 1;
        data['AnnouncementBonus-contactPersonEmail'] =
          !announcement.contactPersonEmail || announcement.contactPersonEmail.length < 1;
        data['AnnouncementBonus-contactPersonTelephone'] =
          !announcement.contactPersonTelephone || announcement.contactPersonTelephone.length < 1;
        data['AnnouncementBonus-usersPerGroup'] = announcement.usersPerGroup < 1;
        data['AnnouncementGeneralInfo-didacticGuide'] = !announcement.didacticGuide;

        /** Check if email is valid **/
        if (!validEmail(announcement.contactPersonEmail)) {
          data['AnnouncementBonus-contactPersonEmail'] = true;
        }

        let isError = false;
        data.i18n = [];
        if (!announcement.didacticGuide) {
          data.i18n.push('ANNOUNCEMENT.FORM.DIDACTIC_GUIDE.REQUIRED_STEP_2');
          isError = true;
        }

        const keys = Object.keys(data);
        for (let i = 0; i < keys.length; i++) {
          if (keys[i] === 'i18n') continue;
          if (data[keys[i]]) {
            isError = true;
            break;
          }
        }

        return { ...error, error: isError, data };
      },
    },
    AnnouncementStudents: {
      enabled: true,
      validate: announcement => {
        let error = {
          type: 'AnnouncementStudents',
          error: false,
          data: {},
        };
        if (announcement.students.length === 0) {
          error.error = true;
          error.message = 'ANNOUNCEMENT.FORM.GROUP.AT_LEAST_ONE_GROUP';
          error.i18n = true;
        } else {
          announcement.students.forEach(group => {
            if (group.data.length < 1) {
              error.error = true;
              error.message = 'ANNOUNCEMENT.FORM.GROUP.CANNOT_BE_EMPTY';
              error.i18n = true;
            }
          })
        }
        return error;
      }
    },
    AnnouncementGroups: {
      enabled: true,
      validate: (announcement) => {
        let error = {
          type: 'AnnouncementGroups',
          error: false,
          data: {
            i18n: []
          },
        };

        let message = null;
        const validateSession = announcement.type === COURSE_TYPE_MIXED || announcement.type === COURSE_TYPE_VIRTUAL_CLASSROOM || announcement.type === COURSE_TYPE_ON_SITE ||
                                 announcement.type === COURSE_TYPE_MIXED_EXTERN || announcement.type === COURSE_TYPE_VIRTUAL_CLASSROOM_EXTERN || announcement.type === COURSE_TYPE_ON_SITE_EXTERN;

        const data = {
          i18n: []
        };


        const announcementDStart = Date.parse(announcement.startAt);
        const announcementDFinish = Date.parse(announcement.finishAt);
        announcement.students.forEach(groupOfStudents => {
          let error = false;
          if (announcement.subsidized) {
            data[`UserGroup-${groupOfStudents.id}-code`] = !groupOfStudents.code || groupOfStudents.code.length < 1;
            // data[`UserGroup-${groupOfStudents.id}-denomination`] = !groupOfStudents.denomination || groupOfStudents.denomination.length < 1;
            // data[`UserGroup-${groupOfStudents.id}-companyProfile`] = !groupOfStudents.companyProfile || groupOfStudents.companyProfile.length < 1;
            // data[`UserGroup-${groupOfStudents.id}-companyCif`] = !groupOfStudents.companyCif || groupOfStudents.companyCif.length < 1;
            // data[`UserGroup-${groupOfStudents.id}-fileNumber`] = !groupOfStudents.fileNumber || groupOfStudents.fileNumber.length < 1;
            if (data[`UserGroup-${groupOfStudents.id}-code`]) error = true;
            // if (data[`UserGroup-${groupOfStudents.id}-denomination`]) error = true;
            // if (data[`UserGroup-${groupOfStudents.id}-companyProfile`]) error = true;
            // if (data[`UserGroup-${groupOfStudents.id}-companyCif`]) error = true;
            // if (data[`UserGroup-${groupOfStudents.id}-fileNumber`]) error = true;
          }

          if (groupOfStudents.tutor == null || groupOfStudents.tutor.tutorId === undefined || groupOfStudents.tutor.tutorId < 1) {
            message = 'ANNOUNCEMENT.FORM.GROUP.TUTOR_REQUIRED';
            error = true;
          }

          if (groupOfStudents.data.length < 1) {
            if (!data.i18n.includes('ANNOUNCEMENT.FORM.GROUP.CANNOT_BE_EMPTY'))
              data.i18n.push('ANNOUNCEMENT.FORM.GROUP.CANNOT_BE_EMPTY');
            error = true;
          }

          if (groupOfStudents.tutor == null || groupOfStudents.tutor.tutorId === undefined || groupOfStudents.tutor.tutorId < 1) {
            if (!data.i18n.includes('ANNOUNCEMENT.FORM.GROUP.TUTOR_REQUIRED'))
              data.i18n.push('ANNOUNCEMENT.FORM.GROUP.TUTOR_REQUIRED');
            error = true;
          }

          // Validate sessions
          if ((announcement.type === COURSE_TYPE_VIRTUAL_CLASSROOM || announcement.type === COURSE_TYPE_ON_SITE || announcement.type === COURSE_TYPE_MIXED ||
               announcement.type === COURSE_TYPE_VIRTUAL_CLASSROOM_EXTERN || announcement.type === COURSE_TYPE_ON_SITE_EXTERN || announcement.type === COURSE_TYPE_MIXED_EXTERN) && groupOfStudents.sessions.length === 0)
          {
            error = true;
            if (!data.i18n.includes('ANNOUNCEMENT.FORM.GROUP.SESSIONS_REQUIRED'))
              data.i18n.push('ANNOUNCEMENT.FORM.GROUP.SESSIONS_REQUIRED');
          }

          if (validateSession && groupOfStudents.numberOfSessions > 0) {
            let prevFinishAt = null;

            groupOfStudents.sessions.every((session, sessionIndex) => {
              if (
                  session.startAt.length < 1 ||
                  session.finishAt.length < 1 ||
                  ((announcement.type === COURSE_TYPE_VIRTUAL_CLASSROOM || announcement.type === COURSE_TYPE_VIRTUAL_CLASSROOM_EXTERN) && session.url?.length < 1)
              ) {
                if (!data.i18n.includes('ANNOUNCEMENT.FORM.GROUP.SESSION_FIELDS_REQUIRED'))
                  data.i18n.push('ANNOUNCEMENT.FORM.GROUP.SESSION_FIELDS_REQUIRED');
                data[`UserGroup-${groupOfStudents.id}-session-${sessionIndex}`] = true;
                error = true;
                return false;
              }

              const dSessionStart = Date.parse(session.startAt);
              const dSessionFinish = Date.parse(session.finishAt);

              if (prevFinishAt != null) {
                // Check if current start is greater or equal than previous session finishAt
                if (dSessionStart < prevFinishAt) {
                  data.i18n.push("ANNOUNCEMENT.FORM.GROUP.WRONG_DATE_BETWEEN_SESSIONS");
                  error = true;
                  data[`UserGroup-${groupOfStudents.id}-session-${sessionIndex}`] = true;
                  return false;
                }
              }
              prevFinishAt = dSessionFinish;

              if (dSessionStart > dSessionFinish) {
                if (!data.i18n.includes('ANNOUNCEMENT.FORM.GROUP.SESSION_DATE_MISMATCH'))
                  data.i18n.push('ANNOUNCEMENT.FORM.GROUP.SESSION_DATE_MISMATCH');
                data[`UserGroup-${groupOfStudents.id}-session-${sessionIndex}`] = true;
                error = true;
              }

              if (
                  dSessionStart < announcementDStart ||
                  dSessionStart > announcementDFinish ||
                  dSessionFinish < announcementDStart ||
                  dSessionFinish > announcementDFinish) {
                if (!data.i18n.includes('ANNOUNCEMENT.FORM.GROUP.ERROR_SESSION_BETWEEN_ANNOUNCEMENT'))
                  data.i18n.push('ANNOUNCEMENT.FORM.GROUP.ERROR_SESSION_BETWEEN_ANNOUNCEMENT');
                data[`UserGroup-${groupOfStudents.id}-session-${sessionIndex}`] = true;
                error = true;
              }
              return true;
            })
          }

          data[`AnnouncementGroups-${groupOfStudents.id}`] = error;
        });

        const keys = Object.keys(data);
        let isError = false;
        for (let i = 0; i < keys.length; i++) {
          if (data[keys[i]] === true) {
            isError = true;
            break;
          }
        }

        return {...error, error: isError, data, message, i18n: true};
      },
    }
  },

  /**
   * 'ComponentWhoFailed': {
   *     error: true,
   *     i18n: false,
   *     message: null,
   *     data: []
   * }
   */
  errors: {},//

  loadingChapters: false,//
  chapters: [],//
  types: [],//
  approvedCriteria: [],//
  clientConfigurationAnnouncement: [],//
  tutors: [],//
  alertsTypeTutor: [],//
  typeDiplomas: [],//
  typeSurveys: [],//
  virtualClassroomTypes: [],//
  timezones: [],//

  fundaeConfiguration: {
    actionTypes: [],
    maxStudentsPerGroup: 0,
    minPassingScore: 0,
    default_entry_margin: 0,
    default_exit_margin: 0
  },//
  typeMoneys: [],//
  typeSessions: []//
};

const mutations = {// migrated
  ...make.mutations(state),
  increaseStep(state) {
    state.steps.current += 1;
  },
  decreaseStep(state) {
    state.steps.current -= 1;
  },

  setChapterTiming(state, timing) {
    state.announcement.chapterTiming = timing;
  },

  initAnnouncementApprovedCriteria(state, approvedCriteria) {// migrated
    const data = {};
    approvedCriteria.forEach((criteria) => {
      if (criteria.extra.alwaysEnabled) {
        data[criteria.id] = {
          value: 0,
          enabled: false,
          alwaysEnabled: true
        };
      } else {
        data[criteria.id] = {
          value: 0,
          enabled: false,
        };
      }
    });
    state.announcement.approvedCriteriaValues = data;
  },

  SET_CONFIG_ANNOUNCEMENT(state, configAnnouncement) {
    state.announcement.configAnnouncement = configAnnouncement;
  },

  SET_ALERT_TYPE_TUTOR_VALUES(state, alertsTypeTutor) {
    state.announcement.alertTypeTutorValues = alertsTypeTutor;
  },

  SET_CURRENT_STEP(state, step) {
    state.steps.current = step;
  },

  RESET_ERROR(state) {
    state.errors = {};
  },

  SET_ID(state, id) {
    state.announcement.id = id;
  },

  SET_INSPECTOR_ACCESS(state, access) {
    state.announcement.inspectorAccess = access;
  },

  SET_DIDACTIC_GUIDE(state, guide) {
    state.announcement.didacticGuide = guide;
  },

  SET_UPDATE_STUDENTS(state, students) {
    state.announcement.students = students;
  },

  setPreSelectedCourse(state, course) {
    state.announcement.type = course.type;
    state.announcement.course = course;
  },

  SET_ANNOUNCEMENT_DATA(state, data) {
    const keys = Object.keys(state.announcement);
    keys.forEach(key => {
      state.announcement[`${key}`] = data[`${key}`];
    });
  },
  RUN_FUNDAE_CONFIGURATION(state, configuration) {
    const actionTypes = [];
    configuration.action_types.forEach((value) => {
      actionTypes.push({
        name: `ANNOUNCEMENT.FORM.ENTITY.FUNDAE_CONFIGURATION.${value.toUpperCase()}`,
        value
      })
    });
    state.fundaeConfiguration.actionTypes = actionTypes;
    state.fundaeConfiguration.maxStudentsPerGroup = configuration.max_students_per_group;
    state.fundaeConfiguration.minPassingScore = configuration.min_passing_score;
    state.fundaeConfiguration.default_entry_margin = configuration.default_entry_margin;
    state.fundaeConfiguration.default_exit_margin = configuration.default_exit_margin;
  },
  SET_ANNOUNCEMENT_CODE(state, code) {
    state.announcement.code = code;
  }
};

export const getters = {
  ...make.getters(state),
  isNotified(state) {//migrated
    return state.announcement.notifiedAt !== null
  },
  isConfigurable(state) {//migrated
    return state.announcement.status === STATUS_CONFIGURATION;
  }
};

export const actions = {
  ...make.actions(state),
  // setUpdate({ commit }, update) {
  //   commit('SET_UPDATE', update);
  // },

  async loadPreData({ commit, dispatch }, { id = null}) {//migrated
    commit('SET_LOADING', true);
    try {
      const { data } = await axios.get('/admin/announcement/form/pre-data');
      const {
        typeCourses, approvedCriteria, clientConfigurationAnnouncement,
        alertTypeTutor, typeDiplomas, typeSurveys, fundaeConfiguration, classroomvirtualTypes,
        codes, timezones, typeMoneys, typeSessions, stepsConfigurations, extra} = data.data;

      await commit('SET_STEPS_CONFIGURATIONS', stepsConfigurations);//
      await dispatch('configureSteps', stepsConfigurations);




      if (source === ANNOUNCEMENT_DENOMINATION_EXTERN) dispatch('initExternAnnouncement');

      commit('initAnnouncementApprovedCriteria', approvedCriteria);//
      commit('SET_TYPES', typeCourses);//
      commit('SET_APPROVED_CRITERIA', approvedCriteria);//
      commit('SET_CLIENT_CONFIGURATION_ANNOUNCEMENT', clientConfigurationAnnouncement);//
      commit('SET_ALERTS_TYPE_TUTOR', alertTypeTutor);//
      commit('SET_TYPE_DIPLOMAS', typeDiplomas);//
      commit('SET_TYPE_SURVEYS', typeSurveys);//
      commit('SET_VIRTUAL_CLASSROOM_TYPES', classroomvirtualTypes);//
      commit('RUN_FUNDAE_CONFIGURATION', fundaeConfiguration);//
      commit('SET_TIMEZONES', timezones);//
      commit('SET_TYPE_MONEYS', typeMoneys)//
      commit('SET_TYPE_SESSIONS', typeSessions)//
      commit('SET_EXTRA', extra)//

      commit('SET_CODES', codes);//
      // Load non immediate use data
      dispatch('loadTutors');

      // await dispatch('calculateSteps');
      if (id && id > 0) await dispatch('loadAnnouncementForm', { id });//
      else {
        commit('SET_ANNOUNCEMENT_CODE', codes.code);//
      }
    } finally {
      commit('SET_LOADING', false);//
    }
  },

  async findCourses({ commit }, { query, typeCourse }) {//migrated
    const url = new URL(window.location.origin + '/admin/announcement/form/courses');
    url.searchParams.set('query', query);
    url.searchParams.set('typeCourse', typeCourse);
    const result = await axios.get(url.toString());
    const { error, data } = result.data;
    if (!error) {
      return data;
    }
    return [];
  },

  initExternAnnouncement({ commit }) {//migrated
    console.log('initExternAnnouncement');
    let announcement = EMPTY_ANNOUNCEMENT;
    announcement.source = ANNOUNCEMENT_DENOMINATION_EXTERN;
    announcement.students = [{
      data: [],
      tutor: {
        name: "",
        email: "",
      },
      place: ''
    }];

    commit('SET_ANNOUNCEMENT', announcement);
  },

  configureSteps({ commit, getters}, stepsConfigurations) {// migrated
    // const { stepsConfigurations } = getters;
    const keys = Object.keys(stepsConfigurations);
    let data = {};
    keys.forEach( type => {
      const info = {
        titles: new Map(),
        steps: [],
      };

      let step = 1;
      // stepsConfigurations[type].forEach(stepInfo => {
      //   info.titles.set(step, stepInfo.name);
      //   const objectInfo = {...{type: stepInfo.extra.component, configurations: stepInfo.configurations, step}, ...INTERN_COMPONENT_DEFAULT_CONFIGURATION[stepInfo.extra.component] ?? {}};
      //   info.steps.push(objectInfo);
      //   step += 1;
      // });

      data[type] = info;
    });
    commit('SET_STEPS_CONFIGURATIONS', data);
  },

  // calculateSteps({commit, getters}) {
  //   const {type} = getters['announcement'];
  //   const { clientConfigurationAnnouncement } = getters;
  //
  //   let step = 1;
  //   const steps = {
  //     titles: new Map(),
  //     steps: [],
  //   };
  //
  //
  //   // steps.titles.set(step, 'ANNOUNCEMENT.FORM.STEPS.TEST1');
  //   // steps.steps.push({
  //   //   type: 'AnnouncementGeneralInfoExtern',
  //   //   icon: 'fa fa-university',
  //   //   step,
  //   // });
  //   // step += 1;
  //
  //   // Step 1
  //   // steps.titles.set(step, 'ANNOUNCEMENT.FORM.STEPS.COURSE');
  //   // steps.steps.push({
  //   //   type: 'AnnouncementCourse',
  //   //   icon: 'fa fa-university',
  //   //   step,
  //   // });
  //   // step += 1;
  //
  //   // Step 2
  //   steps.titles.set(step, 'ANNOUNCEMENT.FORM.STEPS.ANNOUNCEMENT');
  //   steps.steps.push({
  //     type: 'AnnouncementGeneralInfo',
  //     icon: 'fa fa-bullhorn',
  //     step,
  //     save: true,
  //     saveMethodName: 'saveGeneralInfo'
  //   });
  //   step += 1;
  //
  //   // Step 3
  //   const bonificationEnabled = clientConfigurationAnnouncement.find(conf => conf.type === 'BONIFICATION');
  //   if (bonificationEnabled) {
  //     steps.titles.set(step, 'ANNOUNCEMENT.FORM.STEPS.SUBSIDIZED');
  //     steps.steps.push({
  //       type: 'AnnouncementBonus',
  //       icon: 'fa fa-coins',
  //       step,
  //       save: true,
  //       saveMethodName: 'saveBonification'
  //     });
  //     step += 1;
  //   }
  //
  //   // Step 4
  //   steps.titles.set(step, 'ANNOUNCEMENT.FORM.STEPS.STUDENTS');
  //   steps.steps.push({
  //     type: 'AnnouncementStudents',
  //     icon: 'fa fa-users',
  //     step,
  //     save: true,
  //     saveMethodName: 'saveStudents',
  //     confirmationRequired: true
  //   });
  //   step += 1;
  //
  //   // Step 5
  //   steps.titles.set(step, 'ANNOUNCEMENT.FORM.STEPS.GROUPS');
  //   steps.steps.push({
  //     type: 'AnnouncementGroups',
  //     icon: 'fa fa-bullhorn',
  //     step,
  //     save: true,
  //     saveMethodName: 'saveGroupGeneralInfo'
  //   });
  //   step += 1;
  //
  //   const communicationEnabled = clientConfigurationAnnouncement.find(conf => conf.type === 'COMMUNICATION');
  //   if ((type === 'online' || type === 'mixed') && communicationEnabled) {
  //     // Step 6
  //     steps.titles.set(step, 'ANNOUNCEMENT.FORM.STEPS.COMMUNICATION');
  //     steps.steps.push({
  //       type: 'AnnouncementCommunication',
  //       icon: 'fa fa-envelope',
  //       step,
  //       save: true,
  //       saveMethodName: 'sendCommunicationStatus'
  //     });
  //     step += 1;
  //   }
  //
  //   const surveyEnabled = clientConfigurationAnnouncement.find(conf => conf.type === 'SURVEY');
  //   if (surveyEnabled) {
  //     steps.titles.set(step, 'ANNOUNCEMENT.FORM.STEPS.SURVEY');
  //     steps.steps.push({
  //       type: 'AnnouncementSurvey',
  //       icon: 'fa fa-bullhorn',
  //       step,
  //       save: true,
  //       saveMethodName: 'sendSurveyStatus'
  //     });
  //     step += 1;
  //   }
  //
  //   const certificateEnabled = clientConfigurationAnnouncement.find(conf => conf.type === 'CERTIFICATE');
  //   if (certificateEnabled) {
  //     steps.titles.set(step, 'ANNOUNCEMENT.FORM.STEPS.CERTIFICATE');
  //     steps.steps.push({
  //       type: 'AnnouncementCertificate',
  //       icon: 'fa fa-bullhorn',
  //       step,
  //       save: true,
  //       saveMethodName: 'sendCertificateStatus'
  //     });
  //     step += 1;
  //   }
  //
  //   // Step 9
  //   const { alertsTypeTutor } = getters;
  //   const alertEnabled = clientConfigurationAnnouncement.find(conf => conf.type === 'ALERT') && alertsTypeTutor.length > 0;
  //   if (alertEnabled) {
  //     steps.titles.set(step, 'ANNOUNCEMENT.FORM.STEPS.ALERTS');
  //     steps.steps.push({
  //       type: 'AnnouncementAlerts',
  //       icon: 'fa fa-bullhorn',
  //       step,
  //       save: true,
  //       saveMethodName: 'sendAlertsStatus'
  //     });
  //     step += 1;
  //   }
  //
  //
  //   commit('SET_STEPS', {
  //     current: 1,
  //     total: steps.steps.length,
  //   });
  //
  //   commit('SET_STEPS_INFORMATION', steps);
  // },

  resetError({ commit }) {//migrated
    commit('RESET_ERROR');
  },

  async nextStep({ commit, dispatch, getters }) {//migrated
    commit('RESET_ERROR');

    const { status } = getters['announcement'];
    if (status === STATUS_ACTIVE)
    {
      // Do nothing, avoid saving
      commit('increaseStep');
      return;
    }

    let { confirmation } = getters;
    const { steps, stepsInformation } = getters;

    if (confirmation.name !== undefined && !confirmation.confirmed) {
      confirmation = {
        name: undefined,
        confirmed: false
      };

      commit('SET_CONFIRMATION', confirmation);
    }

    const errors = {};
    const errorResult = await dispatch('validate', steps.current);
    if ('type' in errorResult) errors[errorResult.type] = errorResult;
    commit('SET_ERRORS', errors);
    if (errorResult.error) return;

    const currentStepInfo = stepsInformation.steps.find((step) => step.step === steps.current);

    if (currentStepInfo.confirmationRequired && currentStepInfo.type !== confirmation.name) {
      commit('SET_CONFIRMATION', {
        name: currentStepInfo.type,
        confirmed: false
      });
      return false;
    } else {
      commit('SET_CONFIRMATION', {
        name: undefined,
        confirmed: false
      });
    }

    if (currentStepInfo.save && currentStepInfo.saveMethodName) {
      try {
        commit('SET_SAVING', true);
        const { error, data } = await dispatch(`${currentStepInfo.saveMethodName}`);
        if (error) {
          let isString = typeof data === 'string';
          errors[currentStepInfo.type] = {
            error: true,
            i18n: isString,
            message: isString ? data : null,
            data,
            request: true
          };
          commit('SET_ERRORS', errors);
          return;
        }
      } finally {
        commit('SET_SAVING', false);
      }
    }

    commit('increaseStep');
  },

  prevStep({ commit }) {// migrated
    commit('RESET_ERROR');
    commit('SET_CONFIRMATION', {
      name: undefined,
      confirmed: false
    });
    commit('decreaseStep');
  },

  async onStepChange({ commit, getters, dispatch }, step) {//migrated
    commit('SET_ERRORS', {});
    commit('SET_CONFIRMATION', {
      name: undefined,
      confirmed: false
    });

    const steps = getters['steps'];
    const direction = step > steps.current ? 1 : -1;
    if (direction === 1) {
      let isValid = true;
      const errors = {};
      for (let i = steps.current; i < step; i++) {
        const result = await dispatch('validate', i);
        errors[result.type] = result;
        if (result.error) isValid = false;
      }

      commit('SET_ERRORS', errors);
      if (isValid) {
        const stepsInformation = getters['stepsInformation'];
        const currentStepInfo = stepsInformation.steps.find((step) => step.step === steps.current);
        if (currentStepInfo.save && currentStepInfo.saveMethodName) {
          try {
            commit('SET_SAVING', true);
            if (!await dispatch(`${currentStepInfo.saveMethodName}`)) return;
          } finally {
            commit('SET_SAVING', false);
          }
        }

        commit('SET_CURRENT_STEP', step);
      }
    } else {
      commit('SET_CURRENT_STEP', step);
    }
  },

  /**
   *
   * @param commit
   * @param getters
   * @param currentStep
   */
  validate({ commit, getters }, currentStep) {// migrated
    commit('RESET_ERROR');

    const validations = getters['validation'];
    const announcement = getters['announcement'];
    const stepsInformation = getters['stepsInformation'];
    const currentStepInfo = stepsInformation.steps.find((step) => step.step === currentStep);
    if (!currentStepInfo) return { error: false };

    const activeValidation = validations[currentStepInfo.type];
    if (!activeValidation || !activeValidation.enabled)
      return {
        error: false,
        type: currentStepInfo.type,
      };

    return activeValidation.validate(announcement);
  },

  async getCourseChaptersInfo({ commit, getters }) {//migrated
    const { course, chapterTiming } = getters['announcement'];
    const chapterTimingKeys = Object.keys(chapterTiming);
    const result = await axios.get(`/admin/announcement/form/chapters/${course.id}`);
    const { data } = result.data;
    let chapters = {};
    if (chapterTimingKeys.length === 0) {
      data.forEach((chapter) => {
        chapters[chapter.id] = {
          title: chapter.title,
          type: chapter.type,
          start: '',
          end: '',
          time: '00:00:00',
        };
      });
    } else {
      data.forEach(chapter => {
        const find = chapterTimingKeys.find(key => parseInt(key) === chapter.id);
        chapters[chapter.id] = {
          title: chapter.title ?? '',
          type: chapter.type ?? '',
          start: chapterTiming[find]?.start?.slice(0, 16),
          end: chapterTiming[find]?.end?.slice(0, 16),
          time: chapterTiming[find]?.time ?? '00:00:00'
        };
      })
    }

    commit('setChapterTiming', chapters);
    commit('SET_CHAPTERS', data);
    return true;
  },

  setConfigAnnouncement({ commit }, config) {//migrated
    commit('SET_CONFIG_ANNOUNCEMENT', config);
  },

  setAlertTypeTutorValues({ commit }, typeTutorValues) {// migrated
    commit('SET_ALERT_TYPE_TUTOR_VALUES', typeTutorValues);
  },

  async loadTutors({ commit }) {////migrated
    // const result = await axios.get('/admin/announcements/tutors');
    const result = await axios.get('/admin/announcement/form/tutors');
    const { data } = result.data;
    commit('SET_TUTORS', data);
  },

  async saveGeneralInfo({ commit, getters }) {//migrated
    const headers = {
      'Content-Type': 'multipart/form-data'
    };
    const url = `/admin/announcement/form/general-info`;
    const { id, course, code, startAt, finishAt, totalHours, usersPerGroup, didacticGuide, objectiveAndContent,
      approvedCriteriaValues, configAnnouncement, chapterTiming, timezone } = getters['announcement'];

    const result = await axios.post(url, {
      id, courseId: course.id, code, startAt, finishAt, totalHours,
      usersPerGroup, didacticGuide, objectiveAndContent,approvedCriteriaValues,
      configAnnouncement,
      chapterTiming, timezone
    }, {
      headers
    });

    const { error, data } = result.data;
    if (!error) {
      const { id, access, didacticGuideResult } = data;
      commit('SET_ID', id)
      commit('SET_INSPECTOR_ACCESS', access);
      commit('SET_DIDACTIC_GUIDE', didacticGuideResult);
    }


    return result.data;
  },

  async saveBonification({ commit, getters }) {//migrated
    const headers = {
      'Content-Type': 'multipart/form-data'
    };
    const url = `admin/announcement/form/bonification`;
    const { id,
      actionType, actionCode,
      denomination, contactPerson,
      contactPersonEmail, contactPersonTelephone,
      usersPerGroup, configAnnouncement, approvedCriteriaValues } = getters['announcement'];

    const result = await axios.post(
        url,
        {
          id, actionType, actionCode, denomination, contactPerson,
          contactPersonEmail, contactPersonTelephone, usersPerGroup,
          configAnnouncement, approvedCriteriaValues
        },
        {
          headers
        });

    return result.data;
  },

  async saveStudents({ commit, getters }) {//migrated
    const headers = {
      'Content-Type': 'multipart/form-data'
    };
    const url = `admin/announcement/form/students`;

    const { id, students } = getters['announcement'];
    const result = await axios.post(url, { id, students }, {
      headers
    });
    const { error, data } = result.data;
    if (!error) {
      commit('SET_UPDATE_STUDENTS', data);
    }
    return result.data;
  },

  async saveGroupGeneralInfo({ getters }) {//migrated
    const { id, students } = getters['announcement'];
    // Only send unsaved data
    const groupData = {
      id,
      data: []
    };
    students.forEach(group => {
      const { id, code, denomination, companyProfile, companyCif, fileNumber, numberOfSessions, place, sessions, typeMoney, cost } = group;
      groupData.data.push({
        id, code, denomination, companyProfile, companyCif, fileNumber, numberOfSessions, place, sessions, typeMoney, cost
      });
    })

    const result = await axios.post('/admin/announcement/form/group-info', groupData);
    return result.data;
  },

  async sendCommunicationStatus({ getters }){//migrated
    // const { id, enableChat, enableSMS, enableNotifications, enableForum } = getters['announcement'];
    const { id, configAnnouncement } = getters['announcement'];
    const result = await axios.post('/admin/announcement/form/communication', {
      id, configAnnouncement
    });
    return result.data;
  },

  async sendCertificateStatus({ getters }) {//migrated
    const { id, isConfirmationRequiredDiploma, typeDiploma, configAnnouncement } = getters['announcement'];
    const result = await axios.post('/admin/announcement/form/certificate', {
      id, isConfirmationRequiredDiploma, typeDiploma, configAnnouncement
    });

    return result.data;
  },

  async sendSurveyStatus({ getters }){//migrated
    const { id, configAnnouncement, typeSurvey } = getters['announcement'];
    const result = await axios.post('/admin/announcement/form/survey', {
      id, configAnnouncement, typeSurvey
    });

    return result.data;
  },

  async sendAlertsStatus({ getters }) {//migrated
    const { id, configAnnouncement, alertTypeTutorValues } = getters['announcement'];
    const result = await axios.post('/admin/announcement/form/alerts', {
      id, configAnnouncement, alertTypeTutorValues
    });
    return result.data;
  },

  async saveGroupTutorInfo({ commit }, formData) {//migrated
    const headers = {
      'Content-Type': 'multipart/form-data'
    };
    const result = await axios.post('/admin/announcement/form/group-tutor', formData, {
      headers
    });

    return result.data;
  },


  // When is called is an update
  async loadAnnouncementForm({ commit }, { id })// migrated
  {
    commit('SET_LOADING', true);
    try {
      const result = await axios.get(`/admin/announcement/form/announcement/${id}`);
      const { data } = result.data;
      
      // Repair dates
      data.finishAt = data.finishAt?.slice(0, 19);
      data.startAt = data.startAt?.slice(0, 19);
      const timingKeys = Object.keys(data.chapterTiming);
      timingKeys.forEach(k => {
        data.chapterTiming[k].start = data.chapterTiming[k].start?.slice(0, 19);
        data.chapterTiming[k].end = data.chapterTiming[k].end?.slice(0, 19);
        data.chapterTiming[k].offset = data.chapterTiming[k].start?.slice(19, 25);
      })
      data.students = data.students.map(group => {
        group.sessions = group.sessions.map(session => {
          return {
            ...session,
            startAt: session?.startAt?.slice(0, 19),
            finishAt: session?.finishAt?.slice(0, 19),
            offset: session?.startAt?.slice(19, 25)
          };
        })
        return {...group};
      });
      commit('SET_ANNOUNCEMENT_DATA', data);
    } finally {
      commit('SET_LOADING', false);
    }
  },

  async loadPreSelectedCourse({ commit }, courseId) {// migrated
    const url = new URL(window.location.origin + '/admin/announcement/form/pre-selected-course')
    url.searchParams.set('courseId', courseId)
    const result = await axios.get(url.toString());
    const { error, data } = result.data;
    if (!error) {
      commit('setPreSelectedCourse', data);
    }
  },

  /**
   * Send the last step
   * @returns {Promise<void>}
   */
  async submitForm({ dispatch, commit }) {//migrated
    try {
      commit('SET_SAVING', true);
      return await dispatch('sendAlertsStatus');
    } finally {
      commit('SET_SAVING', false);
    }
  },

  async uploadUsersExcelFile({ commit }, formData) {//migrated
    const headers = {
      'Content-Type': 'multipart/form-data'
    };
    const result = await axios.post('/admin/announcement/form/user-from-file', formData, {
      headers
    });
    return result.data;
  },

  async deleteSessionFromGroup({}, sessionId) {//migrated
    const result = await axios.post('/admin/announcement/form/delete-group-info-session', {
      id: sessionId
    });
    return result.data;
  },

  async saveGroupSession({}, {groupId, session}) {//migrated
    const result = await axios.post('/admin/announcement/form/group-info-session', {
      ...session,
      groupId
    });

    return result.data;
  }
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};
