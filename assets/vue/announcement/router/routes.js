import AnnouncementTask        from "../components/AnnouncementTask.vue";
import HomeView                from "../views/HomeView.vue";
import NewObservation          from "../../common/components/observationAnnouncement/NewObservation.vue";
import Observation             from "../../common/components/observationAnnouncement/Observation.vue";
import TaskCourse              from "../../common/components/taskCourse/TaskCourse.vue";
import ViewAnnouncement        from "../views/ViewAnnouncement.vue";
import AnnouncementUserDetails from "../views/AnnouncementUserDetails.vue";
import AnnouncementForm        from "../views/AnnouncementForm.vue";
import AnnouncementFormIntern from "../views/AnnouncementFormIntern.vue";
import AnnouncementFormExtern from "../views/AnnouncementFormExtern.vue";
import AnnouncementTaskDetails from "../views/Details/AnnouncementTaskDetails";
import ViewAnnouncementOld     from "../views/ViewAnnouncementOld";
import DetailView from "../views/DetailView.vue";

import router from "./index";
import AnnouncementCriteria from "../../settings_catalog/components/announcementCriteria/AnnouncementCriteria.vue";
import AnnouncementInfoExtern from "../views/Details/AnnouncementInfoExtern.vue";

export default [
    {
        path: '/admin/apps/announcement',
        component: HomeView,
        name: 'Home'
    },
    {
        path: '/admin/apps/announcement/create',
        component: AnnouncementFormIntern,
        name: 'CreateAnnouncement',
        beforeEnter: (to, from, next) => {
            if (router.app.$isGranted('ROLE_MANAGER')) next();
            return false;
        }
    },
    {
        path: '/admin/apps/announcement-extern/create',
        component: AnnouncementFormExtern,
        name: 'CreateAnnouncementExtern',
        beforeEnter: (to, from, next) => {
            if (router.app.$isGranted('ROLE_MANAGER')) next();
            return false;
        }
    },
    {
        path: '/admin/apps/announcement/update/:id',
        component: AnnouncementFormIntern,
        name: 'UpdateAnnouncement',
        beforeEnter: (to, from, next) => {
            if (router.app.$isGranted('ROLE_MANAGER')) next();
            return false;
        }
    },
    {
        path: '/admin/apps/announcement-extern/update/:id',
        component: AnnouncementFormExtern,
        name: 'UpdateAnnouncementExtern',
        beforeEnter: (to, from, next) => {
            if (router.app.$isGranted('ROLE_MANAGER')) next();
            return false;
        }
    },
    {
        path: '/admin/apps/announcement/:id',
        component: ViewAnnouncement,
        name: 'ViewAnnouncement'
    },
    {
        path: '/admin/apps/announcementOld/:id',
        component: ViewAnnouncementOld,
        name: 'ViewAnnouncementOld'
    },
    {
        path: '/admin/apps/announcement/task/:id/:studentId',
        component: AnnouncementTaskDetails,
        name: 'ViewAnnouncementTaskDetails'
    },
    {
        path: '/admin/apps/announcement/:id/task/create',
        component: AnnouncementTask,
        name: 'CreateAnnouncementTask'
    },
    {
        path: '/admin/apps/announcement/:announcementId/task/:id/update',
        component: AnnouncementTask,
        name: 'UpdateTaskCourse'
    },
    {
        path: '/admin/apps/announcement/:parentName/:parentId/task/:id/view',
        component: TaskCourse,
        name: 'ViewTaskCourse'
    },
    {
        path: '/admin/apps/announcement/:id/observation/create',
        component: NewObservation,
        name: 'NewObservation'
    },
    {
        path: '/admin/apps/announcement/:id/observation/update',
        component: NewObservation,
        name: 'UpdateObservation'
    },
    {
        path: '/admin/apps/announcement/observation/:id/view',
        component: Observation,
        name: 'ViewObservation'
    },
    {
        path: '/admin/apps/announcement/:id/user-detail/:user',
        component: AnnouncementUserDetails,
        name: 'ViewAnnouncementUserDetail'
    },

    // View Announcement extern
    {
        path: '/admin/apps/announcement/:id/view',
        component: DetailView,
        name: 'AnnouncementDetail',
        children: [
            {
                path: 'info-extern',
                name: 'AnnouncementExternInfo',
                component: AnnouncementInfoExtern
            },
        ]
    },
];
