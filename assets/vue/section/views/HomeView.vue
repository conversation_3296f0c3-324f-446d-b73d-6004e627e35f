<template>
  <div class="home">
    <home
      title="SECTION.HOME.TITLE"
      description="SECTION.HOME.DESCRIPTION"
      src-thumbnail="/assets/imgs/Grupo 5762.svg"
    >
      <template v-slot:content-main>
        <div
          class="col-12 d-flex flex-row align-items-center justify-content-center"
          v-if="loading"
        >
          <spinner />
        </div>
        <div v-else>
          <div
            class="col-12 d-flex align-items-center justify-content-end mt-0"
          >
            <router-link
              type="button"
              class="btn btn-primary"
              :to="{ name: 'SectionCreate' }"
            >
              {{ $t("COURSE.COURSE_SECTION.CREATE") }}
            </router-link>
          </div>

          <table class="table table-condensed mt-5">
            <thead>
              <tr>
                <th>{{ $t("COURSE.COURSE_SECTION.SORT") }}</th>
                <th>
                  {{ $t("COURSE.COURSE_SECTION.NAME") }}
                </th>
                <th>{{ $t("COURSE.COURSE_SECTION.ACTIVE") }}</th>
                <th>
                  <label-with-info
                    :info="
                      $t('COURSE.COURSE_SECTION.INITIAL_INFO', {
                        section: $t('CHAPTER.CONTENT.SECTION').toLowerCase(),
                      }) + ''
                    "
                    id="initial-info"
                    location="top"
                  >
                    {{ $t("COURSE.COURSE_SECTION.INITIAL") }}
                  </label-with-info>
                </th>
                <th style="text-align: right">
                  {{ $t("COURSE.COURSE_SECTION.ACTIONS") }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="(section, index) in sections"
                :key="section.id"
                draggable
                @dragstart="startDrag($event, index)"
                @drop="onDrop(index)"
                @dragover.prevent
                @dragenter.prevent
              >
                <td><i class="fa fa-bars sections"></i></td>
                <td>
                  <router-link
                    :to="{
                      name: 'SectionUpdate',
                      params: { ...$route.params, id: section.id },
                    }"
                  >
                    {{ section.name }}
                  </router-link>
                </td>
                <td>
                  <BaseSwitch
                    :tag="`switcher-course-section-${section.id}`"
                    v-model="section.active"
                    @change="changeStatus(section)"
                    :disabled="section.isMain"
                    theme="light"
                  />
                </td>
                <td class="text-center">
                  <button
                    @click="ChangeIsMain(section)"
                    type="button"
                    style="border: none; background-color: transparent"
                  >
                    <i v-if="section.isMain" class="fas fa-flag sections"></i>
                    <i v-else class="fas fa-flag" style="color: #cbd5e1"></i>
                  </button>
                </td>
                <td>
                  <div class="d-flex justify-content-end">
                    <router-link
                      class="btn btn-sm btn-primary ml-1"
                      :to="{
                        name: 'SectionUpdate',
                        params: { ...$route.params, id: section.id },
                      }"
                    >
                      <i class="fa fa-pen"></i>
                    </router-link>
                    <button
                      @click="deleteSection(section)"
                      type="button"
                      class="btn btn-sm btn-danger ms-2"
                    >
                      <i class="fa fa-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </template>
    </home>
  </div>
</template>

<script>
import { get } from "vuex-pathify";

import Home from "../../base/Home.vue";
import Spinner from "../../admin/components/base/Spinner.vue";
import BaseSwitch from "../../base/BaseSwitch.vue";
import LabelWithInfo from "../../common/components/ui/LabelWithInfo.vue";

export default {
  components: {
    Home,
    BaseSwitch,
    Spinner,
    LabelWithInfo,
  },

  data() {
    return {
      sortBy: "name",
      sortDesc: false,
      dragItem: null,
      dropItem: null,
      antIdMain: null,
    };
  },

  computed: {
    ...get("sectionModule", ["loading", "getSections", "getUserLocale"]),
    defaultLocale: get("localeModule/defaultLocale"),

    sections() {
      return this.getSections();
    },
  },

  async created() {
    this.$store.dispatch("contentTitleModule/addRoute", {
      routeName: this.$route.name,
      params: {
        linkName: this.$t("COURSE.COURSE_SECTION.PAGE_TITLE"),
        params: this.$route.params,
      },
    });

    await this.$store.dispatch(
      "sectionModule/fetchSections",
      "/admin/section/all"
    );

    this.sections.sort((a, b) => a.sort - b.sort);

    const index = this.sections.findIndex((item) => item.isMain === true);
    this.antIdMain = this.sections[index].id;

    this.sections.forEach((item) => {
      let sectionName = this.getNameByDefaultLocale(item.translations);
      if (sectionName) item.name = sectionName;
    });
  },

  methods: {
    getNameByDefaultLocale(translations) {
      let translation = translations.filter(
        (translation) => translation.locale === this.userLocale
      );
      return translation.length > 0 ? translation[0].name : "";
    },
    async ChangeIsMain(section) {
      if (section.isMain) {
        this.$toast.error(
          this.$t("COURSE.COURSE_SECTION.ISMAIN_ERROR1", {
            section: this.$t("CHAPTER.CONTENT.SECTION").toLowerCase(),
          }) + ""
        );
      } else if (!section.active) {
        this.$toast.error(
          this.$t("COURSE.COURSE_SECTION.ISMAIN_ERROR2", {
            section: this.$t("CHAPTER.CONTENT.SECTION").toLowerCase(),
          }) + ""
        );
      } else {
        await this.$store.dispatch("sectionModule/changeIsMain", {
          endpoint: `/admin/section/${section?.id}/change-is-main`,
          requestData: { id: section.id },
        });
      }
    },
    deleteSection(section) {
      if (section.isMain) {
        this.$toast.error(
          this.$t("COURSE.COURSE_SECTION.ISMAIN_ERROR1", {
            section: this.$t("CHAPTER.CONTENT.SECTION").toLowerCase(),
          }) + ""
        );
      } else {
        this.$alertify.confirmWithTitle(
          this.$t("DELETE"),
          this.$t("COMMON_AREAS.QUESTION_DELETE"),
          () => {
            this.$store
              .dispatch("sectionModule/deleteSection", {
                endpoint: `/admin/section/${section?.id}/delete`,
              })
              .then((r) => {
                const index = this.sections.findIndex(
                  (item) => item.id === section.id
                );
                this.sections.splice(index, 1);
                this.$toast.success(this.$t("DELETE_SUCCESS") + "");
              })
              .catch((e) => {
                this.$toast.error("DELETE_FAILED");
              })
              .finally(() => {});
          },
          () => {}
        );
      }
    },
    startDrag(evt, index) {
      this.dragItem = index;
      evt.dataTransfer.dropEffect = "move";
      evt.dataTransfer.effectAllowed = "move";
    },

    async onDrop(index) {
      let data = this.orderNewSort(this.dragItem, index + 1);

      this.dropItem = this.sections.splice(this.dragItem, 1)[0];
      this.sections.splice(index, 0, this.dropItem);
      this.dropItem = null;

      await this.$store.dispatch("sectionModule/changeSort", {
        endpoint: `/admin/section/changeSort`,
        requestData: { sections: data },
      });
    },

    orderNewSort(antPosSort, newPosSort) {
      let newSortSections = [];

      let elmentTras = {
        id: this.sections[antPosSort].id,
        newSort: newPosSort,
      };
      newSortSections.push(elmentTras);

      if (this.dragItem > newPosSort) {
        this.sections.forEach((item) => {
          let element = {
            id: null,
            newSort: null,
          };

          if (item.sort >= newPosSort && item.sort <= antPosSort) {
            element.id = item.id;
            element.newSort = item.sort + 1;
            newSortSections.push(element);
          }
        });
      } else {
        this.sections.forEach((item) => {
          let element = {
            id: null,
            newSort: null,
          };
          if (item.sort > antPosSort + 1 && item.sort <= newPosSort) {
            element.id = item.id;
            element.newSort = item.sort - 1;
            newSortSections.push(element);
          }
        });
      }

      return newSortSections;
    },

    async changeStatus(section) {
      if (section.isMain) {
        this.$toast.error(this.$t("COURSE.COURSE_SECTION.ISMAIN_ERROR1") + "");
      } else {
        await this.$store.dispatch("sectionModule/changeStatus", {
          endpoint: `/admin/section/${section?.id}/active`,
          requestData: { id: section.id, active: section.active },
        });
      }
    },

    sortByColumn(column) {
      if (this.sortBy === column) {
        this.sortDesc = !this.sortDesc;
      } else {
        this.sortBy = column;
        this.sortDesc = false;
      }
    },
  },
};
</script>

 <style scoped lang="scss"> 
.home {
  .sort-asc::after,
  .sort-desc::after {
    content: " ▼";
    font-size: 10px;
    opacity: 0.5;
    margin-left: 5px;
  }

  .sort-desc::after {
    content: " ▲";
  }

  th {
    cursor: pointer;
  }

  th.sort-asc:hover::after,
  th.sort-desc:hover::after {
    opacity: 1;
    cursor: pointer;
  }

  .sections {
    color: var(--color-primary);
  }
}
</style>
