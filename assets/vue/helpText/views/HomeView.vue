<template>
  <home
    title="HELP_TEXT.HOME.TITLE"
    description="HELP_TEXT.HOME.DESCRIPTION"
    src-thumbnail="/assets/imgs/help_text_home.svg"
  >
    <template v-slot:content-actions>
      <router-link
        class="btn btn-primary mr-auto"
        :to="{ name: 'CreateHelpText' }"
        >{{ $t("HELP_TEXT.CREATE_HELP_CONTENT") }}</router-link
      >
    </template>
    <template v-slot:content-main>
      <div
        class="d-flex align-items-center justify-content-center"
        v-if="loading"
      >
        <spinner />
      </div>
      <div v-else class="w-100 TableContainer">
        <table class="table">
          <thead>
            <tr>
              <th>{{ $t("CATEGORY") }}</th>
              <th>{{ $t("CONTENT.CONFIGUREFIELD.CONTENT") }}</th>
              <th></th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="item in items" :key="item.id">
              <td>{{ item.category }}</td>
              <td>
                <div v-html="item.text" />
              </td>
              <td>
                <div class="d-flex  justify-content-end">
                  <router-link class="btn btn-primary" :to="{name: 'UpdateHelpText', params: {id: item.id}}"> <i class="fa fa-pen" ></i></router-link>
                  <button type="button" class="btn btn-danger ms-2" @click="deleteHelpText(item)"><i class="fa fa-trash "></i></button>
                </div>
              </td>
            </tr>
            <tr v-if="items.length === 0">
              <td colspan="3">
                <base-not-result />
              </td>
            </tr>
          </tbody>
        </table>

        <pagination
          v-if="items.length > 0"
          :total-items="totalItems"
          :prop-current-page="page"
          @current-page="onCurrentPage"
        />
      </div>
    </template>
  </home>
</template>

<script>
import { get, sync } from "vuex-pathify";
import Home from "../../base/Home.vue";
import Spinner from "../../admin/components/base/Spinner.vue";
import Pagination from "../../admin/components/Pagination.vue";
import BaseNotResult from "../../base/BaseNotResult.vue";

export default {
  name: "HomeView",
  components: { BaseNotResult, Pagination, Spinner, Home },
  computed: {
    loading: get("helpTextModule/loading"),
    items: get("helpTextModule/items"),
    totalItems: get("helpTextModule/pagination@totalItems"),
    page: sync("helpTextModule/pagination@page"),
    defaultLocale: get("localeModule/defaultLocale"),
  },
  created() {
    this.$store.dispatch("contentTitleModule/addRoute", {
      routeName: this.$route.name,
      params: {
        linkName: this.$t("HELP_TEXT.TITLE"),
        params: {},
      },
    });

    this.getItems();
  },
  methods: {
    itemLocales(item) {
      return Object.keys(item.translations);
    },
    getItems() {
      this.$store.dispatch("helpTextModule/getItems");
    },
    deleteHelpText(item) {
      this.$alertify.confirmWithTitle(
        this.$t("HELP_TEXT.DELETE.CONFIRM.TITLE"),
        this.$t("HELP_TEXT.DELETE.CONFIRM.DESCRIPTION"),
        () => {
          this.$store
            .dispatch("helpTextModule/deleteHelpText", item.id)
            .then((res) => {
              const { error, data } = res;
              if (error)
                this.$toast.error(this.$t("HELP_TEXT.DELETE.FAILED") + "");
              else
                this.$toast.success(this.$t("HELP_TEXT.DELETE.SUCCESS") + "");
            });
        },
        () => {}
      );
    },
    onCurrentPage(page) {
      this.page = page;
      this.getItems();
    },
  },
};
</script>

 <style scoped lang="scss"> 
.Home {
  .table th,
  .table td {
    border: none !important;
  }
  tr {
    border-bottom: 1px solid #dee2e6 !important;
  }

  :deep(.Home--header) {
    grid-template-columns: 1fr 280px;
  }
}
</style>
