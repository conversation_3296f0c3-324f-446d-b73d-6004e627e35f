import Vue from 'vue';

// Froala editor
import 'froala-editor/js/plugins.pkgd.min';
import 'froala-editor/js/third_party/font_awesome.min';
import 'froala-editor/js/froala_editor.pkgd.min';

import 'froala-editor/css/froala_editor.pkgd.min.css';
import 'froala-editor/css/froala_editor.min.css';

import VueFroala from 'vue-froala-wysiwyg';
import $ from "jquery";
Vue.use(VueFroala);

function initFroalaEditor(store, froalaKey) {
    store.dispatch('froalaEditorModule/setFroalaKey', froalaKey);
}

export function findFroalaKey(store, froalaElement = null) {
    if (froalaElement == null) return;
    const froalaKey = froalaElement.attr('froala-key');
    froalaElement.remove();// Remove from dom object
    initFroalaEditor(store, froalaKey);
}

export default initFroalaEditor;
