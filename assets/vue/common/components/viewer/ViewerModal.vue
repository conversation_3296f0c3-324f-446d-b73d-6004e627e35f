<template>
  <div class="ViewerModal">
    <div class="modal fade" :id="`${id}-viewer-modal`" tabindex="-1" aria-hidden="true">
      <div class="modal-dialog" :style="dialogStyle">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="btn-close" @click="closeModal()"></button>
          </div>
          <div class="modal-body" :style="bodyStyle">
            <slot name="content" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: "ViewerModal",
  props: {
    id: {
      type: String|Number,
      default: 'viewer-modal'
    },
    open: {
      type: Boolean,
      default: false
    },
    dialogStyle: {
      type: Object,
      default: {}
    },

    bodyStyle: {
      type: Object,
      default: {}
    }
  },
  watch: {
    open() {
      if (this.open) this.openModal();
      else this.closeModal();
    }
  },
  mounted() {
    if (this.open) this.openModal();
  },
  methods: {
    openModal(){
      const modal = document.getElementById( this.id + '-viewer-modal');
      modal.classList.add("show");
      modal.style.display = 'block';

      modal.setAttribute("aria-modal", "true");
      modal.setAttribute('role', 'dialog');
      modal.removeAttribute('aria-hidden');
      modal.setAttribute('style', 'display: block; padding-right: 17px;');
    },

    closeModal() {
      const modal = document.getElementById(this.id + '-viewer-modal');
      modal.classList.remove("show");
      modal.style.display = 'none';

      modal.setAttribute("aria-modal", "false");
      modal.setAttribute('role', 'dialog');
      modal.setAttribute('aria-hidden', 'true');
      modal.setAttribute('style', 'display: none; padding-right: 17px;');

      this.$emit('close');
    },

  }


}
</script>

 <style scoped lang="scss"> 
.ViewerModal {
  .btn-close {
    color: #212121;
  }

  .modal {
    padding: 0 !important;
    $modal-border-radius: 5px;
    height: auto;

    .modal-dialog {
      max-width: unset;
      width: calc(100vw - 3rem);
      height: calc(100vh - 3rem);
      border-radius: $modal-border-radius;

      .modal-content {
        height: calc(100% - 1rem);
        border-radius: $modal-border-radius;

        .modal-header {
          padding: 0.5rem 1rem;
          font-size: 12px;
          border-top-left-radius: $modal-border-radius;
          border-top-right-radius: $modal-border-radius;
        }

        .modal-body {
          border-bottom-left-radius: $modal-border-radius;
          border-bottom-right-radius: $modal-border-radius;
          background-color: $modal-body-bg-color;
          padding: 0;
        }
      }
    }
  }
}
</style>
