<template>
  <div class="ForumContent">
    <slot name="top-content"></slot>
    <forum-header
        :thread="thread"
        :total-items="totalMessages"
        :show-actions="showThreadActions"
        :header-title="headerTitle"
        @update="$emit('update', $event)"
        @delete="$emit('delete', $event)"
        v-if="showHeader && !loadingMessages"
    >
      <template v-slot:left-custom-content>
        <slot name="header-left-custom-content" />
      </template>
    </forum-header>
    <forum-body>
      <template v-slot:content>
        <div class="px-4 pb-4 d-flex flex-column-reverse">
          <DataNotFound
              :text="$t('ANNOUNCEMENT.FORUMTAB.NOT_FOUND') || ''"
              icon="fa-comments"
              :hide-on="!messages.length"
              :banner="true"/>
          <div v-for="message in messages" :key="'message_' + message.id" class="mt-3">
            <thread-message
                :message="message"
                @reply="replyTo = $event"
                :show-reply="true"
            />
            <div class="replies pl-5">
              <thread-message
                  v-for="reply in message.replies" :key="'message_reply_' + reply.id"
                  :message="reply"
              />
              <div class="d-flex gap-3 w-100 mt-2 mb-2" v-if="replyTo?.id === message.id">
                <img :src="`url(/uploads/users/avatars/${message.avatar ?? 'default.svg'})`"
                     class="userAvatar" alt=" ">
                <forum-footer class="p-0 w-100" :reply-to-message="message" @reply="onReply"/>
              </div>
            </div>
          </div>
        </div>
        <div class="w-100 d-flex align-items-center justify-content-center flex-column" v-if="loadingMessages">
          <spinner />
        </div>
      </template>
    </forum-body>
    <forum-footer v-if="isThreadAvailable" v-show="!loadingMessages" class="bg-gray" @reply="onReply" @click="replyTo = null"/>
  </div>
</template>

<script>
import ForumBody     from "./ForumBody.vue";
import ForumFooter   from "./ForumFooter.vue";
import ForumHeader   from "./ForumHeader.vue";
import ThreadMessage from "../../../announcement/components/forum/ThreadMessage.vue";
import DataNotFound  from "../../../announcement/components/details/DataNotFound";
import Spinner       from "../../../admin/components/base/Spinner";
import {get} from "vuex-pathify";

export default {
  name: "ForumContent",
  components: {Spinner, DataNotFound, ThreadMessage, ForumFooter, ForumBody, ForumHeader},
  props: {
    headerTitle: {
      type: String,
      default: 'COMMENTS'
    },

    thread: {
      type: Object|Array,
      default: () => ({
        id: -1,
        name: ''
      })
    },
    loadingMessages: {
      type: Boolean,
      default: false
    },

    showThreadActions: {
      type: Boolean,
      default: true
    },

    showMessageActions: {
      type: Boolean,
      default: true
    },

    showHeader: {
      type: Boolean,
      default: true
    },

    showFooter: {
      type: Boolean,
      default: true
    },

    showEmoji: {
      type: Boolean,
      default: false
    },

    // Only for messages
    value: {
      type: Array|Object,
      default: () => ([])
    }
  },
  data() {
    return {
      replyTo: null
    };
  },
  computed: {
    messages: {
      get() {
        return this.value ?? [];
      },
      set(newValue) {
        this.$emit('input', newValue);
      }
    },
    totalMessages() {
      const messageList = (this.messages || []);
      return messageList.reduce((acc, cur) => (acc + (cur.replies || []).length), messageList.length)
    },
    selectedForumThread: get('forumModule/selectedForumThread'),
    isThreadAvailable() {
      if (this.selectedForumThread == null) return false;
      const currentDate = new Date();
      if (this.selectedForumThread.from != null) {
        const from = Date.parse(this.selectedForumThread.from);
        if (currentDate.getTime() < from) return false;
      }
      if (this.selectedForumThread.to != null) {
        const to = Date.parse(this.selectedForumThread.to);
        if (currentDate.getTime() > to) return false;
      }
      return true;
    }
  },
  methods: {
    reply(id = null) {
      if (id != null) {
        const index = this.messages.findIndex(message => message.id === id);
        if (index >= 0) return this.messages[index];
      }
      return null;
    },
    onReply(data) {
      this.$emit('reply', data);
      this.replyTo = null;
    }
  },
}
</script>

 <style scoped lang="scss"> 
.ForumContent {
  width: 100%;
  position: relative;

  .replies {
    .userAvatar {
      width: 2.5rem;
      height: 2.5rem;
    }

    ::v-deep {
      .ThreadMessage {
        border-width: 0 0 0 2px;
      }
    }
  }

  .bg-gray {
    background-color: var(--color-neutral-light);
  }
}
:deep(.loader) {
  font-size: 24px !important;
}
</style>
