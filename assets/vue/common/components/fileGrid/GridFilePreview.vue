<template>
  <div class="GridFilePreview" :id="id" :style="{
    'width': renderWidth,
    'height': renderHeight
  }">
    <canvas  :id="`${id}-canvas`"></canvas>
    <div class="actions" v-if="deleteBtn || downloadBtn">
      <button v-if="deleteBtn" type="button" class="btn btn-sm btn-danger" @click="$emit('delete', file)"><i class="fa fa-trash"></i></button>
      <a class="btn btn-sm btn-info" v-if="downloadBtn && !accept.includes('image')" type="button" :href="file.url" target="_blank"><i class="fa fa-eye"></i></a>
    </div>
  </div>
</template>

<script>
import axios from "axios";

const pdfJsLib = require('pdfjs-dist/build/pdf');
pdfJsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.4.120/pdf.worker.min.js';

export const A4_WIDTH = 695;
export const A4_HEIGHT = 842;

export default {
  name: "GridFilePreview",
  props: {
    id: {
      type: String,
      default: 'file-selector-renderer'
    },
    file: {
      type: File|Object|Array,
      required: true
    },
    width: {
      type: Number,
      default: 250
    },
    height: {
      type: Number,
      default: 250
    },
    isPercent: {
      type: Boolean,
      default: false
    },
    downloadBtn: {
      type: Boolean,
      default: false
    },
    deleteBtn: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      newHeight: 0,
      newWidth: 0,
      memoryData: {
        urlObject: null,
        object: null
      },
      accept: '*/*'
    };
  },
  computed: {
    renderHeight() {
      return this.accept.includes('pdf') ? (this.newHeight === 0 ? this.height : this.newHeight) + 'px' : this.height + (this.isPercent ? '%' : 'px');
    },
    renderWidth() {
      if (this.accept.includes('video')) {
        return this.newWidth + 'px';
      }
      return this.width + (this.isPercent ? '%' : 'px');
    },
    isVideo() {
      return this.accept.includes('video');
    }
  },
  watch: {
    file: {
      handler: function (val, oldVal) {
        if (this.file instanceof File){
          this.accept = this.file.type;
          this.renderFile(this.file);
        }
        else {
          // Is object or array
          this.accept = this.file.mimeType;
          this.renderFromUrl();
        }
      },
      deep: true,
    }
  },

  mounted() {
    if (this.file instanceof File){
      this.accept = this.file.type;
      this.renderFile(this.file);
    }
    else {
      // Is object or array
      this.accept = this.file.mimeType;
      this.renderFromUrl();
    }
  },

  beforeDestroy() {
    if (this.memoryData.urlObject != null) URL.revokeObjectURL(this.memoryData.urlObject);// Invalidate created url object for memory cleaning
    if (this.memoryData.object) this.memoryData.object.remove();// Is a dom element, remove for memory cleaning
  },

  methods: {
    calculateHeight() {
      if (this.accept.includes('pdf')) {
        // const elementWidth = document.getElementById(this.id).clientWidth;
        const elementWidth = this.width;
        this.newHeight = (A4_HEIGHT / A4_WIDTH) * elementWidth;
      } else this.newHeight = this.width;
    },

    renderFromUrl() {
      if (this.memoryData.urlObject != null) URL.revokeObjectURL(this.memoryData.urlObject);// Invalidate created url object for memory cleaning
      if (this.memoryData.object) this.memoryData.object.remove();// Is a dom element, remove for memory cleaning
      this.calculateHeight();
      if (this.accept.includes('video')) {
        // this.renderVideoFile(file);
      } else {
        if ('url' in this.file) {
          axios.get(this.file.url, {
            responseType: 'blob'
          }).then(r => {
            this.readFileAsyncFromBlob(r.data).then(result => {
              if (this.accept.includes('pdf')) this.renderPdfContent(result)
              if (this.accept.includes('image')) this.renderImageContent(result);
            });
          })
        }
      }
    },

    renderFile(file) {
      if (this.memoryData.urlObject != null) URL.revokeObjectURL(this.memoryData.urlObject);// Invalidate created url object for memory cleaning
      if (this.memoryData.object) this.memoryData.object.remove();// Is a dom element, remove for memory cleaning

      this.calculateHeight(file);
      if (this.accept.includes('video')) {
        this.renderVideoFile(file);
      } else {
        this.readFileAsync(file).then(result => {
          if (file.type.includes('pdf')) this.renderPdfContent(result)
          if (file.type.includes('image')) this.renderImageContent(result);
        })
      }
    },

    renderVideoFile(file) {
      const self = this;
      let canvas = document.getElementById(this.id + '-canvas');
      let context = canvas.getContext('2d');

      const video = document.createElement('video');
      self.memoryData = {
        urlObject: URL.createObjectURL(file)
      };
      video.src = self.memoryData.urlObject;
      video.muted = true;

      video.oncanplay = function (e) {
        video.play();
        video.pause();
        requestAnimationFrame(updateCanvas);
      };

      //this.newHeight = (A4_HEIGHT / A4_WIDTH) * elementWidth;
      const clientHeight = document.getElementById(this.id).clientHeight;

      video.addEventListener('loadedmetadata', function () {
        console.log('loaded metadata');
        console.log(video.videoWidth);
        console.log(video.videoHeight);
        console.log('Element height ' + clientHeight);
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        self.newWidth = (video.videoWidth / video.videoHeight) * clientHeight;
        // console.log(newWidth);
      })

      self.memoryData.object = video;

      function updateCanvas() {
        context.drawImage(video, 0, 0, video.videoWidth, video.videoHeight);
        window.requestAnimationFrame(updateCanvas);
      }
    },

    renderImageContent(result) {
      let canvas = document.getElementById(this.id + '-canvas');
      let context = canvas.getContext('2d');
      let image = new Image();
      image.onload = function () {
        canvas.width = image.naturalWidth;
        canvas.height = image.naturalHeight;
        context.drawImage(image, 0, 0);
      }
      image.src = result;
    },

    renderPdfContent(result) {
      let canvas = document.getElementById(this.id + '-canvas');
      let context = canvas.getContext('2d');
      let scale = 1;

      let loadingTask = pdfJsLib.getDocument({data: result});
      loadingTask.promise.then(function (pdf) {
        pdf.getPage(1).then(function(page) {
          let viewport = page.getViewport({scale: scale});
          canvas.height = viewport.height;
          canvas.width = viewport.width;

          let renderContext = {
            canvasContext: context,
            viewport: viewport
          };

          let renderTask = page.render(renderContext);
          renderTask.promise.then(function () {
            // console.log('rendered')
          })
        });
      });
    },

    readFileAsyncFromBlob(blobData) {
      return new Promise((resolve, reject) => {
        let reader = new FileReader();
        reader.onload = e => {
          resolve(reader.result);
        };
        reader.onerror = reject;
        if (this.accept.includes('image')) reader.readAsDataURL(blobData);
        else
          reader.readAsBinaryString(blobData);
      });
    },

    readFileAsync(file) {
      return new Promise((resolve, reject) => {
        let reader = new FileReader();
        reader.onload = e => {
          resolve(reader.result);
        };
        reader.onerror = reject;
        if (file.type.includes('image')) reader.readAsDataURL(file);
        else
          reader.readAsBinaryString(file);
      });
    },

    play() {
      this.memoryData.object.play();
    },

    pause() {
      this.memoryData.object.pause();
    },

    mute() {
      this.memoryData.object.muted = true;
    },

    unmute() {
      this.memoryData.object.muted = false;
    },

    download() {
      if (this.file instanceof File) {

      } else {
        if (`url` in this.file) {
          let link = document.createElement('a');
          link.href = this.file.url;
          link.target = '_blank';
          link.click();
        }
      }
    }
  }
}
</script>

 <style scoped lang="scss"> 
.GridFilePreview {
  position: relative;
  .actions {
    position: absolute;
    bottom: 0;
    right: 10px;
    z-index: 10;
    padding: 2px 3px;
    display: flex;
    flex-flow: row nowrap;
    gap: .5rem;
    justify-content: flex-end;
  }

  canvas {
    width: 100%;
    height: 100%;
  }

  .VideoControls {
    position: absolute;
    bottom: 0;
    width: 100%;
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    justify-content: center;

    button {
      border: none;
      color: #212121;
      font-size: 20px;
      background-color: #ffffff;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      margin: 0.1rem;
      &:hover {
        border: none;
        transform: scale(1.1);
        transition: all .2s ease-in;
      }
    }
  }
}
</style>
