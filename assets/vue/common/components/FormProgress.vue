<template>
  <div class="Progress">
    <div class="Progress--steps--container" :style="{
      'grid-template-columns': `repeat(${ numberOfSteps }, minmax(0, 0.18fr))`,
    }">
      <div
          v-for="step in numberOfSteps"
          class="Progress--steps"
          :id="`form-step-${step}`"
          :class="stepClass(step)">
        <button type="button" class="Progress--steps--step"
                :key="step"
                @click="goToStep(step)"
        >
          <span>{{ step }}</span>
        </button>
        <p @click="goToStep(step)" class="Progress--steps--title cursor-pointer" v-html="getStepTitle(step)" />
      </div>
    </div>

    <div class="Progress--mobile">
      <div class="Progress--mobile--steps">
        <span>{{ currentStep }} / {{ numberOfSteps }}</span>
      </div>
      <div class="Progress--mobile--info">
        <h4 class="Progress--mobile--info--current">{{ getStepTitle(currentStep) }}</h4>
        <h4 class="Progress--mobile--info--next" v-if="currentStep < numberOfSteps">{{ $t('NEXT') }}: {{ getStepTitle(currentStep + 1) }}</h4>
      </div>
    </div>

    <div v-if="showBarProgressPercentage" class="Progress--progress">
      <span class="progress--number" :style="{
        'left': progressValue
      }">{{ progress }}%</span>
      <span class="progress--bar" :style="{
        'width': `${progress}%`
      }"></span>
    </div>
  </div>
</template>

<script>
export default {
  name: "FormProgress",
  props: {
    numberOfSteps: {
      type: Number,
      default: 1
    },
    currentStep: {
      type: Number,
      default: 1
    },
    prefix: {
      type: String,
      default: 'FORM'
    },

    /**
     * Pass a map of custom titles for step with no prefix: eg: 1 => 'HELLO_IS_ME' where 1 is the step number and the value the title
     */
    customTitles: {
      type: Map,
      default: function () {
        return new Map()
      }
    },

    /**
     * @param warning Pass the step number who has a warning
     */
    warning: {
      type: Object,
      default: () => ({})
    },

    i18n: {
      type: Boolean,
      default: true
    },
    showBarProgressPercentage: {
      type: Boolean,
      default: true
    },
    disabledSteps: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      progress: 0
    };
  },
  computed: {
    progressValue() {
      return `${Math.min(97, this.progress - 1)}%`
    }
  },
  watch: {
    currentStep: {
      handler: function (val, oldVal) {
        this.calculateProgress();
      },
      immediate: true
    },
    numberOfSteps() {
      this.calculateProgress();
    }
  },
  mounted() {
    this.calculateProgress();
  },
  methods: {
    calculateProgress() {
      if (this.numberOfSteps > 0) {
        let progress = (this.currentStep * 100) / this.numberOfSteps;
        if ((progress - Math.floor(progress)) > 0) {
          progress = progress.toFixed(0);
        }
        

        this.progress = progress > 100 ? 100 : progress;
        
      }
    },
    getStepTitle(step) {
      let title = '';
      if (this.customTitles.has(step))
        title = this.customTitles.get(step);
      else title = `${this.prefix}.STEP-${step}`;
      return this.i18n ? this.$t(title) : title;
    },
    stepClass(step) {
      let stepClass = '';
      if (this.currentStep >= step) {
        stepClass += ' completed';
      }

      if (step in this.warning && this.warning[step]) {
        stepClass += ' warning';
      }
      
      if (this.disabledSteps.some(id => id === step)) {
        stepClass += ' disabled'
      }
      
      return stepClass;
    },
    goToStep(step) {
      if (this.disabledSteps.some(id => id === step)) return;
      // this.currentStep = step;
      this.$emit('on-step-change', step);
    }
  }
}
</script>

 <style scoped lang="scss"> 
.Progress {
  $color-normal: var(--color-neutral-light);
  $color-completed: var(--color-primary);
  width: 100%;
  display: flex;
  flex-flow: column nowrap;
  align-items: center;

  &--mobile {
    display: none;
    padding: 0 1rem;
    width: 100%;

    @media #{extra-small-screen()} {
      display: grid;
      grid-template-columns: 60px 1fr;
      gap: .5rem;
    }

    &--steps {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      text-align: center;
      border: 5px solid var(--color-primary);
      color: var(--color-neutral-darkest);
      font-weight: bold;
      user-select: none;
    }

    &--info {
      display: flex;
      flex-flow: column;
      align-items: center;
      justify-content: flex-end;

      &--current, &--next {
        width: 100%;
      }

      &--current {
        color: var(--color-primary);
        font-size: 20px;
        text-align: end;
        font-weight: bold;
      }

      &--next {
        color: var(--color-secondary);
        font-size: 16px;
        text-align: end;
        font-weight: normal;
      }
    }
  }



  &--steps--container {
    padding: 0.25rem;
    display: grid;
    gap: 0.25rem;
    align-items: center;
    justify-content: center;
    width: 100%;

    @media #{extra-small-screen()} {
      display: none;
    }
  }

  &--steps {
    width: 100%;
    display: flex;
    flex-flow: column nowrap;
    align-items: center;
    justify-content: center;
    color: var(--color-neutral-mid-darker);

    &--step {
      background-color: var(--color-neutral-light);
      width: 30px;
      height: 30px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      color: var(--color-neutral-mid-darker);
      cursor: pointer;
      font-size: 16px;

      border: none;
      &:hover {
        border: none;
      }
    }

    &--title {
      font-weight: normal;
      white-space: nowrap;
      margin: 0 0 0 0.2rem;
    }
    
    &.disabled {
      opacity: 0.8;
      filter: grayscale(1);
    }

    &.completed {
      p {
        color: var(--color-neutral-darkest);
      }

      button {
        background-color: $color-completed;
        color: var(--color-neutral-lighter);
      }

      .Progress--steps--title {
        color: $color-grey-darkest;
      }
    }

    &.completed.warning, &.warning {
      button {
        background-color: var(--color-secondary-light) !important;
        position: relative;
        color: #ffffff !important;
        &:after {
          content: '!';
          position: absolute;
          color: var(--color-secondary-light) !important;
          right: -0.5rem;
          top: -0.3rem;
        }
      }

      .Progress--steps--title {
        color: var(--color-neutral-mid-darker);
      }
    }
  }

  &--progress {
    width: 100%;
    height: 5px;
    background-color: var(--color-neutral-light);
    position: relative;
    margin-top: 1rem;

    & > span.progress--number {
      position: absolute;
      bottom: 5px;
      color: var(--color-primary);
    }

    & > span.progress--bar {
      background-color: var(--color-primary);
      height: 5px;
      position: absolute;
      left: 0;
    }
  }
}
</style>
