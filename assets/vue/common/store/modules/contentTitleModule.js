/**
 * Share common module for current app/user locale
 */
export default {
    namespaced: true,
    state: {
        contentTitle: [],
        actions: {
            route: '',
            actions: []
        },
        /// If this is true, when creating an app, declare a global event handler
        ///Vue.prototype.$eventBus = new Vue();// Global event bus
        useGlobalEventBus: false
    },
    getters: {
        getContentTitle(state) { return state.contentTitle; },
        getActions(state) { return state.actions },
        getUseGlobalEventBus(state) { return state.useGlobalEventBus; },
    },
    mutations: {
        SET_ADD_ROUTE(state, {routeName, params}) {
            const index = state.contentTitle.findIndex(item => item.routeName === routeName);
            if (index >= 0) {
                state.contentTitle = state.contentTitle.slice(0, index);
            }
            state.contentTitle.push({ routeName, params })
        },
        SET_REPLACE_ROUTE(state, {routeName, params}) {
            const index = state.contentTitle.findIndex(item => item.routeName === routeName);
            console.log('replace ' + index)
            let contentTitle = state.contentTitle;
            if (index >= 0) {
                console.log(state.contentTitle);
                contentTitle[index] = {routeName, params};
            }
            state.contentTitle = contentTitle;
        },

        SET_CONTENT_TITLE(state, contentTitle) {
            state.contentTitle = contentTitle;
        },

        SET_REMOVE_ROUTE_FROM_CONTENT_TITLE(state, routeName) {
            const index = state.contentTitle.findIndex(item => item.routeName === routeName);
            if (index >= 0) state.contentTitle.splice(index, 1);
        },

        SET_ACTIONS(state, { route, actions }) {
            state.actions = {
                route, actions
            };
        },

        SET_CLEAR_ACTIONS(state) {
            state.actions = {
                route: '',
                actions: []
            };
        },

        SET_USE_GLOBAL_EVENT_BUS(state, value = false) {
            state.useGlobalEventBus = value;
        }
    },
    actions: {
        /**
         *
         * @param commit
         * @param routeName Active route name, for handling specific cases
         * @param params [{linkName: 'ValueToDisplay', params: 'Specific route params'}]
         */
        addRoute({ commit }, { routeName, params }) {
            commit("SET_ADD_ROUTE", {routeName, params});
        },

        replaceRoute({ commit }, { routeName, params }) {
            commit("SET_REPLACE_ROUTE", {routeName, params});
        },

        setContentTitle({ commit }, data) {
            commit('SET_CONTENT_TITLE', data);
        },

        /**
         *
         * @param commit
         * @param route Current route for the actions to be valid, otherwise, actions is cleared
         * @param actions [{ name: '', type: '', class: '', 'event': '' }]
         * @constructor
         */
        setActions({ commit }, { route, actions }) {
            commit('SET_ACTIONS', {route, actions});
        },

        useGlobalEventBus({ commit }, value = false) {
            commit('SET_USE_GLOBAL_EVENT_BUS', value);
        },

        clearActions({ commit }) {
            commit('SET_CLEAR_ACTIONS');
        },

        removeRouteFromContentTitle({ commit }, routeName) {
            commit('SET_REMOVE_ROUTE_FROM_CONTENT_TITLE', routeName);
        }
    }
}
