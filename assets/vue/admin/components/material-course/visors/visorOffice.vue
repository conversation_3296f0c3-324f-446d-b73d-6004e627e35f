<template>
  <div class="visorOffice">
   <VueDocPreview :url="urlOffice" type="office" />
  </div>
</template>

<script>
import VueDocPreview from "vue-doc-preview";
export default {
  components: {
    VueDocPreview,
  },
  props: {
    name: {
      type: String,
      default: '',
    },
    base: {
      type: String,
      default: "/uploads/material_course/",
    },
  },

  computed: {
    urlOffice() {
      const domain = window.location.origin;
      return `${domain}${this.base}${this.name}`;
    },
  },
};
</script>

 <style scoped lang="scss"> 
.visorOffice{
  padding: 1rem;
  height: 80vh;
  scroll-behavior: smooth;
  overflow: scroll;
}
</style>
