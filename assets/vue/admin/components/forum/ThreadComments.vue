<template>
  <div
      v-if="currentcomments"
       class="ThreadComments"
  >
    <div
        id="container"
        class="discord-container scroll"
    >
      <div
          v-for="(message, index) in currentcomments"
          :id="'message'+message.id"
          :key="index"
          class="discord-mensaje"
      >
        <div class="discord-mensaje-avatar">
          <img
              v-if="message.user.avatar"
              class="profile"
              :src="loadImageServer(message.user.avatar)"
              alt="user-avatar"
          >

          <img
              v-else
              class="profile"
              :src="`/assets/forum/profile-default.png`"
              alt="user-icon"
          >

          <div>
            #{{ message.id }}
          </div>

          <div class="redlike">
            {{ message.forumLikes.length }} - <i class="fas fa-heart" />
          </div>
        </div>

        <div class="discord-mensaje-content">
          <div>
            <span class="discord-autor-info">
              <span class="discord-mensaje-username">
                {{ message.user.firstName }} {{ message.user.lastName }}
              </span>

              <span class="discord-mensaje-timestamp">
                <div
                    v-if="isResponse(message)"
                    class="discord-response-body"
                >
                  <div class="response-text">
                    <div class="discord-response-text">
                      En respuesta a:
                    </div>

                    <div
                        class="discord-response-id"
                        @click="scrollToComment(message.response.id)"
                    >
                      #{{ message.response.id }}
                    </div>
                  </div>
                </div>

                {{ message.createdAt }}
              </span>
            </span>
          </div>

          <div
              class="discord-mensaje-body"
          >
            <div
                v-if="isGif(message.message)"
                class="gif"
                :style="`background-image: url('${message.message}')`"
            />

            <div
                v-else
                class="discord-mensaje-body"
            >
              {{ message.message }}
            </div>
          </div>

          <span class="discord-respond">
              <span
                  class="discord-mensaje-discord-respond"
              >



                  <BaseButton
                      variation="secondary"
                      size="xs"
                      class="send-button"
                      @click="answer(message.id)"
                  >
                    <i class="fas fa-reply" />

                    <span
                        class="respond-button"
                    >
                      Responder
                    </span>
                </BaseButton>
              </span>
            </span>
        </div>
      </div>
    </div>

    <div
        v-show="show"
        class="giphy h-scroll"
    >
      <search-input @gifs-fetched="onGifsFetched" />

      <gif-list :gifs="gifs" />
    </div>

    <div
        v-show="response"
        class="response-block"
    >
      <input
          ref="textAreaResponse"
          class="response-postnumber-text"
          disabled
      >
      <div>
        <BaseButton
            variation="secondary"
            size="xs"
            class="close-response"
            @click="deactivateResponse()"
        >
          <i class="fas fa-times-circle"/>

          <span
              class="respond-button"
          >
            Cerrar
          </span>
        </BaseButton>
      </div>
    </div>

    <div class="send-area">
      <textarea
          ref="textAreaObj"
          placeholder="Escribe aquí tu comentario"
      />

      <div class="send-add">
        <twemoji-picker
            :emoji-data="emojiDataAll"
            :emoji-groups="emojiGroups"
            :picker-close-on-clickaway="true"
            :skins-selection="false"
            :random-emoji-array="['😀']"
            is-loading-label="Loading..."
            @emojiUnicodeAdded="emojiUnicodeAdded"
        />

        <img
            :src="`/assets/forum/ico-gif.svg`"
            alt="emoji"
            @click="toggle"
        >
      </div>

      <div
          v-if="sendGif"
          class="sendgif"
      />

        <BaseButton
            v-if="!response"
            variation="primary"
            class="send-button"
            @click="sendComment()"
        >
          <span
              class="send"
          >
            Enviar
          </span>

          <i class="fas fa-paper-plane" />
        </BaseButton>

        <BaseButton
            v-else
            variation="primary"
            class="send-button"
            @click="sendResponse()"
        >
          <span
              class="send"
          >
            Responder
          </span>

          <i class="fas fa-paper-plane" />
        </BaseButton>
    </div>
  </div>
</template>

<script>
import { get } from 'vuex-pathify';

import SearchInput from '../gif/SearchInput';
import GifList from '../gif/GifList';
import BaseButton from '../base/BaseButton';

import {
  TwemojiPicker,
} from '@kevinfaguiar/vue-twemoji-picker';
import EmojiAllData from '@kevinfaguiar/vue-twemoji-picker/emoji-data/es/emoji-all-groups.json';
import EmojiGroups from '@kevinfaguiar/vue-twemoji-picker/emoji-data/emoji-groups.json';

export default {

  components: {
    'twemoji-picker': TwemojiPicker,
    GifList,
    SearchInput,
    BaseButton,
  },

  props: {
    currentcomments: {
      type: Array,
      default: null,
    },
  },

  data() {
    return {
      gifs: [],
      show: false,
      response: false,
      responsePostId: undefined,
    };
  },

  computed: {
    ...get('forumModule', ['getGif']),

    emojiDataAll() {
      return EmojiAllData;
    },

    emojiGroups() {
      return EmojiGroups;
    },

    sendGif() {
      if (this.getGif()) {
        this.$refs.textAreaObj.value = this.getGif();
        this.$store.dispatch('forumModule/purgeGif');
        this.purgeGif();
        this.scrollToEnd();
        this.toggle();
      }

      return false;
    },
  },

  watch: {
    // cada vez que la pregunta cambie, esta función será ejecutada
    currentcomments() {
      setInterval(this.scrollToEnd(), 5000);
    },
  },

  updated() {
    // whenever data changes and the component re-renders, this is called.
    this.$nextTick(() => this.scrollToEnd());
  },

  mounted() {
    setInterval(this.scrollToEnd(), 5000);
  },

  methods: {
    scrollToEnd() {
      // scroll to the start of the last message
      const container = this.$el.querySelector('#container');
      container.scrollTop = container.scrollHeight;
    },

    answer(mensaje) {
      this.$refs.textAreaResponse.value = `Respondiendo al comentario #${mensaje}`;
      this.response = true;
      this.responsePostId = mensaje;
    },

    deactivateResponse() {
      this.response = false;
    },

    loadImageServer(image) {
      return `${process.env.VUE_APP_IMG_TRY_DIR}/${image}`;
    },

    sendComment() {
      const text = this.$refs.textAreaObj.value;
      this.$refs.textAreaObj.value = '';
      setInterval(this.scrollToEnd(), 10000);
      this.$emit('saveComments', text);
    },

    sendResponse() {
      const text = this.$refs.textAreaObj.value;
      this.$refs.textAreaObj.value = '';
      setInterval(this.scrollToEnd(), 10000);
      const payload = {
        text,
        response_id: this.responsePostId,
      };
      this.response = false;
      this.$emit('saveComments', payload);
    },

    emojiUnicodeAdded(emojiUnicode) {
      const text = this.$refs.textAreaObj.value;
      this.$refs.textAreaObj.value = text + emojiUnicode;
    },

    onGifsFetched(result) {
      this.gifs = result.data;
    },

    purgeGif() {
      this.gifs = [];
    },

    isGif(comment) {
      if (comment.includes('giphy')) {
        return true;
      }
      return false;
    },

    isResponse(comment) {
      if (comment.response !== null && comment.response) {
        return true;
      }
      return false;
    },

    scrollToComment(id) {
      document.getElementById(`message${id}`).scrollIntoView({
        behavior: 'smooth',
      });
    },

    toggle() {
      this.show = !this.show;
    },
  },
};
</script>

 <style scoped lang="scss"> 
.ThreadComments {
  display: flex;
  flex-direction: column;
  background-color: white;

  height: 46rem;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  //@media #{$breakpoint-md-min} {
  //  border-radius: 0 20px 20px 0;
  //}

  .send{
    margin-right: .5rem;
  }

  .respond-button{
    margin-left: .5rem;
  }

  .discord-respond{
    float: right;
  }

  .gif {
    width: 240px;
    height: 240px;
    margin-bottom: 20px;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
  }

  .sgiphy {
    margin: 0 1rem 2px;
    padding: 1rem;
    background-color: white;
    border-radius: 0.5rem;
  }

  .giphy input {
    border: 1px solid #ccc;
  }

  .h-scroll{
    width: 73rem;
  }

  .response-block{
    display: flex;
    margin: 0 1.5rem 0rem;
    align-items: center;
  }

  .response-postnumber-text{
    width: 30%;
  }

  .discord-mensaje-discord-respond{
    color: hsl(198, 100%, 37%);
    cursor: pointer;
  }

  .redlike{
    color:red;
  }

  .close-response{
    color:hsl(0, 100%, 62%);;
  }

  .discord-container {
    color: var(--light-color-1);

    font-size: 16px;
    font-family: sans-serif;
    line-height: 170%;
    margin: 1rem;
    background-color: white;
    border-radius: 0.5rem;
    flex: 1;
  }

  .scroll {
    overflow-y: auto;
    /* height: 40rem;*/
    padding-right: 0.5rem;
    scrollbar-width: thin;
    scrollbar-color: orange transparent;
  }

  div::-webkit-scrollbar {
    width: 12px;
  }

  div::-webkit-scrollbar-track {
    background-color: #36393e;
  }

  div::-webkit-scrollbar-thumb {
    background-color:  hsl(172, 38%, 57%);
    border-radius: 20px;
  }

  .discord-mensaje {
    color:  hsl(198, 98%, 25%);
    background-color: rgba(228, 220, 220, 0.95);
    border-radius: 5px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    font-size: 0.9em;
    margin: 1em 0;
    padding: 1em 1.5em 1rem;
  }

  .discord-mensaje-avatar {
    margin-top: 1px;
    margin-right: 16px;
    width: 55px;
    img {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      margin-bottom: .5rem;
    }

    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .discord-mensaje-content {
    width: 100%;
    line-height: 160%;
    font-weight: normal;
    overflow-wrap: anywhere;
  }

  .discord-autor-info {
    color: hsl(198, 100%, 37%);
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: self-start;
    justify-content: space-between;
    font-size: 15px;
  }

  .discord-mensaje-username {
    font-size: 1.1em;
    font-weight: 500;
    letter-spacing: 0.5px;
    font-weight: bold;
    color: hsl(198, 100%, 37%);;
  }

  .discord-mensaje-timestamp {
    color: #7b7b7b;
    font-size: 12px;
    margin-left: 3px;
    float: right;
    display:flex;
  }

  .discord-mensaje-body {
    position: relative;
    //font-family: var(--font-family-primary);
    //font-size: var(--font-size-s);
    color:hsl(198, 98%, 25%);
  }

  .discord-response-body{
    position: relative;
    color: hsl(198, 100%, 37%);
    margin-right: .5rem;
  }

  .response-text{
    display: flex;
  }

  .discord-response-text{
    color: hsl(198, 98%, 25%);
    margin-right: 0.2rem;
    font-weight: lighter;
    font-style: italic;
  }

  .discord-response-id{
    cursor: pointer;
  }

  textarea {
    flex: 1 1 auto;
    height: 2.5rem;
    padding: 0.7rem 0.7rem 0.2rem;
    color: hsl(198, 100%, 37%);;
    border-radius: 0.5rem 0 0 0.5rem;
    resize: none;
    border: none;
    width: 10px;
    background-color: #ebebeb;
    &:focus {
      outline: none;
      box-shadow: inset 0 0 2px 2px rgb(199, 215, 238);
    }
  }

  .send-area {
    display: flex;
    align-items: center;
    margin: 0 1rem 1rem;
    height: 2.5rem;
  }
  .send-add {
    flex: 0 0 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 2.5rem;
    background-color: #ebebeb;
    img {
      width: 30px;
      cursor: pointer;
    }
  }
  .send-button {
    height: 2.5rem;
    &:hover {
      background-color: hsl(210, 14%, 97%);
    }
    i {
      margin-left: 0;
      //@media #{$breakpoint-md-min} {
      //  margin-left: 0.5rem;
      //}
    }
  }
  span {
    //display: none;
    //@media #{$breakpoint-md-min} {
    //  display: inline;
    //}
  }
  img {
    max-height: 70px;
    clip-path: circle();
  }

  img.profile {
    max-height: 50px;
    clip-path: circle();
  }
}
</style>
