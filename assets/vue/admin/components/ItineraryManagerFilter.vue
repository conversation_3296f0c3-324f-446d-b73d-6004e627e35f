<template>
  <div class="col-lg-12 " display="flex">
    <div class="col align-self-end itinerary-manager-filter-action" display="flex" style="text-align:right; margin-top: 10px; margin-bottom: 10px;" >
        <!-- Button trigger modal -->
        <button
            type="button"
            class="btn btn-primary"
            data-bs-toggle="modal"
            data-bs-target="#assignManagerModal"
          >
          <i class="fa fa-edit"></i>
          {{useI18n
                          ? $t(prefix + "ASSIGN_MANUAL")
                          : translations.assign_manually
          }}
        </button>

        <!-- Modal -->
        <div
          class="modal fade modal-add-users"
          id="assignManagerModal"
          tabindex="-1"
          aria-labelledby="assignManagerModalLabel"
          aria-hidden="true"
          >
            <div class="modal-dialog">
              <div class="modal-content">
                <div class="modal-header col-12 d-flex justify-content-center">
                  <h5 class="modal-title" id="assignManagerModalLabel">{{useI18n ? $t(prefix + "ASSIGN_MANUAL") : translations.modify_users }}</h5>
                  <button
                    type="button"
                    class="btn-close"
                    data-bs-dismiss="modal"
                    aria-label="Close"
                  ></button>
                </div>
                <div class="modal-body"> <!-- MODAL BODY -->
                  <div class="mb-3 mt-1" >
                    <div class="card-body p-0 my-3">
                      <div class="row">
                        <div class="col-md-12 input-group mb-2 mr-sm-2">
                          <input
                            v-model="userSearchQuery"
                            type="text"
                            class="form-control"
                            id="search"
                            v-bind:placeholder="
                              useI18n
                                ? $t(prefix + 'SEARCH_USER')
                                : translations.placeholder_search_user
                            "
                            aria-describedby="btn-filter"
                            v-on:keyup.enter="searchUsers()"
                          />
                          <div class="input-group-append">
                            <span
                              class="input-group-text btn h-100"
                              id="btn-filter"
                              @click="showFilters = !showFilters"
                              ><i class="fa fa-filter"></i>
                              {{useI18n ? $t(prefix + "ASSIGN_FILTER") : translations.filters }}
                            </span>
                          </div>
                        </div>

                        <div
                          v-if="showFilters && loadingFilters"
                          class="col-12 d-flex justify-content-center"
                        >
                          <loader
                            :is-loaded="loadingFilters"
                            class="ml-auto mr-auto"
                          ></loader>
                        </div>

                        <template v-if="showFilters">
                          <div
                            class="col-md-6 mb-2"
                            v-for="(filter, index) in filters"
                            :key="index"
                          >
                            <Multiselect
                              v-model="filterQueries[filter.name]"
                              :options="filter.filters"
                              :multiple="true"
                              :placeholder="
                                useI18n
                                  ? $t('FIND_BY', [filter.name])
                                  : `${
                                      translations.find_by !== undefined
                                        ? translations.find_by
                                        : translationBackup.find_by
                                    } ${filter.name}`
                              "
                              track-by="name"
                              label="name"
                            ></Multiselect>
                          </div>
                        </template>

                        <div
                          v-if="showFilters && !loadingFilters"
                          class="row col-12 d-flex align-items-center justify-content-center"
                        >
                          <div class="col-3 d-flex justify-content-left">
                            <span><b>{{
                                useI18n
                                  ? $t("RESULTS_WITH_VALUE", [availableUsers.length])
                                  : translations.result_found + ": " + availableUsers.length
                              }}</b>
                            </span>
                          </div>
                          <div class="col-6 d-flex justify-content-center">
                              <button type="button" class="btn btn-danger mr-1" data-dismiss="modal" @click="clearUsers()">
                                    <i class="fa fa-brush"></i>
                                    {{ useI18n ? $t("CLEAR_RESULTS") : translations.clear_result }}
                              </button>
                              <button class="btn btn-primary ml-1" @click="searchUsers()">
                                <i class="fa fa-check"></i>
                                {{ useI18n ? $t("APPLY_FILTERS") : translations.apply_filters }}
                              </button>
                          </div>
                          <div class="col-3 d-flex justify-content-left">
                            
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="control-panel py-3">

                      <div  class="row users-container m-0 min-vh-100">
                          <div class="col-md-5 max-height-70 ">
                          
                            <div
                              class="available-users col-md-12 h-100 max-height-70"
                            >
                              <div v-if="searchingUsers" class="col-md-12 d-flex">
                                <loader
                                  :is-loaded="searchingUsers"
                                  class="ml-auto mr-auto"
                                ></loader>
                              </div>

                              <div
                                v-else
                                class="card"
                                v-for="(user, index) in availableUsers"
                                :key="user.id"
                              >
                                <div class="card-information">
                                  <span class="card-firstName"
                                    >{{ user.firstName }} {{ user.lastName }}</span
                                  >
                                  <span class="card-email">{{ user.email }}</span>
                                </div>

                                <button @click="add(index)" class="ml-auto btn btn-primary add-action btn-sm ">
                                  <i class="fa fa-plus"></i>
                                </button>
                              </div>
                            </div>
                          </div>
                          <div
                            class="col-md-2 action max-height-70 p-2">
                            <div
                              v-if="showAddRemoveLoader"
                              class="w-100 mb-auto d-flex align-items-center justify-content-center"
                            >
                              <loader :is-loaded="showAddRemoveLoader"></loader>
                            </div>
                            <div class="w-100 d-flex mt-auto mb-auto flex-column">
                              <button class="mt-1 btn btn-primary" @click="addAll()">
                                {{ useI18n ? $t("ADD_ALL") : translations.add_all }}
                                <i class="fas fa-angle-double-right ml-2"></i>
                              </button>
                              <button class="mt-1 btn btn-danger" @click="removeAll()">
                                <i class="fas fa-angle-double-left mr-2"></i>
                                {{ useI18n ? $t("REMOVE_ALL") : translations.remove_all }}
                              </button>
                            </div>
                          </div>

                          <div
                            class="selected-users max-height-70 col-md-5"
                          >
                            <div class="card" v-for="(user, index) in users" :key="user.id">
                              <div class="card-information">
                                <span class="card-firstName"
                                  >{{ user.firstName }} {{ user.lastName }}</span
                                >
                                <span class="card-email">{{ user.email }}</span>
                              </div>

                              <button
                                class="btn btn-sm btn-danger delete-action "
                                @click="removeUser(index)"
                              >
                                <i class="fa fa-minus"></i>
                              </button>
                            </div>

                            <div v-if="users.length === 0">
                              <span>{{ useI18n ? $t("NO_USERS") : translations.no_users }}</span>
                            </div>
                          </div>        
                      </div>
                    </div>

                </div> <!-- MODAL BODY -->

                <div class="modal-footer">
                  <!--
                  <button
                    type="button"
                    class="btn btn-secondary"
                    data-bs-dismiss="modal"
                    @click="$emit('cancel-edition');"
                  >
                    <i class="fa fa-edit"></i>
                        {{
                          useI18n
                              ? $t("CANCEL")
                              : translations.cancel
                        }}
                  </button>
                  <button type="button" class="btn btn-primary">{{ useI18n ? $t("CANCEL") : translations.save }}</button>
                -->
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal"
                  >
                    {{ useI18n ? $t("CLOSE") : translations.close }}
                  </button>
                </div>
              </div>
            </div>
          </div> 
    </div>
    <div class="col-md-12 custom-content p-0" v-if="showCustomContent">
        <slot name="custom-content"></slot>
        <jw-pagination
              :items="paginatedUsers"
              v-on:changePage="onChangePage"
              :labels="paginationCustomLabels"
            ></jw-pagination>
      </div>
  </div>
</template>

<script>
import Multiselect from "vue-multiselect";
import Loader from "./Loader";
import axios from "axios";
import { get } from "vuex-pathify";
import store from "../store";
import JwPagination from "jw-vue-pagination";

import VueToast from "vue-toast-notification";
import "vue-toast-notification/dist/theme-sugar.css";
import VueAlertify from "vue-alertify";

export default {
  name: "ItineraryUserFilter",
  components: {
    Loader,
    Multiselect,
    JwPagination,
    VueToast,
    VueAlertify,
  },
  store,
  computed: {
    ...get("userFilterModule", []),
    paginationCustomLabels() {
      return {
        first: "<<",
        last: ">>",
        previous: "<",
        next: ">",
      };
    },
    prefix() {
      return this.i18nPrefix.length > 0 ? `${this.i18nPrefix}.` : "";
    },
  },
  props: {
    useRestMethods: {
      type: Boolean,
      default: false,
    },

    id: {},
    paginatedUsers: {},
    translations: {},
    showCustomContent: {
      type: Boolean,
      default: false,
    },
    urlGetFilters: {
      type: String,
      default: null,
    },
    urlSearchUsers: {
      //Url to find users
      type: String,
      default: null,
    },
    urlGetUsers: {
      //Load selected users
      type: String,
      default: null,
    },
    urlAddUser: {
      type: String,
      default: null,
    },
    urlAddAll: {
      type: String,
      default: null,
    },
    urlRemoveUser: {
      type: String,
      default: null,
    },
    urlRemoveAll: {
      type: String,
      default: null,
    },

    useI18n: {
      type: Boolean,
      default: false,
    },
    i18nPrefix: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      showFilters: false,
      loadingFilters: false,
      filters: [],
      pageOfUsers: [],
      translationsTemp: {
        no_users: "No users have been added to the itinerary",
        cancel: "Cancel",
        modify_users: "Modify Users",
        assign_manual: "Assign manually",
        assign_filters: "Assign by Filters",
        placeholder_search_user: "Search for users",
        apply_filters: "Apply filters",
        result_found: "Results found",
        clear_result: "Clear Results",
        add_all: "Add All",
        remove_all: "Remove All",
        find_by: "Find by",
      },
      filterQueries: {},
      userSearchQuery: "",
      showAddRemoveLoader: false,

      searchingUsers: false,
      loadingUsers: false,
      users: [],
      availableUsers: [],
    };
  },
  async created() {
    await this.getUsers();
    await this.getFilters();
    await this.searchUsers();
  },
  methods: {
    onChangePage(pageOfUsers) {
      this.pageOfUsers = pageOfUsers;
      this.$emit("on-change-page", pageOfUsers);
    },
    async getFilters() {
      if (this.urlGetFilters === undefined) return;
      this.loadingFilters = true;
      try {
        await axios.get(this.urlGetFilters).then((r) => {
          this.filters = r.data.data.filters;
        });
      } finally {
        this.loadingFilters = false;
      }
    },

    async searchUsers() {
      try {
        this.searchingUsers = true;
        await axios
          .post(this.urlSearchUsers, {
            searchQuery: this.userSearchQuery,
            filters: this.filterQueries,
          })
          .then((r) => {
            if (!r.data.error) {
              this.availableUsers = r.data.data.users;
            }
          });
      } finally {
        this.searchingUsers = false;
      }
    },

    clearUsers() {
      this.availableUsers = [];
      this.userSearchQuery = "";
      this.filterQueries = [];
    },

    async getUsers() {
      this.loadingUsers = true;
      try {
        await axios.post(this.urlGetUsers, { id: this.id }).then((r) => {
          if (!r.data.error) {
            this.users = r.data.data.users;
          }
        });
      } finally {
        this.loadingUsers = false;
      }
    },

    async add(index) {
      if (this.urlAddUser == null) {
        return;
      }

      this.showAddRemoveLoader = true;
      try {
        function addUser(useRest = false, url, user_id) {
          if (useRest) return axios.post(`${url}/${user_id}`);
          else return axios.post(url, { user_id });
        }

        let user = this.availableUsers[index];
        addUser(this.useRestMethods, this.urlAddUser, user.id)
          .then((res) => {
            const { message, error } = res.data;

            if (error) {
              this.$toast.error(
                message ??
                  (this.useI18n
                    ? this.$t(`${this.prefix}ADD_USER.ERROR`, [user.email])
                    : this.translations?.add?.error)
              );
            } else {
              this.$toast.success(
                message ??
                  (this.useI18n
                    ? this.$t(`${this.prefix}ADD_USER.SUCCESS`, [user.email])
                    : this.translations?.add?.success)
              );
              this.availableUsers.splice(index, 1);
              this.users.push(user);
              this.usersUpdated();
            }
          })
          .finally(() => {
            this.showAddRemoveLoader = false;
          });
      } finally {
        // this.showAddRemoveLoader = false;
      }
    },

    async addAll() {
      this.showAddRemoveLoader = true;
      try {
        const ids = this.availableUsers.map((user) => user.id);

        await axios
          .post(this.urlAddAll, {
            id: this.id,
            user_ids: ids,
          })
          .then((r) => {
            if (!r.data.error) {
              this.users = [...this.users, ...this.availableUsers];
              this.availableUsers = [];
              this.$toast.success(r.data.message);
              this.usersUpdated();
            }
          });
      } finally {
        this.showAddRemoveLoader = false;
      }
    },

    async removeUser(index) {
      this.showAddRemoveLoader = true;
      try {
        function remove(useRest = false, url, user_id) {
          if (useRest) return axios.delete(`${url}/${user_id}`);
          else return axios.post(url, { user_id });
        }

        const user = this.users[index];
        remove(this.useRestMethods, this.urlRemoveUser, user.id)
          .then((res) => {
            const { message, error } = res.data;
            if (error) {
              this.$toast.error(
                message !== undefined
                  ? message
                  : this.useI18n
                  ? this.$t(`${this.prefix}REMOVE_USER.ERROR`, [user.email])
                  : this.translations?.remove?.error
              );
            } else {
              this.$toast.success(
                message !== undefined
                  ? message
                  : this.useI18n
                  ? this.$t(`${this.prefix}REMOVE_USER.SUCCESS`, [user.email])
                  : this.translations?.remove?.success
              );
              this.users.splice(index, 1);
              this.usersUpdated();
            }
          })
          .finally(() => {
            this.showAddRemoveLoader = false;
          });
      } finally {
      }
    },

    async removeAll() {
      this.$alertify.confirmWithTitle(
        this.useI18n
          ? this.$t(`${this.prefix}REMOVE_ALL.CONFIRM.TITLE`)
          : this.translations.confirm_delete_all.title,
        this.useI18n
          ? this.$t(`${this.prefix}REMOVE_ALL.CONFIRM.DESCRIPTION`)
          : this.translations.confirm_delete_all.subtitle,
        async () => {
          this.showAddRemoveLoader = true;
          try {
            await axios
              .post(this.urlRemoveAll, {
                id: this.id,
              })
              .then((r) => {
                if (!r.data.error) {
                  this.users = [];
                  this.searchUsers();
                  this.$toast.success(r.data.message);
                  this.usersUpdated();
                }
              });
          } finally {
            this.showAddRemoveLoader = false;
          }
        },
        () => {}
      );
    },

    usersUpdated() {
      this.$emit("users-updated");
    },
  },
};
</script>
<style src="vue-multiselect/dist/vue-multiselect.min.css"></style>
<style scoped lang="scss">
@import "../assets/config/_transitions.scss";

.users-container {
  .available-users.show {
    display: grid;
    max-height: 70vh;
  }

  .hide {
    display: none;
  }

  .action.show {
    display: flex;
    flex-flow: column;
    align-items: center;
    justify-content: center;
  }

  .max-height-70 {
    max-height: 70vh;
    overflow-y: auto;
    overflow-x: hidden;
  }

  .available-users,
  .selected-users {
    border: 1px solid var(--color-neutral-mid-light);
    border-radius: 5px;
    padding: 0.5rem;
    background: var(--color-neutral-lighter);
  }

  .available-users,
  .selected-users {
    display: grid;
    gap: 0.25rem;
    align-content: flex-start;

    .card {
      padding: 0.75rem;
      flex-direction: row;
      align-items: center;

      .card-information {
        display: grid;
        flex: 1;
        gap: 0.125rem;
        font-size: 0.9rem;

        .card-firstName {
          font-weight: 500;
        }

        .card-email {
          font-style: italic;
          color: var(--color-neutral-dark);
        }
      }
    }
  }
}

.custom-content {
  display: grid;
  .pagination {
    justify-self: flex-end;
  }

  @media screen and (max-width: 768px) {
    overflow: auto;
  }
}
</style>
