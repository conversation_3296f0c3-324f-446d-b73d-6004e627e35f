<template>
  <div class="add-question">
    <!--    <p><b>{{ translationsVue.games_categorize }} </b></p>-->
    <div class="categorize-form">
      <div class="form-question-input">
        <div class="mb-3">
          <BaseTextTarea
            :label="translationsVue.quiz_configureFields_question"
            :max="39"
            :value.sync="questionValue"
            :placeholder="
              translationsVue.quiz_configureFields_question_placeholder
            "
            :required="false"
            :rows="3"
          ></BaseTextTarea>
        </div>

        <div>
          <label for="title" class="form-label"
            >{{ translationsVue.games_text_common_time }}
          </label>

          <BaseInputTime
            v-model="time"
            :options="['minutes', 'seconds']"
            :maxMinutes="31"
            :time="time"
            @time-update="timeUpdate"
          />
        </div>
      </div>

      <div class="image-question">
        <label>{{ translationsVue.games_text_common_ilustre_question }}</label>
        <div
          :style="{ backgroundImage: 'url(' + currentImage + ')' }"
          :class="
            image == null && questionCopy.image == null
              ? 'preview-image preview-image-default'
              : 'preview-image'
          "
          @click="$refs.inputFile.click()"
        ></div>

        <div class="mb-3 mt-2">
          <input
            type="file"
            @change="loadImage($event)"
            accept="image/*"
            ref="inputFile"
          />

          <a class="btn-sm btn btn-primary" @click="$refs.inputFile.click()">
            <i class="fas fa-upload"></i>
            {{ translationsVue.games_text_common_select_image }}
          </a>

          <a class="btn-sm btn btn-danger" @click="removeImage()">
            <i class="fas fa-trash-alt"></i>
          </a>
        </div>
      </div>
    </div>

    <div class="optiones mt-3">
      <label>
        {{ translationsVue.categorize_configureFields_title_group }}</label
      >
      <div
        class="row mt-2 pr-4"
        v-for="(category, index) in question.categorizeAnswers"
        :key="category.id"
      >
        <div class="col-md-12">
          <div class="form-check row text-break">
            <input
              class="form-check-input"
              type="checkbox"
              :id="`inlineCheckbox${category.id}`"
              value="option1"
              v-model="category.correct"
              @input="checkSelected(index)"
            />
            <label
              class="form-check-label"
              :for="`inlineCheckbox${category.id}`"
              >{{ category.options.name }}
            </label>
          </div>
        </div>
      </div>
    </div>

    <div class="text-center mt-5" v-if="!processSave">
      <button
        v-show="0"
        type="button"
        class="btn btn-secondary"
        data-bs-dismiss="modal"
        ref="closeChapterContentModal"
      ></button>

      <button class="btn btn-primary btn-sm" @click="editQuestion()">
        {{ translationsVue.Save }}
      </button>
    </div>
    <div v-else class="text-center">
      <Spinner />
    </div>
  </div>
</template>

<script>
import { get } from "vuex-pathify";
import Spinner from "../base/Spinner";

import { modalMixin } from "../../../mixins/modalMixin";
import { alertToastMixin } from "../../../mixins/alertToastMixin";

export default {
  components: {
    Spinner,
  },

  props: {
    categories: {
      type: Array,
      required: true,
    },

    question: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      questionValue: "",
      selectedCategory: "",
      image: null,
      preview: "/assets/common/add_image_file.svg",
      translationsVue,
      chapterId,
      time: "00:00:30",
      option: null,
      answersDeleted: [],
      processSave: false,
    };
  },

  mixins: [modalMixin, alertToastMixin],

  computed: {
    ...get("questionsGamesModule", ["isLoading", "getRouteChapter"]),

    routeChapter() {
      return this.getRouteChapter();
    },

    currentImage() {
      if (this.image) {
        return this.preview;
      }

      return this.getQuestionImage ?? this.preview;
    },

    getQuestionImage() {
      return this.question.image
        ? "/uploads/games/categorize/" + this.question.image
        : null;
    },

    questionCopy() {
      return JSON.parse(JSON.stringify(this.question));
    },
  },

  created() {
    this.getTime(this.questionCopy?.time);
    this.questionValue = this.question.question
  },

  methods: {
    checkSelected(index) {
      this.question.categorizeAnswers.forEach((ans, i) => {
        if (i != index) {
          ans.correct = false;
        }
      });
    },

    deletedCategory(index) {
      if (this.question.categorizeAnswers.length > 2) {
        this.answersDeleted.push(this.question.categorizeAnswers[index]);
        this.question.categorizeAnswers.splice(index, 1);
      }
    },

    loadImage(event) {
      const input = event.target;

      if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = (e) => {
          this.preview = e.target.result;
        };

        this.image = input.files[0];

        reader.readAsDataURL(input.files[0]);
      }
    },

    async editQuestion() {
      const verify = this.questionCopy.categorizeAnswers.filter(
        (category) => category.correct
      );

      if (
        verify.length === 1 &&
        (this.questionValue !== "" || this.currentImage != null)
      ) {
        this.sendQuestionFormData();

        this.$refs["closeChapterContentModal"].click();

        this.fetchQuestions();

        this.alertSuccesSave();
      } else {
        this.$toast.open({
          message: this.translationsVue.games_validate_add_categorize,
          type: "info",
          duration: 5000,
          position: "top-right",
        });
      }
    },

    async sendQuestionFormData() {
      const time = this.time;
      const seconds = time.split(":");
      const secondsTime =
        +seconds[0] * 60 * 60 + +seconds[1] * 60 + +seconds[2];

      const formData = new FormData();
      formData.append("id", this.questionCopy.id);
      formData.append("question", this.questionValue);
      formData.append("image", this.image);
      formData.append(
        "answers",
        JSON.stringify(this.questionCopy.categorizeAnswers)
      );
      formData.append("answersDeleted", JSON.stringify(this.answersDeleted));
      formData.append("idChapter", this.chapterId);
      formData.append("time", secondsTime);

      this.processSave = true;
      await this.$store.dispatch(
        "questionsGamesModule/editCategories",
        formData
      );
      this.processSave = false;
    },

    async fetchQuestions() {
      await this.$store.dispatch(
        "questionsGamesModule/fetchCategories",
        this.chapterId
      );
    },

    removeImage() {
      this.question.image = null;
      this.image = null;
      this.preview = "/assets/common/add_image_file.svg";
    },

    getTime(time) {
      const hours = Math.floor(time / 3600);
      const minutes = Math.floor((time - hours * 3600) / 60);
      const seconds = Math.floor(time - hours * 3600 - minutes * 60);

      const minutesFormat = minutes < 10 ? "0" + minutes : minutes;
      const secondsFormat = seconds < 10 ? "0" + seconds : seconds;
      const hoursFormat = hours < 10 ? "0" + hours : hours;

      this.time = hoursFormat + ":" + minutesFormat + ":" + secondsFormat;
    },

    timeUpdate(time) {
      this.time = time;
    },
  },
};
</script>

 <style scoped lang="scss"> 
.add-question {
  .categorize-form {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 1rem;
    .form-question-input {
      flex: 1;
    }
  }
  .preview-image {
    width: 250px;
    height: 181px;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    cursor: pointer;
  }
  .preview-image-default {
    background-size: 30%;
  }

  input[type="file"] {
    display: none;
  }

  .form-check-input:checked {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
  }
}
</style>
