<template>
  <div class="categories">
    <h3 class="title">
      <b>{{ translationsVue.categorize_configureFields_title_group }}</b>

      <button
        class="btn btn-primary add-category-button"
        @click="addNewCategory"
        v-if="isAddButtonAvailable"
      >
        <i class="fas fa-plus"></i>
        <span>{{ translationsVue.games_add_categories }}</span>
      </button>
    </h3>

    <div class="list-categories mt-2" v-if="categories">
      <CategoryCard
        v-for="(category, i) in visibleCategories"
        :key="i"
        v-model="visibleCategories[i]"
        :is-erasable="isErasable()"
        @remove="removeCategory(i)"
        @add="saveCategory(i)"
      ></CategoryCard>
    </div>
  </div>
</template>

<script>
import { get } from "vuex-pathify";
import CategoryCard from "./CategoryCard.vue";

const MINIMUM_CATEGORIES = 2;
const DEFAULT_CATEGORY = { name: "", image: null };

export default {
  components: {
    CategoryCard,
  },

  /*  props: {
    categories: {
      type: Array,
      required: true,
    },
  }, */

  data() {
    return {
      visibleCategories: [],
      chapterId,
      translationsVue,
    };
  },

  watch: {
    /*  categories: {
      deep: true,
      handler() {
        this.setVisibleCategories();
      },
    }, */
    isAddQuestionButtonAvailable: {
      immediate: true,
      handler(value) {
        this.$emit("question-button-available", value);
      },
    },
  },

  async mounted() {
    await this.fetchCategories();
    this.setVisibleCategories();
  },

  computed: {
    ...get("questionsGamesModule", ["isLoading", "getCategoriesOptions"]),

    categories() {
      const categories = this.getCategoriesOptions();

      const result = categories?.map((category) => {
        return {
          id: category.id,
          name: category.name,
          image: category.image,
          selected: false,
          isErasable: category.eresable,
        };
      });

      return result;
    },

    isAddQuestionButtonAvailable() {
      if (this.visibleCategories?.length < 2) return false;

      const filledFields = this.visibleCategories.every(
        (c) => c.name || c.image
      );
      return filledFields;
    },

    isAddButtonAvailable() {
      if (this.visibleCategories?.length < 2) return false;

      const filledFields = this.visibleCategories.every((c) => c.name);
      return filledFields;
    },
  },

  methods: {
    async fetchCategories() {
      await this.$store.dispatch(
        "questionsGamesModule/fetchCategoriesOptions",
        this.chapterId
      );

      await this.$store.dispatch(
        "questionsGamesModule/fetchCategories",
        this.chapterId
      );
    },

    setVisibleCategories() {
      this.visibleCategories = this.fillToMinimumCategories(this.categories);
    },

    fillToMinimumCategories(categories) {
      while (categories.length < MINIMUM_CATEGORIES) {
        categories.push({ ...DEFAULT_CATEGORY });
      }

      return categories;
    },

    isErasable() {
       if(this.visibleCategories.length > 2){
        return true;
      }
      return false; 
    /// return i >= MINIMUM_CATEGORIES && this.visibleCategories[i]?.isErasable;
    },

    addNewCategory() {
      this.visibleCategories = [...this.visibleCategories, DEFAULT_CATEGORY];
    },

    async removeCategory(i) {
      const formData = new FormData();
      const category = this.visibleCategories[i];
      const categoryId = category?.id;
      formData.append("id", categoryId);
      formData.append("chapter", this.chapterId);

      if (categoryId) {
        await this.deleteCategory(formData);

        this.setVisibleCategories();
      } else {
        this.visibleCategories.splice(i, 1);
      }

      await this.fetchCategories();
      this.setVisibleCategories();
    },

    async saveCategory(i) {
      const formData = new FormData();
      const category = this.visibleCategories[i];
      const categoryId = category?.id;
      formData.append("image", category?.image);
      formData.append("name", category?.name);
      formData.append("chapter", this.chapterId);      
      
      if (categoryId) {
        formData.append("id", categoryId);
        await this.saveExistingCategory(formData);
      } else {
        if (!category.name.length && !category.image.length) return;
        await this.saveNewCategory(formData);
      }
    },

    async saveNewCategory(formData) {
      await this.$store.dispatch(
        "questionsGamesModule/newCategoriesOptions",
        formData
      );
    },

    async saveExistingCategory(formData) {
      await this.$store.dispatch(
        "questionsGamesModule/editCategoriesOptions",
        formData
      );
    },

    async deleteCategory(formData) {
      await this.$store.dispatch(
        "questionsGamesModule/deleteCategoryOptions",
        formData
      );

      await this.fetchCategories();
    },
  },
};
</script>
 <style scoped lang="scss"> 
.categories {
  padding: $spacing-m;

  .title {
    display: flex;
    align-items: center;
    border-bottom: 1px solid;
    margin-bottom: $spacing-m;
    padding-bottom: $spacing-xs;
    font-size: var(--font-size-xl);
    .add-category-button {
      margin-left: auto;
    }
  }

  .list-categories {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: $spacing-m;
  }
}
</style>
