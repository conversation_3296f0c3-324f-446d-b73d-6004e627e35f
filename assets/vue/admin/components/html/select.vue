<template>
  <div class="BaseSelect">
    <label v-bind:for="labelID">{{ label }}</label>
    <div>
      <select v-bind:id="labelID" v-model="localValue" class="form-control">
        <option value="">{{ empty }}</option>

        <option
            v-for="(item, i) in list"
            :key="i"
            v-bind:value="item.id"
        >
          {{ item.name }}
        </option>
      </select>

    </div>
  </div>
</template>

<script>

export default {
  name : 'custom-select',
  props: ['value', 'empty', 'list', 'label'],
  model: {
    prop : 'value',
    event: 'change'
  },

  data() {
    return {
      localValue: this.value,
      labelID   : '',
    }
  },
  created() {
    this.labelID = 'inputDate' + Math.floor((Math.random() * 1000))
   
  },

  watch: {
    localValue(value) {
      this.$emit('change', value)
    },

    value(newValue) {
      this.localValue = newValue
    }
  }
}
</script>

<style scoped lang="scss">
.BaseSelect {
  label {
    text-transform: uppercase;
    font-weight: 500;
    padding: 0 .5rem;
    margin-bottom: .25rem;
  }

  .form-control {
    background-color: #FFFFFF;
    border: 1px solid #E5E5E5;
    border-radius: 50px;
  }
}
</style>
