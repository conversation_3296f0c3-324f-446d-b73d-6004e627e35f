<!-- Vue component -->
<template>
  <div class="BaseSelect">
    <label v-bind:for="labelID">{{label}}</label>
    <input v-bind:id="labelID" type="date" v-model="localValue" autocomplete="off" class="form-control" @change="emitChange"
           :disabled="this.localValue !== ''"/>
  </div>
</template>

<script>
export default {
  name : 'input-date',
  props: ['value', 'label'],
  model: {
    prop: 'value',
    event: 'change'
  },
  data () {
    return {
      localValue: this.value,
      labelID : '',
    }
  },
  created() {
    this.labelID = 'inputDate' + Math.floor((Math.random() * 1000))
  },
  methods: {
    emitChange: function () {
      let date = new Date(String(this.localValue) + ' 00:00');
      if (isNaN(date.getTime()) || date.getTime() < 0 || date.getTime() >= 7289654400000) // permitir desde 1970/01/01 hasta 2200/12/31
        this.localValue = '';
      else {
        this.$emit('change', this.formatDate(date));
      }
    },
    formatDate: function (date) {
      return date.getFullYear() + '-' + String(date.getMonth() + 1).padStart(2, '0') + '-' + String(date.getDate()).padStart(2, '0');
    }
  },
  watch: {
    value(newValue) {
      this.localValue = newValue
    }
  }
}
</script>

<style scoped lang="scss">
.BaseSelect{
  position: relative;

  label{
    text-transform: uppercase;
    font-weight: 500;
    padding: 0 .5rem;
    margin-bottom: .25rem;
  }

  .form-control{
    background-color: #FFFFFF;
    border: 1px solid #E5E5E5;
    border-radius: 50px;
  }
}
</style>
