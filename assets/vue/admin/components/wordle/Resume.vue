<template>
  <tr>
    <td>{{ block.question }}</td>
    <td>{{ block.word }}</td>
    <td>{{ getTime(block.time) }}</td>
    <td class="text-right">
      <button
        type="button"
        class="btn-sm btn btn-danger"
        data-bs-toggle="modal"
        :data-bs-target="`#deleteModal${block.id}`"
      >
        <i class="fas fa-trash-alt"></i>
      </button>

      <button
        class="btn btn-primary btn-sm"
        @click="modifyLine"
        data-bs-toggle="modal"
        data-bs-target="#editQuestion"
      >
        <i class="fa fa-edit"></i>
      </button>

      <BaseModalDelete
        :identifier="`deleteModal${block.id}`"
        :title="translationsVue.quiz_configureFields_question_delete"
        @delete-element="deleteLine"
      />
    </td>
  </tr>
</template>

<script>
export default {
  props: {
    block: {
      type: Object,
      default: () => ({}),
    },
  },

  data() {
    return {
      translationsVue,
    };
  },

  methods: {
    modifyLine() {
      const data = {
        id: this.block.id,
        question: this.block.question,
        time: this.block.time,
        word: this.block.word,
      };

      this.$emit("modifyLine", data);
    },

    deleteLine() {
      this.$emit("deleteLine", this.block.id);
    },

    getTime(time) {
      const hours = Math.floor(time / 3600);
      const minutes = Math.floor((time - hours * 3600) / 60);
      const seconds = Math.floor(time - hours * 3600 - minutes * 60);

      const minutesFormat = minutes < 10 ? "0" + minutes : minutes;
      const secondsFormat = seconds < 10 ? "0" + seconds : seconds;
      const hoursFormat = hours < 10 ? "0" + hours : hours;

      return hoursFormat + ":" + minutesFormat + ":" + secondsFormat;
    },
  },
};
</script>

 <style scoped lang="scss"> 
.Resume {
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.cursor {
  cursor: pointer;
}
</style>
