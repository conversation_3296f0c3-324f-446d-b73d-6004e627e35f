<template>
  <div class="CourseStatsDetails">
    <CourseStatsFilter
      name="stats"
      v-if="!itineraryId"
      :translations="translationData"
      :loading-data="loadingData"
      :announcement-id="announcementId"
      :itinerary-id="itineraryId"
      @apply="loadData"
    />
   
  
    <div class="row mx-0" v-if="charLoaded">
      <div class="col-lg-3 col-md-6 col-sm-12">
        <chart
          v-if="charLoaded"
          :options="pieConfig"
          :is-loading="false"
          :show-header="false"
        />
      </div>
      <div
        class="col-lg-9 col-md-6 col-sm-12 d-flex flex-column justify-content-between py-3"
      >
        <div class="d-flex gap-2 font-weight-bold">
          <p class="my-0"><i class="fa fa-user" /> </p>
          <p class="my-0">
            {{
              $t(itineraryId ? "ITINERARY.ITINERARY_SECTION.STATS_RESUMEN" :  "COURSE.COURSE_SECTION.STATS_RESUMEN", [
                totalFinished,
                totalStarted,
                totalInactives,
              ])
            }}
            <br />
            {{ $t(itineraryId ? "ITINERARY.ITINERARY_SECTION.STATS_RESUMEN_AVG" :  "COURSE.COURSE_SECTION.STATS_RESUMEN_AVG", [totalAvg]) }}
          </p>
        </div>
        <div v-if="!allUsersInChapters">
          <p class="my-0 alert alert-info font-weight-bold" style="font-size: 14px">
            ** {{ $t("ITINERARY.CHAPTERS_EXCEPTION_MESSAGE") }} **
          </p>
        </div>
      <div class="text-right" v-if="allowExport && !itineraryId && (enableReports || this.announcementId)">
          <button v-if="$auth.hasPermission(COURSE_PERMISSIONS.EXPORT)"
            class="btn btn-sm btn-primary"
            data-bs-toggle="modal"
            data-bs-target="#userCourseExcelStatsModal"
          >
            <i class="fa fa-download"></i>
            {{ $t("ANNOUNCEMENT.MODALS.REPORT_XML") }}
          </button>
        </div>
        <div class="text-right" v-if="itineraryId && enableReports">
          <button
            class="btn btn-sm btn-primary"
            @click="downloadExcel"
          >
            <i class="fa fa-download"></i>
            {{ $t("ANNOUNCEMENT.MODALS.REPORT_XML") }}
          </button>
        </div>
      </div>
    </div>
    <div v-else>
      <Spinner/>
    </div>
    <div class="table-responsive mt-3" v-if="(chapterData.length && !this.itineraryId) || (coursesData.length && itineraryId)">
      <table class="table table-condensed" >
        <thead>
        <tr>
          <th>{{ $t( !itineraryId ? "CHAPTERS.LABEL.PLURAL" : "COURSES.HOME.TITLE") }}</th>
          <th v-if="!itineraryId">{{ $t("TYPE") }}</th>
          <th>{{ $t("ANNOUNCEMENT.STATUS.IN_PROCESS") }}</th>
          <th>{{ $t("FINISHED") }}</th>
          <th>{{ $t("TOTAL_TIME") }}</th>
          <th class="text-center">{{ $t("RESULTS") }}</th>
        </tr>
        </thead>
        <tbody class="position-relative" v-if="!itineraryId">
        <tr v-for="(chapter, index) in chapterData" :key="'chapter_' + index">
          <td>
            <img :src="chapter.image" :alt="chapter.name" />
            {{ chapter.name }}
          </td>
          <td>
            <img :src="getIconDir(chapter.icon)" alt="Chapter Type" />
            <span class="text-capitalize">{{ chapter.type }}</span>
          </td>
          <td>
            <b>{{ chapter.inProcess }} %</b>
          </td>
          <td>
            <b>{{ chapter.finished }} %</b>
          </td>
          <td>{{ chapter.time }}</td>
          <td class="text-center text-primary">
            <b
              v-if="chapter.showView"
              class="cursor-pointer"
              data-bs-toggle="modal"
              data-bs-target="#generalCourseStatsModal"
              @click="setChapter(index)"
            >
              {{ $t("VIEW") }}
            </b>
            <span v-else>-</span>
          </td>
        </tr>
        <tr v-if="loadingData" class="tableLoadingInfo">
          <td colspan="10" class="text-center">
            <b>{{ $t("LOADING") }}...</b>
          </td>
        </tr>
        <tr v-else-if="!chapterData.length">
          <td class="text-center" colspan="10">{{ $t("EMPTY.TITLE") }}</td>
        </tr>
        </tbody>
        <tbody class="position-relative" v-else>
        <tr v-for="(course, index) in coursesData" :key="'course_' + index">
          <td>
            <img :src="course.thumbnail" :alt="course.name" />
            {{ course.name }}
          </td>
          <td>
            <b>{{ course.inProgress }} %</b>
          </td>
          <td>
            <b>{{ course.finished }} %</b>
          </td>
          <td>{{ course.totalTime }}</td>
          <td class="text-center text-primary">
            <b
              v-if="true"
              class="cursor-pointer"
              data-bs-toggle="modal"
              data-bs-target="#CourseStatsDetailsModal"
              @click="setCourse(course)"
            >
              {{ $t("VIEW") }}
            </b>
            <b v-else>-</b>
          </td>
        </tr>
        <tr v-if="loadingData" class="tableLoadingInfo">
          <td colspan="10" class="text-center">
            <b>{{ $t("LOADING") }}...</b>
          </td>
        </tr>
        <tr v-else-if="!coursesData.length">
          <td class="text-center" colspan="10">{{ $t("EMPTY.TITLE") }}</td>
        </tr>
        </tbody>
      </table>
     
    </div>
    <div v-else>
      <Spinner/>
    </div>
  

    <Pagination
        :total-items="totalItems"
        :page-size="pageSize"
        :prop-current-page="currentPage"
        :disabled="loadingData"
        v-if="(chapterData.length && !this.itineraryId) || (coursesData.length && itineraryId)"
        @current-page="setCurrentPage"/>
    <StatsChapterCourse
      v-if="!itineraryId"
      :chapter-data="chapterSelected"
      :courseData="courseData"
    />
    <ExcelDownloadModal
      v-if="allowExport && !itineraryId"
      :filters="currentFilters"
      :chapters="chapterData"
      :sending-loading="sendingExcelData"
      @download="downloadExcel"
    />
    <CourseStatsDetailsModal :course-id="courseIdItinerary" :course-data="courseDataItinerary" :is-modal-open="isModalOpen" @close="isModalOpen = false" :itinerary-id="itineraryId"/>
    <div class="text-right" v-if="itineraryId">
      <h6>{{ $t("TOTAL_TIME") }}: <b>{{ totalTimeCourses }}</b></h6>
    </div>
  </div>
</template>

<script>
import CourseStatsFilter from "./CourseStatsFilter.vue";
import Chart from "../stats/Chart.vue";
import { GeneralStatsModel } from "./models/GeneralStatsModel";
import StatsChapterCourse from "./modal/StatsChapterCourse.vue";
import axios from "axios";
import ExcelDownloadModal from "./modal/ExcelDownloadModal.vue";
import CourseStatsDetailsModal from "./modal/CourseStatsDetailsModal.vue";
import { secondsToTime } from './models/UserStatsModel'
import Pagination from '../components/Pagination.vue'
import Spinner from "../../base/BaseSpinner.vue";
import TaskQueueMixin from '../../mixins/TaskQueueMixin';
import {COURSE_ACTIONS_BY_ROLE, COURSE_PERMISSIONS} from "../../common/utils/auth/permissions/course.permissions";


export default {
  name: "CourseStatsDetails",
  components: {
    Spinner,
    ExcelDownloadModal,
    StatsChapterCourse,
    Chart,
    CourseStatsFilter,
    CourseStatsDetailsModal,
    Pagination
  },
  mixins: [
    TaskQueueMixin
  ],
  props: {
    course_id: { type: Number, default: 0 },
    announcementId: { type: Number, default: 0 },
    course_data: { type: Object, default: () => ({}) },
    itineraryId: { type: String, default: undefined },
    allowExport: { type: Boolean, default: true }
  },
  data: () => ({
    pieConfig: {
      type: "pie",
      height: 200,
      series: [],
    },
    charLoaded: false,
    chapterData: [],
    coursesData: [],
    chapterSelected: undefined,
    totalStarted: 0,
    totalFinished: 0,
    totalInactives: 0,
    totalUsers: 0,
    totalAvg: 0,
    loadingData: false,
    sendingExcelData: false,
    currentFilters: {},
    courseIdItinerary: undefined,
    courseDataItinerary: undefined,
    isModalOpen: false,
    totalTimeCourses: 0,
    pathImage: '',
    allUsersInChapters: true,
    pageSize: 10,
    currentPage: 1,
    totalItems: 0,
    enableReports: true
  }),
  computed: {
    COURSE_PERMISSIONS(){
      return COURSE_PERMISSIONS;
    },
    COURSE_ACTIONS_BY_ROLE(){
      return COURSE_ACTIONS_BY_ROLE;
    },
    translationData: () => courseTranslations || {},
    assetsHome() {
      return assetsDir || "";
    },
    courseId() {
      return this.announcementId != 0 ? this.course_id : (this.itineraryId ? this.courseIdItinerary : (this.$route?.params?.id || courseId));
    },
    courseData() {
      return this.announcementId != 0 ? this.course_data : (this.itineraryId ? this.courseDataItinerary : course_data);
    },
  },
  mounted() {
    setTimeout(() => {
      this.$auth.setPermissionList(COURSE_ACTIONS_BY_ROLE)
      if(!this.itineraryId){
        this.loadData({
          userStatus: "active",
          source: this.itineraryId ? ['itinerary'] : [],
        });
      }
      else{
        this.pageSize = 5;
        this.itinerarySummary(this.itineraryId);
        this.loadItineraryData()
      }

    }, 10);
  },
  beforeDestroy() {
    this.$auth.setPermissionList({})
  },
  methods: {
    setChapter(index) {
      this.chapterSelected = this.chapterData[index];
      this.loadDetails(this.chapterSelected);
    },
    async setCourse(course) {
      this.courseDataItinerary = {};
      this.courseIdItinerary = null;
      this.courseDataItinerary.chaptersData = [];
      this.courseDataItinerary.chaptersData.totalItems = 0;
      await this.courseSummary(course);
      await this.getChaptersCourseItinerary(course.id);
      setTimeout(() => {}, 2);
      this.isModalOpen = true;
    },
    getIconDir(icon = "") {
      return `${this.assetsHome}${icon}`;
    },
    async loadDetails(chapter) {
      if (chapter.detailsLoaded) return;
      const { data: { data } } = await axios.get(`/admin/api/v1/statistics/chapters/${chapter.id}/progress`)
      chapter.setDetails(data)
    },
    paramsToString(obj = {}) {
      return Object.keys(obj).filter((key) => !!obj[key])
        .reduce((acc, key) => [...acc, `${key}=${obj[key]}`], [])
        .join('&');
    },
    async loadData(filters = {}) {
      if (this.loadingData) return null;
      this.loadingData = true;
      this.currentFilters = { ...filters };
      this.chapterData = [];
      const payload = this.paramsToString({
        page: this.currentPage,
        itineraryId: this.itineraryId || ''
      })

      this.currentFilters = { ...filters };
      this.currentFilters.source = this.itineraryId ? ['itinerary'] : [];
      this.currentFilters.announcementId = this.announcementId ?? 0;

      const route  = `/admin/api/v1/statistics/courses/${this.courseId}/chapters?${payload}`;
      let data = {};
      await axios.post(route, this.currentFilters).then((response) => {
        data = response?.data?.data
        this.enableReports = response?.data?.enableReports
      })
      this.chapterData = (data?.chapters || []).map((item) => new GeneralStatsModel({inProcess: item.inProgress, totalTime: item.time, ...item }));
      this.totalItems = data?.pagination?.totalItems || 0;
      this.loadingData = false;
      await this.loadSummary(payload);
    },
    async loadSummary(payload = {}) {
      const { data: { data } } = await axios.post(`admin/api/v1/statistics/courses/${this.courseId}/chapters/summary?${payload}`, this.currentFilters)
      this.totalStarted = +(data.started || 0);
      this.totalFinished = +(data.finished || 0);
      this.totalUsers = +(data.totalUsers || 0);
      this.totalTimeCourses = secondsToTime(data.time || 0);
      
      this.totalInactives = Math.max( this.totalUsers - this.totalFinished - this.totalStarted, 0);

      this.totalAvg = Math.trunc(
        this.totalUsers ? (this.totalFinished * 100) / this.totalUsers : 0
      );

      this.pieConfig.series = [
        {
          name: `${this.$t("NO_STARTING")}: ${this.totalInactives}`,
          y: +this.totalInactives,
          color: "#CBD5E1",
        },
        {
          name: `${this.$t("FINISHED")}: ${this.totalFinished}`,
          y: +this.totalFinished,
          color: "#009BDB",
        },
        {
          name: `${this.$t("STARTED")}: ${this.totalStarted}`,
          y: +this.totalStarted,
          color: "#0074A4",
        },
      ];
      this.charLoaded = true;
      this.porcentChapterData();
    },
    async loadItineraryData(){
      let route =  `admin/api/v1/statistics/itineraries/${this.itineraryId}/courses?page=${this.currentPage}&limit=${this.pageSize}`;
      await axios.get(route).then(async (response) => {
        let totalTime = 0;
        this.totalItems = response?.data?.data?.pagination.totalItems || 0;
        this.coursesData = (response?.data?.data?.courses || []).map(
            (course) => {
              totalTime += course.time || 0;
              return {id: course.id, name: course.name, thumbnail: course.thumbnail, inProgress: course.inProgress, finished: course.finished, totalTime: secondsToTime(course.time || 0), totalChapters: course.totalChapters};
            }
        );
        this.totalTimeCourses = secondsToTime(totalTime);
        this.enableReports = response?.data?.enableReports
        await this.percentCourseData();
      })
    },
    async itinerarySummary(itineraryId){
      let route =  `/admin/api/v1/statistics/itineraries/${itineraryId}/summary`;
      await axios
          .post(
              route,
              this.currentFilters
          )
          .then((response) => {
            this.totalStarted = response?.data?.data?.started || 0;
            this.totalFinished = response?.data?.data?.finished || 0;
            this.totalInactives = Math.max(
                (response?.data?.data?.total_users || 0) -
                this.totalFinished -
                this.totalStarted,
                0
            );
            const totalUsers =
                this.totalStarted + this.totalFinished + this.totalInactives;
            this.totalAvg = (
                totalUsers ? (this.totalFinished * 100) / totalUsers : 0
            ).toFixed(0);
            this.pieConfig.series = [
              {
                name: `${this.$t("NO_STARTING")}: ${this.totalInactives}`,
                y: +this.totalInactives,
                color: "#CBD5E1",
              },
              {
                name: `${this.$t("FINISHED")}: ${this.totalFinished}`,
                y: +this.totalFinished,
                color: "#009BDB",
              },
              {
                name: `${this.$t("STARTED")}: ${this.totalStarted}`,
                y: +this.totalStarted,
                color: "#0074A4",
              },
            ];
            this.charLoaded = true;
          })
    },
    async courseSummary(course){
      let courseId = course != null ? course.id : this.courseId;
      let route =  `/admin/api/v1/statistics/itineraries/${this.itineraryId}/courses/${courseId}/chapters/summary`;
      await axios
          .get(
              route
          )
          .then((response) => {
            course.totalStarted = response?.data?.data?.started || 0;
            course.totalFinished = response?.data?.data?.finished || 0;
            course.totalUsers = response?.data?.data?.totalUsers;
            this.courseDataItinerary = course;
            this.courseIdItinerary = course.id;
          })
    },
    async getChaptersCourseItinerary(courseId){
      let route = `admin/api/v1/statistics/itineraries/${this.itineraryId}/courses/${courseId}/chapters`;
      await axios.get(route).then((response) => {
        this.courseDataItinerary.chaptersData = response.data?.data?.chapters;
        this.courseDataItinerary.chaptersData.totalItems = response.data?.data?.pagination.totalItems;
      })
    },
    porcentChapterData() {
      this.chapterData.forEach((chapter) => {
        let total = chapter.inProcess + chapter.finished;
        if (total > 0) {
          chapter.inProcessPorc =
            chapter.inProcess > 0
              ? parseFloat(chapter.inProcess).toFixed(0)
              : 0;
          chapter.finishedPorc =
            chapter.finished > 0 ? parseFloat(chapter.finished).toFixed(0) : 0;
        } else {
          chapter.inProcessPorc = 0;
          chapter.finishedPorc = 0;
        }
      });
    },
    percentCourseData() {
      if(!this.coursesData) return;
      this.coursesData.forEach((course) => {
        course.inProgressPercent = (course.inProgress / course.totalChapters).toFixed(0);
        course.finishedPercent = (course.finished / course.totalChapters).toFixed(0);
      });
    },
    async downloadExcel(filters) {
      if (this.sendingExcelData || !this.allowExport) return null;
      this.sendingExcelData = true;
      try {
        await this.enqueueTask({
          url: this.itineraryId ?
            `/admin/itinerary/${this.itineraryId}/zip-export` :
            `/admin/api/v1/course-stats/${this.courseId}/xlsx?announcementId=${this.announcementId}`,
          data: filters,
          messages: {
            success: `${this.translationData.export_success}<br/>(${this.$t("COURSE_STATS.EXPORT.ZIP_DIR")})`,
            error: this.translationData.export_error
          }
        });
      } catch (e) {
        console.log(e);
      } finally {
        this.sendingExcelData = false;
      }
    },
    setCurrentPage(value) {
      if (this.loadingData) return null
      this.currentPage = value
      if(this.itineraryId){
        this.coursesData = [];
        this.loadItineraryData(this.currentFilters)
      }
      else
        this.loadData(this.currentFilters)
    },
  },
};
</script>
