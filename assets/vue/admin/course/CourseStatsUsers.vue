<template>
  <div class="CourseStatsUsers">
    <CourseStatsFilter name="users" :translations="translationData" :loading-data="loadingData" @apply="loadData"/>
    <div class="px-3">
      <p class="font-weight-bold mb-3"><i class="fa fa-user"/> {{ $t('COURSE.COURSE_SECTION.STATS_RESUMEN', [totalFinished, totalStarted, totalInactives])}}</p>
      <div class="w-100 text-right">
        <b>{{ $t('COMMON.PAGINATION_INFO', [userList.length, totalUsers]) }}</b>
      </div>
    </div>
    <div class="table-responsive px-3">
      <table class="table table-condensed">
        <thead>
        <tr>
          <th>{{ $t('USER.EMAIL') }}</th>
          <th>{{ $t('NAME') }}</th>
          <th>{{ $t('USER.LABEL.LAST_NAME') }}</th>
          <th>{{ $t('ANNOUNCEMENT.MODALS.PROGRESS_TASK5') }}</th>
        </tr>
        </thead>
        <tbody class="position-relative">
        <tr v-for="(user, index) in userList" :key="'user_' + index">
          <td>
            <span class="text-primary cursor-pointer text-lowercase" @click="goToRoute(index)">
              {{ user.email }}
            </span>
          </td>
          <td class="text-capitalize">{{ user.firstName.toLowerCase() }}</td>
          <td class="text-capitalize">{{ user.lastName.toLowerCase() }}</td>
          <td>
            <div class="progressContainer d-flex gap-3 align-items-center py-2 px-3 cursor-pointer"
                 data-bs-toggle="modal"
                 data-bs-target="#userCourseDetailModal"
                 @click="setUser(index)">
              <span class="user-select-none">{{ user.chapterStarted }}/<span class="text-primary">{{ user.totalChapters }}</span></span>
              <div class="progress bg-dark w-100">
                <div class="progress-bar bg-primary" :style="{ width: `${user.progress}%` }"></div>
              </div>
            </div>
          </td>
        </tr>
        <tr v-if="!userList.length">
          <td class="text-center" colspan="5">{{ $t('EMPTY.TITLE') }}</td>
        </tr>
        <tr v-if="loadingData" class="tableLoadingInfo">
          <td colspan="10" class="text-center"><b>{{ $t('LOADING') }}...</b></td>
        </tr>
        </tbody>
      </table>
    </div>
    <Pagination
      :total-items="totalUsers"
      :page-size="pageSize"
      :prop-current-page="currentPage"
      :disabled="loadingData"
      @current-page="setCurrentPage"/>
    <UserCourseDetails :allow-export="allowExport" :course-id="courseId" :user-data="userSelected"/>
  </div>
</template>

<script>
import CourseStatsFilter from './CourseStatsFilter.vue'
import ProgressBar from '../../announcement/components/details/progressBar.vue'
import UserCourseDetails from './modal/UserCourseDetails.vue'
import axios from 'axios'
import UserStatsModel from './models/UserStatsModel'
import Pagination from '../components/Pagination.vue'

export default {
  name: "CourseStatsUsers",
  components: { Pagination, UserCourseDetails, ProgressBar, CourseStatsFilter },
  props: {
    allowExport: { type: Boolean, default: true }
  },
  data: () => ({
    userList: [],
    userSelected: undefined,
    totalStarted: 0,
    totalFinished: 0,
    totalInactives: 0,
    totalUsers: 0,
    currentPage: 1,
    pageSize: 10,
    loadingData: false,
    currentFilters: {},
  }),
  computed: {
    translationData: () => courseTranslations || {},
    courseId() {
      return this.$route?.params?.id || courseId || 0
    }
  },
  mounted() {
    this.loadData({ userStatus: 'active' })
  },
  methods: {
    loadData(filters = {}) {
      if (this.loadingData) return null
      this.loadingData = true 
      this.currentFilters = filters
      let tabCall = "CourseStatsUsers";
      axios.post(`/admin/api/v1/course-stats/${this.courseId}/users?page=${this.currentPage}&page-size=${this.pageSize}&tabCall=${tabCall}`, filters)
        .then((response) => {
          this.userList = (response?.data?.data?.data || []).map((user) => new UserStatsModel(user))
          this.totalUsers = response?.data?.data?.totalUsers || 0
          this.totalStarted = response?.data?.data?.totalStarted || 0
          this.totalFinished = response?.data?.data?.totalFinished || 0
          this.totalInactives = Math.max((this.totalUsers || 0) - this.totalFinished - this.totalStarted, 0)
        }).finally(() => { this.loadingData = false })
    },
    setCurrentPage(value) {
      if (this.loadingData) return null
      this.currentPage = value
      this.loadData(this.currentFilters)
    },
    setUser(index) {
      this.userSelected = this.userList[index]
    },
    goToRoute(index) {
      if (!this.userList[index].urlView) return null
      window.location.href = this.userList[index].urlView
    }
  }
}
</script>