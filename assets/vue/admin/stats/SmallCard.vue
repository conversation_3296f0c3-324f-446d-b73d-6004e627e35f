<template>
  <div class="SmallCard" :class="{loading: isLoading}" :style="{color: isLoading ? '#CFD8DC' : color}">
    <p><i class="icon" :class="isLoading ? 'fa fa-circle-o-notch fa-spin fa-fw': icon"></i></p>
    <p class="value">{{ isLoading ? '' : value }}</p>
    <p class="title">{{ isLoading ? '' : $t(title) }}</p>
  </div>
</template>

<script>

export default {
  name      : "SmallCard",
  components: {},
  props     : {
    isLoading: {
      type: Boolean,
      default: true
    },
    icon : {
      type   : String,
      default: "fa fa-user",
    },
    title: {
      type   : String,
      default: "Title",
    },
    value: {
      type   : String,
      default: '0',
    },
    color: {
      type   : String,
      default: "#8BC34A",
    },
  },
};
</script>

 <style scoped lang="scss"> 
.SmallCard {
  width: 100%;
  background-color: white;
  border: 1px solid #E7EBF0;
  box-shadow: 0 0 2px 2px #ECEFF1;
  overflow: hidden;
  border-radius: 7px;
  padding: 1rem 0.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  gap: 0;

  p { margin: 0 auto; }

  .icon { font-size: 3rem; }
  .value { font-size: 1.4rem; font-weight: bold; }

  .title { color: #37474F; }

  &.loading {
    gap: 1rem;

    .value, .title {
      animation: opacityAnimation 1.1s linear infinite alternate;
      border-radius: 7px;
    }

    .value {
      width: clamp(30px, 90%, 90px);
      background-color: #CFD8DC;
    }
    .title {
      width: clamp(30px, 90%, 230px);
      background-color: #37474F;
      overflow: hidden;
    }
  }
}
</style>
