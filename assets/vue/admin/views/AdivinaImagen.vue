<template>
  <div class="adivinaImagen">
    <div class="col align-self-end text-right mb-2">
      <button
        type="button"
        class="btn btn-primary"
        data-bs-toggle="modal"
        :data-bs-target="`#modal-chapter-${typeChapter}`"
        @click = "forceRerender()"
      >
        {{ translationsVue.question_configureFields_add_question }}
      </button>
    </div>

    <!-- Modal -->
    <div
      class="modal fade"
      :id="`modal-chapter-${typeChapter}`"
      data-bs-backdrop="static"
      data-bs-keyboard="false"
      tabindex="-1"
      aria-labelledby="staticBackdropLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="staticBackdropLabel">
              {{ translationsVue.quiz_configureFields_title_creation }}
            </h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
              :id="`close-modal-chapter-${typeChapter}`"
            ></button>
          </div>
          <div class="modal-body">
            <NewAdivina :url-chapter="routeChapter" :key="componentKey" />
          </div>
        </div>
      </div>
    </div>

    <div class="mt-4">
      <table class="table" v-if="adivinaImage && adivinaImage.length > 0">
        <thead>
          <tr>
            <th scope="col">
              {{ translationsVue.common_areas_image }}
            </th>
            <th scope="col">
              {{ translationsVue.content_configureFields_title }}
            </th>
            <th scope="col">{{ translationsVue.games_text_common_time }}</th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="adivina in adivinaImage" :key="adivina.id">
            <td>
              <a
                data-bs-toggle="modal"
                :data-bs-target="`#question${adivina.id}`"
              >
                <img
                  :src="
                    adivina.image
                      ? `uploads/games/adivinaImagen/${adivina.image}`
                      : 'assets/chapters/default-image.svg'
                  "
                  alt=""
                  @error="
                    $event.target.src = 'assets/chapters/default-image.svg'
                  "
                />
              </a>

              <BaseViewImagen
                :identifier="`question${adivina.id}`"
                :image="`uploads/games/adivinaImagen/${adivina.image}`"
              />
            </td>
            <td>{{ adivina.title }}</td>
            <td>{{ convertSecondToHoursMinutesAndSeconds(adivina.time) }}</td>

            <td class="text-right">
              <button
                type="button"
                class="btn-sm btn btn-danger"
                data-bs-toggle="modal"
                :data-bs-target="`#deleteModal${adivina.id}`"
              >
                <i class="fas fa-trash-alt"></i>
              </button>
              <button
                type="button"
                class="btn btn-primary btn-sm"
                data-bs-toggle="modal"
                :data-bs-target="`#modal-edit-question${adivina.id}`"
              >
                <i class="fas fa-edit"></i>
              </button>
            </td>

            <!-- Modal -->
            <div
              class="modal fade"
              :id="`modal-edit-question${adivina.id}`"
              data-bs-backdrop="static"
              data-bs-keyboard="false"
              tabindex="-1"
              aria-labelledby="staticBackdropLabel"
              aria-hidden="true"
            >
              <div class="modal-dialog modal-dialog-centered modal-xl">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title" id="staticBackdropLabel">
                      {{ translationsVue.quiz_configureFields_title_creation }}
                    </h5>
                    <button
                      type="button"
                      class="btn-close"
                      data-bs-dismiss="modal"
                      aria-label="Close"
                    ></button>
                  </div>
                  <div class="modal-body">
                    <EditAdivina
                      :url-chapter="routeChapter"
                      :adivina="adivina"
                      :time="
                        convertSecondToHoursMinutesAndSeconds(adivina.time)
                      "
                    />
                  </div>
                </div>
              </div>
            </div>

            <BaseModalDelete
              :identifier="`deleteModal${adivina.id}`"
              :title="translationsVue.quiz_configureFields_question_delete"
              @delete-element="deleteAdivina(adivina.id)"
            />
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script>
import Options from "../components/adivina-imagen/Options";
import Resume from "../components/adivina-imagen/Resume";
import { get } from "vuex-pathify";
import Loader from "../components/Loader";
import NewAdivina from "../components/adivina-imagen/NewAdivina";
import EditAdivina from "../components/adivina-imagen/EditAdivina";

import { formatDateMixin } from "../../mixins/formatDateMixin";
import { modalMixin } from "../../mixins/modalMixin";

export default {
  components: {
    Options,
    Resume,
    Loader,
    NewAdivina,
    EditAdivina,
  },

  props: {
    blocks: {
      type: Array,
      default: () => [],
    },
    chapterId: {
      type: Number,
      default: 0,
    },
  },

  mixins: [formatDateMixin, modalMixin],

  data() {
    return {
      lines: this.blocks,
      currentLine: {},
      called: true,
      typeChapter,
      translationsVue,
      componentKey: 0,
    };
  },

  computed: {
    ...get("callModule", ["isLoading"]),
    ...get("adivinaImagenModule", ["getAdivinaImagen", "getRouteChapter"]),

    showCalled() {
      return !this.isLoading() && this.called;
    },

    adivinaImage() {
      return this.getAdivinaImagen();
    },

    routeChapter() {
      return this.getRouteChapter();
    },
  },

  async created() {
    this.fetchQuestions();
  },

  methods: {
    forceRerender() {
      this.componentKey += 1;
    },
    async fetchQuestions() {
      await this.$store.dispatch(
        "adivinaImagenModule/fetchAdivina",
        this.chapterId
      );
    },

    async deleteAdivina(id) {
      const formData = new FormData();
      formData.append("id", id);

      await this.$store.dispatch(
        "adivinaImagenModule/deleteAdivinaImagen",
        formData
      );

      this.fetchQuestions();
      this.closeModal(`deleteModal${id}`);
    },
  },
};
</script>

 <style scoped lang="scss"> 
.adivinaImagen {
  padding-top: 2rem;
  background: #fff;
}
img {
  width: 50px;
  cursor: zoom-in;
}
</style>
