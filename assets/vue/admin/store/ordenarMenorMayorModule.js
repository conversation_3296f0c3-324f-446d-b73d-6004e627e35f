import axios from 'axios';
import {make} from 'vuex-pathify';

const getDefaultState = () => ({
    loading: false,
    higherLower: undefined,
    routeChapter: '',
});

const state = () => getDefaultState();

export const getters = {
    isLoading: (state) => () => state.loading,

    getHigherLower: (state) => () => state.higherLower,

    getRouteChapter: (state) => () => state.routeChapter,
};

export const mutations = {
    ...make.mutations(state),
};

export const actions = {
    setBlock(context, data) {
        const url = "/admin/chapter/ordenarmenormayor/set-block";
        return axios.post(url, data);
    },

    editBlock(context, data) {
        const url = "/admin/chapter/ordenarmenormayor/edit-line";
        return axios.post(url, data);
    },

    reloadBlock(context, data) {console.log('data', data);
        const url = "/admin/chapter/ordenarmenormayor/reload-block";
        return axios.post(url, data);

    },

    deleteLine(context, data) {
        const url = "/admin/chapter/ordenarmenormayor/delete-line";
        return axios.post(url, data);
    },

    async fetchHigherLower({ commit }, idChapter) {
        try {
            commit('SET_LOADING', true);
            const { data } = await axios.get(`/admin/higher-lower/${idChapter}`);
            commit('SET_HIGHER_LOWER', data?.data);
        }
        catch (e) {
            console.log(e)
        }
        finally {
            commit('SET_LOADING', false);
        }
    },

    async createHigherLower({ commit }, formData) {
        const url = `/admin/new/higher-lower`;
        try {
            commit('SET_LOADING', true);
            const { data } = await axios.post(
                url, formData);

            commit('SET_ROUTE_CHAPTER', data?.route);
        } catch (e) {
            console.log(e)
        }
        finally {
            commit('SET_LOADING', false);
        }
    },

    async editHigherLower({ commit }, formData) {
        try {
            commit('SET_LOADING', true);
            const { data } = await axios.post(
                `/admin/edit/higher-lower`, formData);

            commit('SET_ROUTE_CHAPTER', data?.route);
        }
        catch (e) {
            console.log(e)
        }
        finally {
            commit('SET_LOADING', false);
        }
    },

    async deleteHigherLower({ commit }, formData) {
        try {
            commit('SET_LOADING', true);
            const { data } = await axios.post(
                `/admin/delete/higher-lower`, formData);

            commit('SET_ROUTE_CHAPTER', data?.route);
        }
        catch (e) {
            console.log(e)
        }
        finally {
            commit('SET_LOADING', false);
        }
    },
};

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions,
};
