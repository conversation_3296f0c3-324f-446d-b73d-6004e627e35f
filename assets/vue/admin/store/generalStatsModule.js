import axios from 'axios';
import {make} from 'vuex-pathify';

const getDefaultState = () => ({});

const state = () => getDefaultState();

export const getters = {};

export const mutations = {
    ...make.mutations(state),
};

async function newStat (url, datakey, filters) {
    let stats;

    try {
        const {data} = await axios.post(url, filters);
        stats = datakey ? data.data[datakey] : data.data;
    } finally {
    }

    return stats;
}

export const actions = {
    // General stats:

    async fetchGeneralStats({commit}, {filters}) {
        return newStat('/admin/stats/general', 'stats', filters);
    },

    async fetchUserLogins({commit}, {filters}) {
        return newStat('/admin/stats/logins', 'logins', filters);
    },

    async fetchUsersStatus({commit}, {filters}) {
        return newStat('/admin/stats/users-status', 'status', filters);
    },

    async fetchChapterTypes({commit}, {filters}) {
        return newStat('/admin/stats/chapter-types', 'types', filters);
    },

    async fetchFinishedChapterTypes({commit}, {filters}) {
        return newStat('/admin/stats/finished-chapter-types', 'types', filters);
    },

    async fetchFinishedChapters({commit}, {filters}) {
        return newStat('/admin/stats/finished-chapters', 'finishedChapters', filters);
    },


    async fetchFinishedCourses({commit}, {filters}) {
        return newStat('/admin/stats/finished-courses', 'finishedCourses', filters);
    },

    async fetchTimeSpentByType({commit}, {filters}) {
        return newStat('/admin/stats/time-spent', 'time', filters);
    },

    async fetchUsersGender({commit}, {filters}) {
        return newStat('/admin/stats/users-gender', 'status', filters);
    },

    async fetchUsersCountry({commit}, {filters,bd_filters}) {
        return newStat('/admin/stats/users-country', 'status', {...filters, ...bd_filters});
    },

    async fetchDevicesSesion({commit}, {filters}) {
        return newStat('admin/stats/devices-sesion', 'devices', filters);
    },

    async fetchLoginSesion({commit}, {filters}) {
        return newStat('admin/stats/login-sesion', 'devices', filters);
    },

    async fetchLoginDistinctSesion({commit}, {filters}) {
        return newStat('admin/stats/login-distinct-sesion', 'devices', filters);
    },

    async fetchUsersWithAtLeastOneCourseFinished({commit}, {filters}) {
        return newStat('admin/stats/users-course-finished', 'users', filters);
    },

    async fetchDistributionUserAge({commit}, {filters}) {
        return newStat('admin/stats/distribution-user-age', 'user', filters);
    },

    // Segmented Data Persons:

    async fetchSegmented_persons({commit}, {filters}) {
        return newStat('admin/stats/segmented/person', null, filters);
    },

    async fetchSegmentedDivision_persons({commit}, {filters}) {
        return newStat('admin/stats/segmented/personBy/division', null, filters);
    },

    async fetchSegmentedCountries_persons({commit}, {filters}) {
        return newStat('/admin/stats/segmented/personBy/country', null, filters);
    },

    async fetchSegmentedTotals_persons({commit}, {filters}) {
        return newStat('/admin/stats/segmented/total/structureAndHotel', null, filters);
    },

    async fetchSegmentedStructure_persons({commit}, {filters}) {
        return newStat('/admin/stats/segmented/personAndGroupBy/structure', null, filters);
    },

    async fetchSegmentedHotel_persons({commit}, {filters}) {
        return newStat('/admin/stats/segmented/personAndGroupBy/hotel', null, filters);
    },

    // Segmented Data Courses:

    async fetchSegmented_courses({commit}, {filters}) {
        return newStat('/admin/stats/segmented/course', null, filters);
    },

    async fetchSegmented_segmented_user_courses_new_finished_in_platform({commit}, {filters}) {
        return newStat('/admin/stats/segmented/course/new-finished', null, filters);
    },

    async fetchSegmentedDivision_courses({commit}, {filters}) {
        return newStat('/admin/stats/segmented/course/startedVsFinished', null, filters);
    },

    async fetchSegmentedCountries_courses({commit}, {filters}) {
        return newStat('/admin/stats/segmented/course/country', null, filters);
    },

    async fetchSegmentedTotals_courses({commit}, {filters}) {
        return newStat('/admin/stats/segmented/course/total', null, filters);
    },

    async fetchSegmentedStructure_courses({commit}, {filters}) {
        return newStat('/admin/stats/segmented/courseGroupBy/structure', null, filters);
    },

    async fetchSegmentedHotel_courses({commit}, {filters}) {
        return newStat('/admin/stats/segmented/courseGroupBy/hotel', null, filters);
    },

    // Segmented Data Access:

    async fetchSegmented_access({commit}, {filters}) {
        return newStat('/admin/stats/segmented/access/acumulative', null, filters);
    },

    async fetchSegmentedDivision_access({commit}, {filters}) {
        return newStat('/admin/stats/segmented/access/groupBy/division', null, filters);
    },

    async fetchSegmentedStructureVSHotel_access({commit}, {filters}) {
        return newStat('/admin/stats/segmented/access/groupBy/structureVsHotel', null, filters);
    },

    async fetchSegmentedCountries_access({commit}, {filters}) {
        return newStat('/admin/stats/segmented/access/groupBy/country', null, filters);
    },

    async fetchSegmentedTotals_access({commit}, {filters}) {
        return newStat('/admin/stats/segmented/access/total', null, filters);
    },

    async fetchSegmentedStructure_access({commit}, {filters}) {
        return newStat('/admin/stats/segmented/access/groupByDepartment/structure', null, filters);
    },

    async fetchSegmentedHotel_access({commit}, {filters}) {
        return newStat('/admin/stats/segmented/access/groupByDepartment/hotel', null, filters);
    },

    // Segmented Data Hours:

    async fetchSegmented_hours({commit}, {filters}) {
        return newStat('/admin/stats/segmented/hour', null, filters);
    },

    async fetchSegmentedDivision_hours({commit}, {filters}) {
        return newStat('/admin/stats/segmented/hour/division', null, filters);
    },

    async fetchSegmentedCountries_hours({commit}, {filters}) {
        return newStat('/admin/stats/segmented/hour/total/country', null, filters);
    },

    async fetchSegmentedTotals_hours({commit}, {filters}) {
        return await newStat('/admin/stats/segmented/hour/structureVsHotel', null, filters);
    },

    async fetchSegmentedStructure_hours({commit}, {filters}) {
        return newStat('/admin/stats/segmented/hour/groupBy/structure', null, filters);
    },

    async fetchSegmentedHotel_hours({commit}, {filters}) {
        return newStat('/admin/stats/segmented/hour/groupBy/hotel', null, filters);
    },

    async fetchSchool_hours({commit}, {filters}) {
        return newStat('/admin/stats/segmented/hour/total/school', null, filters);
    },

    async fetchDailyPosts({commit}) {
        const url = `/admin/stats/daily-posts`;
        let dailyPosts;

        try {
            const {data} = await axios.get(url);
            dailyPosts = data.data.dailyPosts;
        } finally {
        }

        return dailyPosts;
    },
};

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions,
};
