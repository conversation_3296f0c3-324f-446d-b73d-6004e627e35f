import axios  from 'axios';
import {make} from 'vuex-pathify';

const getDefaultState = () => ({
	loading     : false,
	routeChapter: '',
	parejas     : undefined
});

const state = () => getDefaultState();

export const getters = {
	isLoading: (state) => () => state.loading,

	getRouteChapter: (state) => () => state.routeChapter,

	getParejas: (state) => () => state.parejas,
};

export const mutations = {
	...make.mutations(state),
};

export const actions = {
	setBlock(context, data) {
		const url = "/admin/chapter/parejas/add-parejasimagen";
		const options = {
			headers: {'Content-Type': 'multipart/form-data'},
		};

		const formData = new FormData();
		formData.append('parejaId', data.parejaId);
		formData.append('tipo', data.tipo);
		formData.append('texto', data.texto);
		formData.append('imagen', data.imagen);

		const {result} = axios.post(url, formData, options);

		return result;
	},

	editParejaImagen(context, data) {
		const url = "/admin/chapter/parejas/edit-parejasimagen";
		const options = {
			headers: {'Content-Type': 'multipart/form-data'},
		};

		const formData = new FormData();
		formData.append('id', data.id);
		formData.append('parejaId', data.parejaId);
		formData.append('tipo', data.tipo);
		formData.append('texto', data.texto);
		formData.append('imagen', data.imagen);

		const {result} = axios.post(url, formData, options);

		return result;
	},

	deleteBlock(context, data) {
		const url = "/admin/chapter/parejas/delete-parejasimagen";
		return axios.post(url, data);
	},

	reloadBlock(context, data) {
		const url = "/admin/chapter/parejas/reload-block";
		return axios.post(url, data);
	},

	deletePareja(context, data) {
		const url = "/admin/chapter/parejas/delete-pareja";
		return axios.post(url, data);
	},

	editPareja(context, data) {
		const url = "/admin/chapter/parejas/edit-pareja";
		return axios.post(url, data);
	},
	addPareja(context, data) {
		const url = "/admin/chapter/parejas/add-pareja";
		return axios.post(url, data);
	},

	async createParejas({commit}, formData) {
		const url = `/admin/new/parejas`;
		try {
			commit('SET_LOADING', true);
			const {data} = await axios.post(
				url, formData);

			commit('SET_ROUTE_CHAPTER', data?.route);
		} catch (e) {
			console.log(e)
		} finally {
			commit('SET_LOADING', false);
		}
	},

	async fetchParejas({commit}, idChapter) {
		try {
			commit('SET_LOADING', true);
			const {data} = await axios.get(`/admin/parejas/${idChapter}`);
			commit('SET_PAREJAS', data?.data);
		} catch (e) {
			console.log(e)
		} finally {
			commit('SET_LOADING', false);
		}
	},

	async editParejas({commit}, formData) {
		const url = `/admin/edit/parejas`;
		try {
			commit('SET_LOADING', true);
			const {data} = await axios.post(
				url, formData);

			commit('SET_ROUTE_CHAPTER', data?.route);
		} catch (e) {
			console.log(e)
		} finally {
			commit('SET_LOADING', false);
		}
	},

	async deleteParejas({commit}, formData) {
		try {
			commit('SET_LOADING', true);
			const {data} = await axios.post('/admin/delete/parejas', formData);
			commit('SET_ROUTE_CHAPTER', data?.route);
		} catch (e) {
			console.log(e)
		} finally {
			commit('SET_LOADING', false);
		}
	}
};

export default {
	namespaced: true,
	state,
	getters,
	mutations,
	actions,
};
