import axios from "axios";
import { make } from "vuex-pathify";

const getDefaultState = () => ({
  uploadScorm: undefined,
});

const state = () => getDefaultState();

export const getters = {
  getUploadScorm: (state) => () => state.uploadScorm,
};

export const mutations = {
  ...make.mutations(state),
};

export const actions = {
  async newPackageScorm(context, { file, chapter, domain }) {
    const url = `${domain}/create-scorm`;

    const options = {
      headers: { "Content-Type": "multipart/form-data" },
    };

    const formData = new FormData();
    formData.append("file", file);
    formData.append("chapter", chapter);

    const { data } = await axios.post(url, formData, options);
    return data;
  },
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};
