import axios from 'axios';
import {make} from 'vuex-pathify';

const getDefaultState = () => ({});

const state = () => getDefaultState();

export const getters = {};

export const mutations = {
    ...make.mutations(state),
};

export const actions = {

    async fetchFilterCategories({commit}, {}) {
        const url = `/admin/filter-categories` ;
        let filterCategories;

        try {
            const {data} = await axios.get(url);
            filterCategories = data.data.filterCategories;
        } finally {
        }

        return filterCategories;
    },

    async fetchCourseFilters({commit}, {userId}) {
        const url = `/admin/managers/filters/${userId}` ;
        let managerFilters;

        try {
            const {data} = await axios.get(url);
            managerFilters = data.data.managerFilters;
        } finally {
        }

        return managerFilters;
    },

};

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions,
};
