<template>
  <div class="AudioPlayer">
<!--    <div>-->
<!--&lt;!&ndash;      <div class="range-slider grad" style='&#45;&#45;min:0; &#45;&#45;max:10000; &#45;&#45;step:100; &#45;&#45;value:200; &#45;&#45;text-value:"200"; &#45;&#45;prefix:"$"'>&ndash;&gt;-->
<!--      <div class="range-slider grad"-->
<!--           :style="{-->
<!--            '&#45;&#45;min': 0,-->
<!--            '&#45;&#45;max': duration,-->
<!--            '&#45;&#45;step': 1,-->
<!--            '&#45;&#45;value': progress-->
<!--           }"-->
<!--      ><input type="range" min="0"-->
<!--               :max="duration"-->
<!--               step="1"-->
<!--               v-model="trackPosition">-->
<!--        <div class='range-slider__progress'></div>-->
<!--      </div>-->
<!--    </div>-->
    <h3 class="title">{{ title }}</h3>
    <h5 class="subtitle">{{ subtitle }}</h5>
    <div class="meta-actions">
      <div class="control-container">
        <div class="media-element">
          <audio id="audio">
            <source :src="src">
            Your browser does not support the audio tag.
          </audio>
        </div>
        <div class="controls">
          <div class="prepended-buttons">
            <div class="play-pause">
              <button aria-label="Play" title="Play" tabindex="0" v-show="!isPlaying" class="fa fa-play" @click="play()"></button>
              <button aria-label="Pause" title="Pause" tabindex="0" v-show="isPlaying" class="fa fa-pause" @click="pause()"></button>
            </div>
            <div class="time-rail">
              <span class="time-rail__time-total time-rail__slider" title="00:00" aria-valuetext="00:00">
<!--                <span class="time-rail__buffering">-->
<!--                  <input type="range" min="0" :max="duration" :value="progress">-->
<!--                  <div class="progress"></div>-->
<!--                </span>-->
                <div class="range-slider grad"
                     :style="{
                        '--min': 0,
                        '--max': duration,
                        '--value': progress
                       }"
                            ><input type="range" min="0"
                                    :max="duration"
                                    step="1"
                                    v-model="trackPosition">
                    <div class='range-slider__progress'></div>
                </div>
              </span>
            </div>
            <div class="timer">
              <span class="current-time">{{ progressFormatted }}</span><span class="separator">|</span><span class="duration">{{ durationFormatted }}</span>
            </div>
          </div>
          <div class="appended-buttons">
            <div class="skip-back">
              <button type="button" class="fa fa-backward" @click="backward(15)"></button>
            </div>
            <div class="speed-button">
              <button title="Speed Rate"
                      aria-label="Speed Rate"
                      tabindex="0"
                      type="button"
                      @click="changeSpeed()"
              >{{ currentSpeed }}x</button>
            </div>
            <div class="volume-button">
              <button title="Mute"
                      aria-label="Mute"
                      tabindex="0"
                      type="button"
                      class="fa fa-volume-up" @click="mute()" v-if="!isMuted"></button>
              <button title="Unmute"
                      aria-label="Unmute"
                      tabindex="0"
                      type="button"
                      class="fa fa-volume-mute" @click="unmute()" v-else></button>
            </div>
            <div class="forward-button">
              <button aria-label="Jump forward 15 seconds"
                      title="Jump forward 15 seconds"
                      type="button"
                      class="fa fa-forward"
                      @click="forward(15)"
              ></button>
            </div>
          </div>
        </div>
      </div>
      <div class="image-artwork">
        <img :src="thumbnail" alt="">
      </div>
    </div>
  </div>
</template>

<script>
import $ from 'jquery';
export default {
  name: "AudioPlayer",
  $,
  props: {
    title: {
      type: String,
      default: 'Title'
    },
    subtitle: {
      type: String,
      default: 'Subtitle'
    },
    src: {
      type: String,
      required: true
    },
    thumbnail: {
      type: String,
      default: 'assets/chapters/default-image.svg'
    }
  },
  data() {
    return {
      player: null,
      isPlaying: false,
      duration: 0,
      progress: 0,
      isMuted: false,
      currentSpeed:1.0,
      sliderCanMove: false,
      trackPosition: 0
    };
  },
  computed: {
    durationFormatted() {
      return this.formatSecondsAsTime(this.duration);
    },
    progressFormatted() {
      return this.formatSecondsAsTime(this.progress);
    },
    progressPercent() {
      if (this.duration === 0) return 0;
      return this.progress * 100 / this.duration;
    }
  },
  mounted() {
    this.configure();
  },
  watch: {
    trackPosition(newValue) {
      if (newValue !== this.progress) {
        if (this.player) this.player.currentTime = newValue;
      }
    },
  },
  methods: {
    configure() {
      this.player = document.getElementById('audio');

      this.player.addEventListener('ended', event => {

      });

      this.player.addEventListener('timeupdate', event => {
        this.progress = this.player.currentTime;
        this.trackPosition = this.progress;
      });

      this.player.addEventListener('progress', event => {

      })

      this.player.addEventListener('play', () => {
        this.isPlaying = true;
      }, false);

      this.player.addEventListener('pause', () => {
        this.isPlaying = false;
      }, false);

      this.player.addEventListener('canplay', () => {
        this.duration = this.player.duration;
      }, false);

      this.player.addEventListener('volumechange', event => {
        this.isMuted = this.player.muted;
      });
    },

    changeSpeed() {
      if (this.currentSpeed >= 2) {
        this.currentSpeed = 0.75;
      } else {
        this.currentSpeed += 0.25;
      }
      if (this.player) this.player.playbackRate = this.currentSpeed;
    },

    play() {
      if (this.player)
        this.player.play();
    },

    pause() {
      if (this.player)
        this.player.pause();
    },

    mute() {
      if (this.player) this.player.muted = true;
    },

    unmute() {
      if (this.player) this.player.muted = false;
    },

    forward(seconds) {
      if (this.player) this.player.currentTime += seconds;
    },

    backward(seconds) {
      if (this.player) this.player.currentTime -= seconds;
    },

    formatSecondsAsTime(secs, format) {
      let hr = Math.floor(secs / 3600);
      let min = Math.floor((secs - (hr * 3600)) / 60);
      let sec = Math.floor(secs - (hr * 3600) - (min * 60));
      let retValue = "";
      if (hr > 0) {
        retValue = (hr < 10 ? '0' + hr : hr) + ":";
      }
      retValue += (min < 10 ? '0' + min : min) + ":";
      retValue += sec < 10 ? '0' + sec : sec;
      return retValue;
    }
  }
}
</script>

 <style scoped lang="scss"> 
$time-rail-height: 40px;
$rail-height: 15px;

.AudioPlayer {
  border-radius: 5px;
  padding: 30px;
  background: #f0f0f0;

  .title {
    font-size: 28px;
    font-weight: bold;
    line-height: 1.5;
    margin-bottom: 0;
    padding-bottom: 0;
    margin-top: 0;
    color: #333333;
  }

  .subtitle {
    margin-top: 10px;
    text-transform: uppercase;
    font-size: 11px;
    color: #555555;
  }

  .meta-actions {
    background: #dddddd;
    margin: 40px -30px -30px -30px;
    padding: 0 30px 30px 30px;
    display: flex;
    justify-content: space-between;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;

    .control-container {
      width: 100%;
      min-width: 320px;
      margin-top: 10px;
      height: 40px;
      background: none !important;
      position: relative;
      text-align: left;
      text-indent: 0;

      .media-element {
        audio {
          display: none;
        }
      }
    }

    .image-artwork {
      transform: translateY(-70px);
      margin-right: 0;
      img {
        width: 150px;
        height: 150px;
        border-radius: 3px;
        box-shadow: 0 4px 6px 0 rgba(0, 0, 0, 1);
        margin-bottom: -80px;
      }
    }
  }
  .controls {
    width: 100%;
    .prepended-buttons {
      display: flex;
      flex-flow: row nowrap;

      .play-pause {
        width: 32px;
        height: $time-rail-height;
        font-size: 10px;
        line-height: 10px;
        margin: 0;
        display: flex;
        align-items: center;
        justify-content: center;

        button {
          background: transparent;
          border: none;
          color: #555555;
          cursor: pointer;
          display: block;
          height: 20px;
          overflow: hidden;
          padding: 0;
          width: 20px;
        }
      }

      .time-rail {
        display: flex;
        align-items: center;
        flex: 1;
        height: $time-rail-height;
        margin: 0 10px;
        position: relative;

        &__time-total {
          width: 100%;
          background: transparent;
          display: block;
          height: $rail-height;
          position: relative;
        }
      }

      .timer {
        color: #777777;
        font-weight: normal;
        box-sizing: content-box;
        font-size: 11px;
        height: $time-rail-height;
        overflow: hidden;
        text-align: center;
        width: auto;
        display: flex;
        align-items: center;
        justify-content: center;

        .separator {
          margin-left: 1px;
          margin-right: 1px;
        }
      }
    }
    .appended-buttons {
      display: flex;
      justify-content: space-evenly;

      div {
        font-size: 10px;
        width: 40px;
        height: 40px;
        margin: 0;
        position: relative;

        button {
          color: #555555;
          background: transparent;
          border: 0;
          cursor: pointer;
          display: block;
          font-size: 11px;
          height: 20px;
          margin: 10px 6px;
          overflow: hidden;
          padding: 0;
          position: absolute;
          text-decoration: none;
          width: 20px;
        }

        &.speed-button {
          button {
            width: 36px;
          }
        }
      }
    }
  }


  /// For removal
  .range-slider.grad {
    --progress-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2) inset;
    --progress-flll-shadow: var(--progress-shadow);
    --fill-color: linear-gradient(to right, LightCyan, var(--primary-color));
    --thumb-shadow: 0 0 4px rgba(0, 0, 0, 0.3),
    -3px 9px 9px rgba(255, 255, 255, 0.33) inset,
    -1px 3px 2px rgba(255, 255, 255, 0.33) inset,
    0 0 0 99px var(--primary-color) inset;

    input {
      &:hover {
        --thumb-transform: scale(1.2);
      }

      &:active {
        --thumb-shadow: inherit;
        --thumb-transform: scale(1);
      }
    }
  }

  ////////////////////////////////////////////////
  // The main styles

  .range-slider {
    position: absolute;
    width: 100%;
    --primary-color: #555555;

    --fill-color: var(--primary-color);
    --progress-background: #eee;
    --progress-radius: 20px;
    --track-height: calc(var(--thumb-size) / 2);

    --thumb-size: 15px;
    --thumb-color: white;
    --thumb-shadow: 0 0 3px rgba(0, 0, 0, 0.4), 0 0 1px rgba(0, 0, 0, 0.5) inset,
    0 0 0 99px var(--thumb-color) inset;

    --thumb-shadow-active: 0 0 0 calc(var(--thumb-size) / 4) inset
    var(--thumb-color),
    0 0 0 99px var(--primary-color) inset, 0 0 3px rgba(0, 0, 0, 0.4);

    --thumb-shadow-hover: var(--thumb-shadow);

    --value-a: Clamp(
        var(--min),
        var(--value, 0),
        var(--max)
    ); // default value ("--value" is used in single-range markup)
    --value-b: var(--value, 0); // default value
    --text-value-a: var(--text-value, "");

    --completed-a: calc(
        (var(--value-a) - var(--min)) / (var(--max) - var(--min)) * 100
    );
    --completed-b: calc(
        (var(--value-b) - var(--min)) / (var(--max) - var(--min)) * 100
    );
    --ca: Min(var(--completed-a), var(--completed-b));
    --cb: Max(var(--completed-a), var(--completed-b));

    @mixin thumb {
      appearance: none;
      height: var(--thumb-size);
      width: var(--thumb-size);
      transform: var(--thumb-transform);
      border-radius: var(--thumb-radius, 50%);
      background: var(--thumb-color);
      box-shadow: var(--thumb-shadow);
      border: none;
      pointer-events: auto;
      transition: 0.1s;
    }

    display: inline-block;
    height: Max(var(--track-height), var(--thumb-size));
    z-index: 1;

    &__progress {
      --start-end: calc(var(--thumb-size) / 2);
      --clip-end: calc(100% - (var(--cb)) * 1%);
      --clip-start: calc(var(--ca) * 1%);
      --clip: inset(-20px var(--clip-end) -20px var(--clip-start));
      position: absolute;
      left: var(--start-end);
      right: var(--start-end);
      top: calc(var(--thumb-size) / 2 - var(--track-height) / 2
      );
      //  transform: var(--flip-y, translateY(-50%) translateZ(0));
      height: calc(var(--track-height));
      background: var(--progress-background, #eee);
      pointer-events: none;
      z-index: -1;
      border-radius: var(--progress-radius);

      // fill area
      &::before {
        content: "";
        position: absolute;
        left: 0;
        right: 0;
        clip-path: var(--clip);
        top: 0;
        bottom: 0;
        background: var(--fill-color, black);
        box-shadow: var(--progress-flll-shadow);
        z-index: 1;
        border-radius: inherit;
      }

      // shadow-effect
      &::after {
        content: "";
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        box-shadow: var(--progress-shadow);
        pointer-events: none;
        border-radius: inherit;
      }
    }

    & > input {
      -webkit-appearance: none;
      width: 100%;
      height: var(--thumb-size);
      margin: 0;
      position: absolute;
      left: 0;
      top: calc(
          50% - Max(var(--track-height), var(--thumb-size)) / 2
      );
      cursor: grab;
      outline: none;
      background: none;

      &:not(:only-of-type) {
        pointer-events: none;
      }

      &::-webkit-slider-thumb {
        @include thumb;
      }
      &::-moz-range-thumb {
        @include thumb;
      }
      &::-ms-thumb {
        @include thumb;
      }

      &:hover {
        --thumb-shadow: var(--thumb-shadow-hover);
      }

      &:active {
        --thumb-shadow: var(--thumb-shadow-active);
        cursor: grabbing;
        z-index: 2; // when sliding left thumb over the right or vice-versa, make sure the moved thumb is on top
      }

      &:nth-of-type(1) {
        --is-left-most: Clamp(0, (var(--value-a) - var(--value-b)) * 99999, 1);
      }

      &:nth-of-type(2) {
        --is-left-most: Clamp(0, (var(--value-b) - var(--value-a)) * 99999, 1);
      }

      // non-multiple range should not clip start of progress bar
      &:only-of-type {
        ~ .range-slider__progress {
          --clip-start: 0;
        }
      }
    }
  }
}
</style>
