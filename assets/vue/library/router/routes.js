import HomeView from "../views/HomeView.vue";
import CategoryView from "../views/CategoryView.vue";
import NewLibrary from "../components/NewLibrary.vue";
import LibraryView from "../views/LibraryView.vue";

const routes = [
    {
        path: '/admin/apps/library',
        component: HomeView,
        name: 'Home'
    },
    {
        path: '/admin/apps/library/category/:id',
        component: CategoryView,
        name: 'CategoryView'
    },
    {
        path: '/admin/apps/library/category/:id/new-library',
        component: NewLibrary,
        name: 'NewLibrary',
        props: true
    },
    {
        path: '/admin/apps/library/category/:category_id/library/:id',
        component: LibraryView,
        name: 'LibraryDetail',
        props: true
    },
    {
        path: '/admin/apps/library/category/library/:id/edit',
        component: NewLibrary,
        name: 'EditLibrary',
        props: true
    },
];

export default routes;
