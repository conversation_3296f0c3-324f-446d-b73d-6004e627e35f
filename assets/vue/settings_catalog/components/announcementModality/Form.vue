<template>
  <base-form v-model="locale" @cancel="returnToList()" @submit="submit()">
    <template v-slot:form>
      <div class="form-group col-12">
        <label>{{ $t('NAME') }}</label>
        <input type="text" class="form-control" v-model="modality.name">
      </div>

      <div class="form-group col-12 d-flex align-items-center justify-content-start">
        <BaseSwitch :tag="`switcher-announcement-modality-form-active-${modality.id}`"
                     v-model="modality.isActive"/>
        <label class="ml-1">{{ $t('ACTIVE') }}</label>
      </div>
    </template>
    <template v-slot:translations>
      <div v-for="t in modality.translations" :key="t.locale" v-if="t.locale === locale">
        <div class="form-group col-12">
          <label>{{ $t('NAME') }}</label>
          <input type="text" class="form-control" v-model="t.name">
        </div>
      </div>
    </template>
  </base-form>
</template>

<script>
import BaseForm from "../BaseForm.vue";
import {get} from "vuex-pathify";
import BaseSwitch from "../../../base/BaseSwitch.vue";
import Multiselect from "vue-multiselect";

export default {
  name: "Form",
  components: {BaseSwitch, BaseForm, Multiselect}, 
  data() {
    return {
      locale: 'es',
      modality: {
        id: -1,
        name: '',
        isActive: true,
        translations: []
      },
    };
  },
  computed: {
    modalities: get('catalogModule/modalities'),
    locales: get('localeModule/locales'),
  },
  created() {
    let modality = {
      id: -1,
      name: '',
      isActive: true,
      translations: []
    };
    
    if (this.$route.name === 'AnnouncementModalityUpdate') {
      modality = this.modalities.find(c => c.id === this.$route.params.id);
      if (modality === undefined) {
        this.returnToList();
        return;
      }
    }

    let translations = [];

    const keys = Object.keys(this.locales);
    keys.forEach((k) => {
      const translated = modality.translations.find(e => e.locale === k);
      translations.push({
        locale: k,
        name: translated?.name ?? '',
      })
    });

    modality.translations = translations;

    this.modality = modality;
  },
  methods: {
    returnToList() {
      this.$router.push({name: 'AnnouncementModality', params: this.$route.params});
    },

    submit() {
      if(this.modality.name===""){
        this.$toast.error(this.$t('MODALITY.NAME.ERROR') + '');
        return;
      }else{
        const update = this.$route.name === 'AnnouncementModalityUpdate';
        const endpoint = update ? '/admin/announcement-modality/update' : '/admin/announcement-modality/create';
        const save = () => {
          return this.$store.dispatch('catalogModule/save', { endpoint: endpoint, data: this.modality });
        }

        save().then(r => {
          const { error, data } = r;
          if (error) this.$toast.error(data);
          else {
            this.$toast.success(this.$t('CATALOG.SAVED') + '');
            this.returnToList();
          }
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">

</style>
