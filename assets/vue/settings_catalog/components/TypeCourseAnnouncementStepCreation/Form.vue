<script>
import BaseForm from "../BaseForm.vue";
import {get} from "vuex-pathify";
import BaseSwitch from "../../../base/BaseSwitch.vue";

export default {
  name: "Form",
  components: {BaseSwitch, BaseForm},
  data() {
    return {
      locale: 'es',
      typeCourseAnnouncementStepCreation: {
        id: -1,
        name: '',
        description: '',
        position:0,
        isRequired: false,
        active: false,
        translations: [],
        StepsConfigurations: []
      },
      optionsTypes: ['INTERN','EXTERN']
    };
  },
  computed: {
    catalogs: get('catalogModule/catalogs'),
    locales: get('localeModule/locales'),
  },
  created() {
    if (this.catalogs.length === 0) {
      this.returnToList();
      return;
    }

    let typeCourseAnnouncementStepCreation= {
        id: -1,
        name: '',
        description: '',
        position:0,
        isRequired: false,
        active: false,
        translations: [],
        StepsConfigurations: []
      }

    if (this.$route.name === 'TypeCourseAnnouncementStepCreationUpdate') {
      typeCourseAnnouncementStepCreation = this.catalogs.find(c => c.id === this.$route.params.id);
      if (typeCourseAnnouncementStepCreation === undefined) {
        this.returnToList();
        return;
      }
    }

    let translations = [];

    const keys = Object.keys(this.locales);
    keys.forEach((k) => {
      const translated = typeCourseAnnouncementStepCreation.translations.find(e => e.locale === k);
      translations.push({
        locale: k,
        name: translated?.name ?? '',
        description: translated?.description ?? ''
      })
    });

    typeCourseAnnouncementStepCreation.translations = translations;
    this.typeCourseAnnouncementStepCreation = typeCourseAnnouncementStepCreation;
  },
  methods: {
    returnToList() {
      this.$router.push({name: 'TypeCourseAnnouncementStepCreation', params: this.$route.params});
    },
    changeActiveStatus(index) {
      const value = this.typeCourseAnnouncementStepCreation.StepsConfigurations[index];
      this.$store.dispatch('catalogModule/changeActiveState',
          {endpoint: `/admin/TypeCourse-AnnouncementStepCreation/${value.id}/activeConfiguration`, data: {id: value.id, active: value.active}}).then(r => {

      });
    },

    submit() {
      const update = this.$route.name === 'TypeCourseAnnouncementStepCreationUpdate';
      const endpoint = update ? '/admin/TypeCourse-AnnouncementStepCreation/update' : '/admin/TypeCourse-AnnouncementStepCreation/create';
      const save = () => {
        return this.$store.dispatch('catalogModule/save', { endpoint: endpoint, data: this.typeCourseAnnouncementStepCreation });
      }

      save().then(r => {
        const { error, data } = r;
        if (error) this.$toast.error(data);
        else {
          this.$toast.success(this.$t('CATALOG.SAVED') + '');
          this.returnToList();
        }
      })
    }
  }
}

</script>

<template>
  <base-form v-model="locale" @cancel="returnToList()" @submit="submit()">
    <template v-slot:form>
      <div class="form-group col-12">
        <label>{{ $t('NAME') }}</label>
        <input type="text" class="form-control" v-model="typeCourseAnnouncementStepCreation.name">
      </div>

      <div class="form-group col-12">
        <label>{{ $t('DESCRIPTION') }}</label>
        <textarea class="form-control" v-model="typeCourseAnnouncementStepCreation.description" rows="5"></textarea>
      </div>

      <div class="form-group col-12 d-flex align-items-center justify-content-start">
        <BaseSwitch :tag="`switcher-type-course-form-isrequired-${typeCourseAnnouncementStepCreation.id}`"
                     v-model="typeCourseAnnouncementStepCreation.isRequired"/>
        <label class="ml-1">{{ $t('CATALOG.TYPE_COURSE.ISREQUIRED') }}</label>
      </div>

      <div class="form-group col-12 d-flex align-items-center justify-content-start">
        <BaseSwitch :tag="`switcher-type-course-form-active-${typeCourseAnnouncementStepCreation.id}`"
                     v-model="typeCourseAnnouncementStepCreation.active"/>
        <label class="ml-1">{{ $t('ACTIVE') }}</label>
      </div>

      <div v-if="typeCourseAnnouncementStepCreation.StepsConfigurations.length > 0">        
        <label class="ml-1">{{ $t('CATALOG.TYPECOURSE_ANNOUNCEMENT_STEPCONFIGURATION.CONFIGURATION') }}</label>
        <div v-for="(StepsConfiguration, index) in typeCourseAnnouncementStepCreation.StepsConfigurations" :key="StepsConfiguration.id" 
            class="form-group col-12 d-flex align-items-center justify-content-start">
            <BaseSwitch :tag="`switcher-StepsConfiguration-AnnouncementStepCreation-${StepsConfiguration.id}`" 
                          v-model="StepsConfiguration.active"/>
            <label class="ml-1">{{ StepsConfiguration.name }}</label>

        </div>
      </div>
      
    </template>
    <template v-slot:translations>
      <div v-for="t in typeCourseAnnouncementStepCreation.translations" :key="t.locale" v-if="t.locale === locale">
        <div class="form-group col-12">
          <label>{{ $t('NAME') }}</label>
          <input type="text" class="form-control" v-model="t.name">
        </div>

        <div class="form-group col-12">
          <label>{{ $t('DESCRIPTION') }}</label>
          <textarea class="form-control" v-model="t.description" rows="5"></textarea>
        </div>
      </div>
    </template>
  </base-form>
</template>

<style scoped lang="scss">

</style>
