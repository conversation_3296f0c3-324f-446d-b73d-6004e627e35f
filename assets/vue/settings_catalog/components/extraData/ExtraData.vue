<script>
import Spinner from "../../../admin/components/base/Spinner.vue";
import {get, sync} from "vuex-pathify";
import BaseSwitch from "../../../base/BaseSwitch.vue";

export default {
  name: "ExtraData",
  components: {BaseSwitch, Spinner},
  computed: {
    loading: get('catalogModule/loading'),
    catalogs: sync('catalogModule/catalogs'),
  },

  created() { 
    this.$store.dispatch('catalogModule/load', '/admin/extra-data/all'); 
  },
  methods: {
    changeActiveStatus(index) {
      const value = this.catalogs[index];
      this.$store.dispatch('catalogModule/changeActiveState',
          {endpoint: `/admin/extra-data/${value.id}/state`, data: {id: value.id, active: value.active}}).then(r => {
      });
    }
  }
}
</script>

<template>
  <div>
    <div class="col-12 d-flex align-items-center justify-content-end">
      <router-link type="button" class="btn btn-primary"
                   :to="{name: 'ExtraDataCreate', params: this.$route.params}"
      >
        {{ $t('COMMON.CREATE') }}
      </router-link>
    </div>
    <div class="col-12 d-flex flex-row align-items-center justify-content-center" v-if="loading">
      <spinner />
    </div>    

    <table class="table table-condensed mt-3" v-else>
      <thead>
      <tr>
        <th></th>
        <th>{{ $t('NAME') }}</th>
        <th>{{ $t('CATALOG.EXTRA_DATA.DESCRIPTION') }}</th>
        <th>{{ $t('ACTIVE') }}</th>
        <th>{{ $t('ACTION') }}</th>
        <th></th>
      </tr>
      </thead>
      <tbody>
      <tr v-for="(c, index) in catalogs" :key="c.id">
        <td></td>
        <td>{{ c.name }}</td>
        <td>{{ c.description }}</td>
        <td>
          <BaseSwitch :tag="`switcher-extra-data-${c.id}`" v-model="c.active" @change="changeActiveStatus(index)" />
        </td>
        <td>
          <div class="dropdown">
            <button class="btn btn-default" type="button" :id="`dropdown-menu-${c.id}`" data-bs-toggle="dropdown" aria-expanded="false">
              <i class="fa fa-ellipsis-h"></i>
            </button>
            <ul class="dropdown-menu" :aria-labelledby="`dropdown-menu-${c.id}`">
              <li><router-link class="dropdown-item" :to="{ name: 'ExtraDataUpdate', params: {...$route.params, id: c.id} }">{{ $t('EDIT') }}</router-link></li>
            </ul>
          </div>
        </td>
      </tr>
      </tbody>
    </table>
  </div>
</template>

<style scoped lang="scss">

</style>
