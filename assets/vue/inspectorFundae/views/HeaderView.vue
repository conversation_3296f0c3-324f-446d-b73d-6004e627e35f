<template>
  <div class="HeaderView bg-dark">
    <div class="content d-flex justify-content-between align-items-center p-3">
      <h4 class="text-uppercase mb-0">{{ $t('INSPECTORVIEW.TITLE') }}</h4>
      <button @click="logout()" type="button" class="btn btn-primary">
        <i class="fa fa-sign-out-alt mr-2"></i>{{ $t('INSPECTORVIEW.LOGOUT') }}
      </button>
    </div>
  </div>
</template>

<script>

import {get} from "vuex-pathify";

export default {
  name: "HeaderView",
  computed: {
    isAuthenticated: get('authModule/isAuthenticated')
  },
  methods: {
    logout() {
      this.$store.dispatch('authModule/logout');
    }
  }
};
</script>

<style scoped lang="scss">
.HeaderView {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
}
</style>
