import axios from "axios";
export default {
    namespaced: true,
    state: {
        loading: true
    },
    getters: {
        isLoading(state) { return state.loading; }
    },
    mutations: {
        SET_LOADING(state, isLoading) { state.loading = isLoading; }
    },
    actions: {
        loadChatData({ }, payload) {
            return axios.post(`/admin/chat/channel/messages`, payload)
              .then((result) => result.data);
        },

        getUserChat({}, { parentId, userId }) {
            return axios.post('/admin/chat/channel', { parentId, userId })
              .then((result) => result.data);
        },

        sendMessage({}, { channelId, message}) {
            return axios.post('/admin/chat/message/send', {channelId, message})
              .then((result) => result.data);
        }
    }
}
