<template>
  <div>
    <label class="input-label">
      {{ labelText }}
      <sup class="text-danger" v-if="isRequired">*</sup>
    </label>
    <div class="input-wrapper">
      <input
        class="form-control"
        :value="value"
        @input="onInput"
        :maxlength="max"
        :minlength="min"
        :required="isRequired"
        :placeholder="placeholder"
        :class="{ 'is-invalid': isInvalid && submitted, 'custom-border': hasCustomBorder, }"
        @keydown="onKeyDown"
      />
      <div class="characters-info">
        <div class="characters-remaining" v-if="max > 0">
          {{ translationsVue.remaining_characters }}: {{ remaining }}
        </div>
        <div class="minimum-characters" v-if="showMinMessage">
          {{ translationsVue.minimiun_characters }}: {{ min }}
        </div>
      </div>
      <div
        class="required-error-message"
        v-if="isRequired && submitted && value.length === 0"
      >
        {{ requiredErrorMessage }}
      </div>
    </div>
  </div>
</template>
  
  <script>
export default {
  props: {
    label: {
      type: String,
      default: "",
    },
    placeholder: {
      type: String,
      default: "",
    },
    min: {
      type: Number,
      default: 0,
    },
    max: {
      type: Number,
      required: false,
    },
    initialText: {
      type: String,
      default: "",
    },
    required: {
      type: Boolean,
      default: false,
    },
    rows: {
      type: Number,
      default: 3,
    },
    value: {
      type: String,
      default: "",
    },
    submitted: {
      type: Boolean,
      default: false,
    },
    requiredErrorMessage: {
      type: String,
      default: translationsVue.field_required,
    },
    preventSpace: {
      type: Boolean,
      default: false,
    },
    customBorderColor: {
      type: String,
      default: "",
    },
    validateSpecialCharacters: {
      type: Boolean,
      default: false,
    },

    acceptSpecialCharactersEnie: {
      type: Boolean,
      default: false,
    },

    acceptSpecialCharactersAccent: {
      type: Boolean,
      default: false,
    },
    upperCaseCharacters:{
      type: Boolean,
      default: false,
    }
  },

  data() {
    return {
      translationsVue,
    };
  },
  computed: {
    labelText() {
      return this.label || "Título por defecto";
    },
    remaining() {
      return this.max - this.value.length;
    },
    showMinMessage() {
      return this.min > 0 && this.value.length < this.min;
    },
    isRequired() {
      return this.required && (this.min > 0 || this.value.length === 0);
    },
    isInvalid() {
      return this.isRequired && this.value.length === 0;
    },
    hasCustomBorder() {
      return this.customBorderColor !== "";
    },
    customBorderStyle() {
      return {
        borderColor: this.customBorderColor,
      };
    },
  },

  methods: {
    onKeyDown(event) {
      if (this.preventSpace && event.keyCode === 32) {
        event.preventDefault();
      }
    },

    onInput(event) {
      let sanitizedValue = event.target.value;

      if (this.validateSpecialCharacters) {
        if (
            this.acceptSpecialCharactersEnie &&
            this.acceptSpecialCharactersAccent
        ) {
          sanitizedValue = sanitizedValue.replace(/[^\wñÑáéíóúÁÉÍÓÚ`´]/g, "");
        } else if (this.acceptSpecialCharactersEnie && !this.acceptSpecialCharactersAccent) {
          sanitizedValue = sanitizedValue.replace(/[^a-zA-ZñÑ]/g, "");
        } else if (this.acceptSpecialCharactersAccent && !this.acceptSpecialCharactersEnie) {
          sanitizedValue = sanitizedValue.replace(/[^a-zA-ZáéíóúÁÉÍÓÚ`´]/g, "");
        } else {
          sanitizedValue = sanitizedValue.replace(/[^a-zA-Z]/g, "");
        }
      }

      if(this.upperCaseCharacters){
        sanitizedValue = sanitizedValue.toUpperCase();
      }

      if (sanitizedValue !== event.target.value) {
        event.target.value = sanitizedValue;
      }

      this.$emit("update:value", sanitizedValue);
    },

  },
};
</script>
  
   <style scoped lang="scss"> 
.input-wrapper {
  position: relative;
}

.input-label {
  font-weight: normal;
}

.characters-info {
  position: relative;
  text-align: right;
  display: flex;
  flex-direction: row;
  gap: 12px;
  color: #888;
  font-size: 10px;
  margin-top: 0.5rem;
  justify-content: flex-end;
}

.characters-remaining,
.minimum-characters {
  white-space: nowrap;
}

.is-invalid {
  border: 1px solid red;
}

.required-error-message {
  color: red;
  font-size: 12px;
  top: -1.4rem;
  position: absolute;
  right: 0;
}
</style>
  