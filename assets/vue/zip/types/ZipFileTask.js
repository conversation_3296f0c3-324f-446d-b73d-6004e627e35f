export default class ZipFileTask {
    static STATUS_FAILURE = -1;
    static STATUS_TIMEOUT = -2;
    static STATUS_PENDING = 0;
    static STATUS_IN_PROGRESS = 1;
    static STATUS_SUCCESS = 2;

    constructor({id, type, entityId, params, status, createdAt, finishedAt = null, startedAt = null, filename = null, originalName, available_at=null}) {
        this.id = id;
        this.type = type;
        this.entityId = entityId;
        this.params = params;
        this.status = status;
        this.createdAt = createdAt ? Date.parse(createdAt) : null;
        this.finishedAt = finishedAt ? Date.parse(finishedAt) : null;
        this.startedAt = startedAt ? Date.parse(startedAt) : null;
        this.available_at = available_at ? Date.parse(available_at) : null;
        this.filename = filename;
        this.originalName = originalName;
        switch (this.status) {
            case ZipFileTask.STATUS_SUCCESS:
                this.statusText = "STATUS_INFORMATION.COMPLETED";
                break;
            case ZipFileTask.STATUS_IN_PROGRESS:
                this.statusText = "STATUS_INFORMATION.IN_PROGRESS";
                break;
            case ZipFileTask.STATUS_PENDING:
                this.statusText = "STATUS_INFORMATION.PENDING";
                break;
            case ZipFileTask.STATUS_TIMEOUT:
                this.statusText = "STATUS_INFORMATION.TIMEOUT";
                break;
            default:
                this.statusText = "STATUS_INFORMATION.FAILED";
        }
    }
}
