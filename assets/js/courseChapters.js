import $ from "jquery";
import 'jquery-ui';
import 'jquery-ui/ui/widgets/sortable'
import axios from "axios";

$(function () {
    const $seasonFilter = $('#season-filter');
    const countSeasonFilter = $('#season-filter option').length;

    if(countSeasonFilter > 1){
        const $chapters = $('.chapter');
        if(!$seasonFilter.val()) {
            $chapters.attr('draggable', 'true');
            $chapters.on('dragstart', function (event) {
                //event.preventDefault();
                const textTrans = getTranslation('ALERT_SEASON_EMPTY', locale);
                let html = '<font color="red"><b>' + textTrans + ' <i class="fas fa-arrow-alt-circle-right"></i></b></font>&nbsp;&nbsp;'
                const $alertSeason = $('#alert-season-empty');
                $alertSeason.html(html);
                setTimeout(function () {
                    $('#alert-season-empty').html('');
                }, 3000);
                $alertSeason.focus();
            });
        }
    }

    $(".course-chapters").sortable({
        start: function (event, ui) {
            $(ui.helper).css('width', `${$(ui.item).width()}px`);
        },
        stop: function (event, ui) {
            let posicion = 1;
            const orden = [];
            const seasonId = $seasonFilter.val();

            $(".course-chapters").children('[data-season="' + seasonId + '"]').each(function () {
                orden.push($(this).data('id'));
                $(this).find('.order').html(posicion);
                posicion++;
            });

            const orderUrl = $('.course-chapters').data('orderurl');

            let orderRequest = $.ajax({type: "POST", url: orderUrl, data: {orden: orden}});

            orderRequest.done(function (result) {
            });

            orderRequest.fail(function () {
                location.reload();
            });
        },
    });

    $seasonFilter.on('change', function () {
        showCourseChaptersBySeason($(this).val());
    });

    showCourseChaptersBySeason($seasonFilter.val());


    $(".course-seasons tbody").sortable({
        start: function (event, ui) {
            $(ui.helper).css('width', `${$(ui.item).width()}px`);
        },
        stop: function (event, ui) {
            let sortUrl = $(this).parent('table').data('sort-url');

            const order = [];

            $(this).children().each(function () {
                order.push($(this).data('id'));
            });

            $.ajax({type: 'POST', url: sortUrl, data: {order: order}})
                .done(function (response) {
                    console.log(response);
                })
                .fail(function () {
                    //   location.reload();
                });
        },
    });

    $('.page-item').on('click', function (e) {
        console.log('click');
        $("html, body").animate({ scrollTop: $(document).height() });
    });

    document.getElementById('share-course-button').onclick = () => {
        console.log('sharecourse ' + idCourse)
        axios.get(` / admin / course / ${idCourse} / share`).then(r => {
            console.log(r);
            const { data, error } = r.data;
            if (!error) {
                const shareUrlContainer = $('#share-url-container');
                const temp = $('#share-url-value');
                if (temp) {
                    temp.remove();
                }

                navigator.clipboard.writeText(data).then(() => {
                    $('#share-url-container > b').css('display', 'none');
                    shareUrlContainer.append('<span id="share-url-value">URL Copied</span>');
                    shareUrlContainer.css('display', 'block');
                    setTimeout(() => {
                        $('#share-url-container').css('display', 'none');
                    }, 2000);
                }, () => {
                    $('#share-url-container > b').css('display', 'block');
                    shareUrlContainer.css('display', 'block');
                    shareUrlContainer.append(` < span id = "share-url-value" > ${data} < / span > `);
                });
            }
        })
    };
});

function showCourseChaptersBySeason(seasonId)
{
    if (seasonId !== '' && seasonId !== undefined) {
        $('.chapter').hide();
        $('*[data-season="' + seasonId + '"]').show();
        $('.course-chapters').sortable('option', 'disabled', false);
    } else {
        $('.chapter').show();
        $('.course-chapters').sortable('disable')
    }
}

function getTranslation(worldTrans, language = 'es'){
    const locales = Object.entries(loadLocalesTranslate());
    let trans = worldTrans;
    locales.forEach(([key, value]) => {
        if(key === language){
            const obj = Object.entries(value);
            obj.forEach(([row, values]) => {
                if(worldTrans === row){
                    trans = values;
                }
            })
        }
    });
    return trans;
}
function loadLocalesTranslate() {
    const locales = require.context('../vue/locales', true, /[A-Za-z0-9-_,\s]+\.json$/i);
    const messages = {};
    locales.keys().forEach(key => {
        const matched = key.match(/([A-Za-z0-9-_]+)\./i);
        if (matched && matched.length > 1) {
            const locale = matched[1];
            messages[locale] = locales(key);
        }
    });
    return messages;
}