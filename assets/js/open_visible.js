import $ from 'jquery';
import 'bootstrap'

$(function () {

    $('#Course_open').change(courseOpeningModalityVisibility);
    courseOpeningModalityVisibility();
    isVisibleEntitySubsidizer();
});

function courseOpeningModalityVisibility() {
    let $courseOpeningModality = $('#Course_open_visible');
    let $wrapper = $courseOpeningModality.parents('.form-group').first();

    let progressiveOpening = $('#Course_open').is(':checked');

    if (progressiveOpening) {
        $wrapper.show();
    } else {
        $wrapper.hide();
    }
}

function isVisibleEntitySubsidizer() {
    let $npsQuestionSource = $('#NpsQuestion_source');
    let $entitySubsidizer = $('.entitySubsidizer');
    let $announcementQuestion = $('.announcementQuestion');
    let $courseQuestion = $('.courseQuestion');

    $npsQuestionSource.change(function () {
        if (this.value === '2') {
            $entitySubsidizer.show();
            $announcementQuestion.show();
            $courseQuestion.hide();
        }
        else{
            $entitySubsidizer.hide();
            $announcementQuestion.hide();
            $courseQuestion.show();
        }
    });

    if($npsQuestionSource.val() === '2'){
        $entitySubsidizer.show();
        $announcementQuestion.show();
        $courseQuestion.hide();
    }
    else{
        $entitySubsidizer.hide();
        $announcementQuestion.hide();
        $courseQuestion.show();
    }

}
