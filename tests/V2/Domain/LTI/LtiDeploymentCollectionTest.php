<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\LTI;

use App\Tests\V2\Domain\Shared\Collection\CollectionTestCase;
use App\Tests\V2\Mother\LTI\LtiDeploymentMother;
use App\V2\Domain\LTI\LtiDeployment;
use App\V2\Domain\LTI\LtiDeploymentCollection;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Collection\CollectionException;

class LtiDeploymentCollectionTest extends CollectionTestCase
{
    /**
     * @throws CollectionException
     */
    protected function createCollection(array $items): Collection
    {
        return new LtiDeploymentCollection($items);
    }

    protected function getExpectedType(): string
    {
        return LtiDeployment::class;
    }

    protected function getItem(): object
    {
        return LtiDeploymentMother::create();
    }
}
