<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\LTI;

use App\Tests\V2\Mother\LTI\LtiDeploymentMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\LTI\Exceptions\LtiDeploymentNotFoundException;
use App\V2\Domain\LTI\Exceptions\LtiException;
use App\V2\Domain\LTI\LtiDeploymentCollection;
use App\V2\Domain\LTI\LtiDeploymentCriteria;
use App\V2\Domain\LTI\LtiDeploymentRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\UuidCollection;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

abstract class LtiDeploymentRepositoryTestCase extends TestCase
{
    abstract protected function getRepository(): LtiDeploymentRepository;

    /**
     * @throws InfrastructureException
     * @throws LtiDeploymentNotFoundException
     * @throws InvalidUuidException
     */
    public function testPut(): void
    {
        $registrationId1 = UuidMother::create();
        $registrationId2 = UuidMother::create();

        $deployment1 = LtiDeploymentMother::create(
            registrationId: $registrationId1
        );

        $deployment2 = LtiDeploymentMother::create(
            registrationId: $registrationId1
        );

        $deployment3 = LtiDeploymentMother::create(
            registrationId: $registrationId2
        );

        $repository = $this->getRepository();

        $repository->put($deployment1);

        try {
            $repository->put($deployment2);
            $this->fail('Expected exception not thrown');
        } catch (LtiException $e) {
            $this->assertEquals(
                LtiException::deploymentIdMustBeUniqueInRegistrationContext(),
                $e,
            );
        }

        $repository->put($deployment3);

        $this->assertEquals(
            $deployment3,
            $repository->findOneBy(
                LtiDeploymentCriteria::createEmpty()
                    ->filterByRegistrationId($registrationId2)
            )
        );
    }

    /**
     * @throws InfrastructureException
     * @throws LtiDeploymentNotFoundException
     * @throws InvalidUuidException
     */
    public function testFindOneBy(): void
    {
        $registrationId1 = UuidMother::create();
        $registrationId2 = UuidMother::create();

        $deployment1 = LtiDeploymentMother::create(
            registrationId: $registrationId1,
            name: 'Deployment 1'
        );

        $deployment2 = LtiDeploymentMother::create(
            registrationId: $registrationId1,
            name: 'Deployment 2',
            deploymentId: 'deployment-2'
        );

        $deployment3 = LtiDeploymentMother::create(
            registrationId: $registrationId2,
            name: 'deployment-3',
        );

        $deployment4 = LtiDeploymentMother::create(
            registrationId: $registrationId2,
            name: 'Deployment 4',
            deploymentId: 'deployment-2'
        );

        $repository = $this->getRepository();

        $repository->put($deployment1);
        $repository->put($deployment2);
        $repository->put($deployment3);
        $repository->put($deployment4);

        $this->assertCount(
            4,
            $repository->findBy(LtiDeploymentCriteria::createEmpty())
        );

        $this->assertEquals(
            $deployment1,
            $repository->findOneBy(
                LtiDeploymentCriteria::createEmpty()
            )
        );

        $this->assertEquals(
            $deployment2,
            $repository->findOneBy(
                LtiDeploymentCriteria::createEmpty()
                    ->filterByDeploymentId('deployment-2')
            )
        );

        $this->assertEquals(
            $deployment4,
            $repository->findOneBy(
                LtiDeploymentCriteria::createEmpty()
                    ->filterByDeploymentId('deployment-2')
                    ->filterByRegistrationId($registrationId2)
            )
        );

        $this->assertEquals(
            $deployment3,
            $repository->findOneBy(
                LtiDeploymentCriteria::createById($deployment3->getId())
                    ->filterByDeploymentId('deploymentId')
                    ->filterByRegistrationId($registrationId2)
            )
        );
    }

    /**
     * @throws InfrastructureException
     */
    #[DataProvider('provideFindBy')]
    public function testFindBy(
        LtiDeploymentCollection $source,
        LtiDeploymentCriteria $criteria,
        int $expectedCount,
        array $expectedResult,
    ): void {
        $repository = $this->getRepository();
        foreach ($source->all() as $deployment) {
            $repository->put($deployment);
        }

        $result = $repository->findBy(LtiDeploymentCriteria::createEmpty());
        $this->assertEquals($source->count(), $result->count());
        $this->assertCount(
            0,
            array_diff(
                $source->all(),
                $result->all(),
            )
        );

        $result = $repository->findBy($criteria);
        $this->assertCount($expectedCount, $result);
        $this->assertCount(
            0,
            array_diff(
                $expectedResult,
                $result->all(),
            )
        );
    }

    /**
     * @throws CollectionException
     * @throws InvalidUuidException
     * @throws CriteriaException
     */
    public static function provideFindBy(): \Generator
    {
        $registrationId1 = UuidMother::create();
        $registrationId2 = UuidMother::create();

        $deployment1 = LtiDeploymentMother::create(
            registrationId: $registrationId1,
            deploymentId: 'deployment-1',
        );

        $deployment2 = LtiDeploymentMother::create(
            registrationId: $registrationId1,
            deploymentId: 'deployment-2',
        );

        $deployment3 = LtiDeploymentMother::create(
            registrationId: $registrationId2,
            deploymentId: 'deployment-1',
        );

        $deployment4 = LtiDeploymentMother::create(
            registrationId: $registrationId2,
            deploymentId: 'deployment-2',
        );

        yield '4 entries all results' => [
            'source' => new LtiDeploymentCollection([$deployment1, $deployment2, $deployment3, $deployment4]),
            'criteria' => LtiDeploymentCriteria::createEmpty(),
            'expectedCount' => 4,
            'expectedResult' => [$deployment1, $deployment2, $deployment3, $deployment4],
        ];

        yield '4 entries by registrationId' => [
            'source' => new LtiDeploymentCollection([$deployment1, $deployment2, $deployment3, $deployment4]),
            'criteria' => LtiDeploymentCriteria::createEmpty()->filterByRegistrationId($registrationId2),
            'expectedCount' => 2,
            'expectedResult' => [$deployment3, $deployment4],
        ];

        yield '4 entries by deploymentId' => [
            'source' => new LtiDeploymentCollection([$deployment1, $deployment2, $deployment3, $deployment4]),
            'criteria' => LtiDeploymentCriteria::createEmpty()->filterByDeploymentId('deployment-2'),
            'expectedCount' => 2,
            'expectedResult' => [$deployment2, $deployment4],
        ];

        yield '4 entries by registrationId and deploymentId' => [
            'source' => new LtiDeploymentCollection([$deployment1, $deployment2, $deployment3, $deployment4]),
            'criteria' => LtiDeploymentCriteria::createEmpty()
                ->filterByRegistrationId($registrationId1)
                ->filterByDeploymentId('deployment-1'),
            'expectedCount' => 1,
            'expectedResult' => [$deployment1],
        ];

        yield '4 entries by id by registrationId and deploymentId' => [
            'source' => new LtiDeploymentCollection([$deployment1, $deployment2, $deployment3, $deployment4]),
            'criteria' => LtiDeploymentCriteria::createById($deployment3->getId())
                ->filterByRegistrationId($registrationId2)
                ->filterByDeploymentId('deployment-1'),
            'expectedCount' => 1,
            'expectedResult' => [$deployment3],
        ];

        yield '4 entries by id collection' => [
            'source' => new LtiDeploymentCollection([$deployment1, $deployment2, $deployment3, $deployment4]),
            'criteria' => LtiDeploymentCriteria::createByIds(new UuidCollection([
                $deployment2->getId(), $deployment3->getId(),
            ])),
            'expectedCount' => 2,
            'expectedResult' => [$deployment2, $deployment3],
        ];

        yield '4 entries by registration1 and 2' => [
            'source' => new LtiDeploymentCollection([$deployment1, $deployment2, $deployment3, $deployment4]),
            'criteria' => LtiDeploymentCriteria::createEmpty()
                ->filterByRegistrationIds(new UuidCollection([$registrationId1, $registrationId2])),
            'expectedCount' => 4,
            'expectedResult' => [$deployment1, $deployment2, $deployment3, $deployment4],
        ];
    }

    /**
     * @throws InfrastructureException
     * @throws InvalidUuidException
     */
    public function testDelete(): void
    {
        $deployment1 = LtiDeploymentMother::create();
        $deployment2 = LtiDeploymentMother::create();
        $deployment3 = LtiDeploymentMother::create();
        $deployment4 = LtiDeploymentMother::create();

        $repository = $this->getRepository();

        $repository->put($deployment1);
        $repository->put($deployment2);
        $repository->put($deployment3);
        $repository->put($deployment4);

        $this->assertCount(
            4,
            $repository->findBy(LtiDeploymentCriteria::createEmpty())->all()
        );

        $repository->delete($deployment2);
        $result = $repository->findBy(LtiDeploymentCriteria::createEmpty());
        $this->assertCount(3, $result);
        $this->assertCount(
            0,
            array_diff(
                [$deployment1, $deployment3, $deployment4],
                $result->all()
            )
        );
    }
}
