<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Course;

use App\Tests\Mother\Entity\CourseMother;
use App\V2\Domain\Course\CourseCriteria;
use App\V2\Domain\Course\CourseRepository;
use App\V2\Domain\Course\Exceptions\CourseNotFoundException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;
use PHPUnit\Framework\TestCase;

abstract class CourseRepositoryTestCase extends TestCase
{
    abstract protected function getRepository(): CourseRepository;

    /**
     * @throws InfrastructureException
     * @throws CriteriaException
     * @throws CourseNotFoundException
     */
    public function testPut(): void
    {
        $repository = $this->getRepository();

        $course1 = CourseMother::create(
            id: 1,
        );

        $course2 = CourseMother::create(
            id: 2,
        );

        $repository->put($course1);
        $repository->put($course2);

        $found = $repository->findOneBy(
            CourseCriteria::createEmpty()->filterById(new Id(1))
        );

        $this->assertEquals($course1, $found);
    }

    /**
     * @throws InfrastructureException
     * @throws CriteriaException
     * @throws CourseNotFoundException
     */
    public function testFindOneBy(): void
    {
        $course1 = CourseMother::create(id: 1);
        $course2 = CourseMother::create(id: 2);
        $course3 = CourseMother::create(id: 3);

        $repository = $this->getRepository();
        $repository->put($course1);
        $repository->put($course2);
        $repository->put($course3);

        $found = $repository->findOneBy(
            CourseCriteria::createEmpty()->filterById(new Id(2))
        );

        $this->assertEquals($course2, $found);
    }

    /**
     * @throws InfrastructureException
     * @throws CriteriaException
     * @throws CourseNotFoundException
     */
    public function testFindOneByThrowsExceptionWhenCourseDoesNotExist(): void
    {
        $repository = $this->getRepository();

        $this->expectException(CourseNotFoundException::class);

        $repository->findOneBy(
            CourseCriteria::createEmpty()->filterById(new Id(1))
        );
    }

    /**
     * @throws InfrastructureException
     * @throws CriteriaException
     */
    public function testFindBy(): void
    {
        $course1 = CourseMother::create(id: 1);
        $course2 = CourseMother::create(id: 2);
        $course3 = CourseMother::create(id: 3);

        $repository = $this->getRepository();
        $repository->put($course1);
        $repository->put($course2);
        $repository->put($course3);

        $found = $repository->findBy(
            CourseCriteria::createEmpty()->filterById(new Id(2))
        );

        $this->assertCount(1, $found);
        $this->assertEquals($course2, $found->first());
    }

    /**
     * @throws InfrastructureException
     * @throws CriteriaException
     */
    public function testFindByReturnsEmptyCollectionWhenNoCoursesMatch(): void
    {
        $course1 = CourseMother::create(id: 1);
        $course2 = CourseMother::create(id: 2);
        $course3 = CourseMother::create(id: 3);

        $repository = $this->getRepository();
        $repository->put($course1);
        $repository->put($course2);
        $repository->put($course3);

        $found = $repository->findBy(
            CourseCriteria::createEmpty()->filterById(new Id(4))
        );

        $this->assertCount(0, $found);
    }

    /**
     * @throws InfrastructureException
     * @throws CriteriaException
     */
    public function testCountBy(): void
    {
        $course1 = CourseMother::create(id: 1);
        $course2 = CourseMother::create(id: 2);
        $course3 = CourseMother::create(id: 3);

        $repository = $this->getRepository();
        $repository->put($course1);
        $repository->put($course2);
        $repository->put($course3);

        $count = $repository->countBy(
            CourseCriteria::createEmpty()->filterById(new Id(2))
        );

        $this->assertEquals(1, $count);
    }
}
