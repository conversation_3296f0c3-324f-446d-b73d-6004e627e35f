<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Diploma\Helper;

use App\V2\Domain\Diploma\Helper\DateHelper;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class DateHelperTest extends TestCase
{
    /**
     * Test getDateToUse with different scenarios.
     */
    #[DataProvider('dateScenarioProvider')]
    public function testGetDateToUse(
        ?string $stringDate,
        ?\DateTime $dateTime,
        string $expectedScenario,
        ?string $expectedDateString = null
    ): void {
        // Act
        $result = DateHelper::getDateToUse($stringDate, $dateTime);

        // Assert
        $this->assertInstanceOf(\DateTime::class, $result, 'Should always return a DateTime object');

        switch ($expectedScenario) {
            case 'specific_date':
                $this->assertEquals($expectedDateString, $result->format('Y-m-d'), 'Should match expected specific date');
                break;
            case 'datetime_object':
                $this->assertEquals($dateTime->format('Y-m-d H:i:s'), $result->format('Y-m-d H:i:s'), 'Should use DateTime object when no string date');
                break;
            case 'current_date':
                $now = new \DateTime();
                $this->assertEquals($now->format('Y-m-d'), $result->format('Y-m-d'), 'Should use current date as fallback');
                break;
        }
    }

    public static function dateScenarioProvider(): \Generator
    {
        // Test string date priority (different formats)
        yield 'string_date_ISO_format' => [
            'stringDate' => '2024-01-15',
            'dateTime' => new \DateTime('2024-01-10'),
            'expectedScenario' => 'specific_date',
            'expectedDateString' => '2024-01-15',
        ];

        yield 'string_date_US_format' => [
            'stringDate' => '01/15/2024',
            'dateTime' => new \DateTime('2024-01-10'),
            'expectedScenario' => 'specific_date',
            'expectedDateString' => '2024-01-15',
        ];

        yield 'string_date_with_time' => [
            'stringDate' => '2024-01-15 14:30:00',
            'dateTime' => new \DateTime('2024-01-10'),
            'expectedScenario' => 'specific_date',
            'expectedDateString' => '2024-01-15',
        ];

        // Test DateTime object when no string date
        yield 'datetime_object_when_null_string' => [
            'stringDate' => null,
            'dateTime' => new \DateTime('2024-01-10 14:30:00'),
            'expectedScenario' => 'datetime_object',
        ];

        yield 'datetime_object_when_empty_string' => [
            'stringDate' => '',
            'dateTime' => new \DateTime('2024-01-10 14:30:00'),
            'expectedScenario' => 'datetime_object',
        ];

        // Test current date fallback
        yield 'current_date_when_both_null' => [
            'stringDate' => null,
            'dateTime' => null,
            'expectedScenario' => 'current_date',
        ];

        yield 'current_date_when_empty_string_and_null_datetime' => [
            'stringDate' => '',
            'dateTime' => null,
            'expectedScenario' => 'current_date',
        ];
    }
}
