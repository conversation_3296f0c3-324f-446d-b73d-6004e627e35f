<?php

declare(strict_types=1);

namespace App\Tests\V2\Mother\Announcement\Manager;

use App\V2\Domain\Announcement\Manager\AnnouncementManagerCollection;

class AnnouncementManagerCollectionMother
{
    public static function create(array $announcementManagers = []): AnnouncementManagerCollection
    {
        if (empty($announcementManagers)) {
            $announcementManagers = [
                AnnouncementManagerMother::create(),
                AnnouncementManagerMother::create(),
            ];
        }

        return new AnnouncementManagerCollection($announcementManagers);
    }

    public static function empty(): AnnouncementManagerCollection
    {
        return new AnnouncementManagerCollection([]);
    }

    public static function withSingleItem(): AnnouncementManagerCollection
    {
        return new AnnouncementManagerCollection([
            AnnouncementManagerMother::create(),
        ]);
    }
}
