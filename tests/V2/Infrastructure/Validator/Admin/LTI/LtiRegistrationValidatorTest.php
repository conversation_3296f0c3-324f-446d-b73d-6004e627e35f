<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Validator\Admin\LTI;

use App\Tests\V2\Infrastructure\Validator\ValidatorTestCase;
use App\V2\Infrastructure\Validator\Admin\LTI\LtiRegistrationValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use PHPUnit\Framework\Attributes\DataProvider;

class LtiRegistrationValidatorTest extends ValidatorTestCase
{
    /**
     * @throws ValidatorException
     */
    #[DataProvider('providePostRegistrationValidation')]
    public function testPostRegistrationValidation(array $payload): void
    {
        $this->expectNotToPerformAssertions();

        LtiRegistrationValidator::validatePostRegistration($payload);
    }

    public static function providePostRegistrationValidation(): \Generator
    {
        yield 'valid data 1' => [
            'payload' => [
                'name' => 'Test Name',
                'client_id' => 'Client ID 1',
            ],
        ];

        yield 'valid data 2' => [
            'payload' => [
                'name' => 'Test Name',
                'client_id' => 'Client ID 1',
            ],
        ];
    }

    #[DataProvider('provideInvalidPostRegistrationValidation')]
    public function testInvalidPostRegistrationValidation(array $payload, array $violations): void
    {
        try {
            LtiRegistrationValidator::validatePostRegistration($payload);
            $this->fail('Expected exception not thrown');
        } catch (ValidatorException $e) {
            $this->assertViolations($violations, $e->getViolations());
        }
    }

    public static function provideInvalidPostRegistrationValidation(): \Generator
    {
        yield 'empty body' => [
            'payload' => [],
            'violations' => [
                '' => 'Body cannot be empty',
                '[name]' => 'This field is missing.',
                '[client_id]' => 'This field is missing.',
            ],
        ];

        yield 'only name' => [
            'payload' => [
                'name' => 'Test Name',
            ],
            'violations' => [
                '[client_id]' => 'This field is missing.',
            ],
        ];

        yield 'only client_id' => [
            'payload' => [
                'client_id' => 'Test ClientId',
            ],
            'violations' => [
                '[name]' => 'This field is missing.',
            ],
        ];

        yield 'name is not string' => [
            'payload' => [
                'name' => 1,
                'client_id' => 'Test ClientId',
            ],
            'violations' => [
                '[name]' => 'This value should be of type string.',
            ],
        ];

        yield 'client id is not string' => [
            'payload' => [
                'name' => 'Test name',
                'client_id' => 1,
            ],
            'violations' => [
                '[client_id]' => 'This value should be of type string.',
            ],
        ];

        yield 'empty name and client_id' => [
            'payload' => [
                'name' => '',
                'client_id' => '',
            ],
            'violations' => [
                '[name]' => 'This value should not be blank.',
                '[client_id]' => 'This value should not be blank.',
            ],
        ];
    }
}
