<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Bus;

use App\V2\Domain\Bus\Query;
use App\V2\Infrastructure\Bus\TacticianQueryBus;
use League\Tactician\CommandBus as TacticianBus;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class TacticianQueryBusTest extends TestCase
{
    /**
     * Test that ask calls the tactician bus with the query and returns the result.
     *
     * @throws Exception
     */
    public function testAsk(): void
    {
        // Arrange
        $query = $this->createMock(Query::class);
        $expectedResult = 'result';

        $tacticianBus = $this->createMock(TacticianBus::class);
        $tacticianBus->expects($this->once())
            ->method('handle')
            ->with($query)
            ->willReturn($expectedResult);

        $bus = new TacticianQueryBus($tacticianBus);

        // Act
        $result = $bus->ask($query);

        // Assert
        $this->assertSame($expectedResult, $result);
    }
}
