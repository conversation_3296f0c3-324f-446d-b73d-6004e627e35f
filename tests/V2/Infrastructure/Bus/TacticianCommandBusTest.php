<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Bus;

use App\V2\Domain\Bus\Command;
use App\V2\Infrastructure\Bus\TacticianCommandBus;
use League\Tactician\CommandBus as TacticianBus;
use PHPUnit\Framework\TestCase;

class TacticianCommandBusTest extends TestCase
{
    /**
     * Test that dispatch calls the tactician bus with the command.
     */
    public function testDispatch(): void
    {
        // Arrange
        $command = $this->createMock(Command::class);

        $tacticianBus = $this->createMock(TacticianBus::class);
        $tacticianBus->expects($this->once())
            ->method('handle')
            ->with($command);

        $bus = new TacticianCommandBus($tacticianBus);

        // Act
        $bus->execute($command);

        // No assertion needed as the expectation is set in the mock
    }
}
