<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\DBAL;

use Doctrine\DBAL\DriverManager;
use Doctrine\DBAL\Exception;
use Doctrine\DBAL\Tools\DsnParser;

class DBALConnectionFactory
{
    /**
     * @return array [Connection, Schema]
     *
     * @throws Exception
     */
    public static function create(bool $sqlite = true): array
    {
        if ($sqlite) {
            $params = [
                'user' => 'root',
                'password' => 'root',
                'driver' => 'pdo_sqlite',
                'path' => ':memory:',
            ];
        } else {
            $parser = new DsnParser([
                'mysql' => 'pdo_mysql',
                'mysql2' => 'pdo_mysql',
            ]);
            $params = $parser->parse($_ENV['DATABASE_URL']);
        }
        $connection = DriverManager::getConnection($params);
        $schemaManager = $connection->createSchemaManager();
        $schema = $schemaManager->introspectSchema();

        return [$connection, $schema];
    }
}
