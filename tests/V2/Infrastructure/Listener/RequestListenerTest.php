<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Listener;

use App\V2\Domain\Security\FirewallInterface;
use App\V2\Infrastructure\Listener\RequestListener;
use App\V2\Infrastructure\Security\FirewallException;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\ContainerInterface;
use Psr\Container\NotFoundExceptionInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;

class RequestListenerTest extends TestCase
{
    /**
     * @throws NotFoundExceptionInterface
     * @throws Exception
     * @throws ContainerExceptionInterface
     * @throws FirewallException
     */
    public function testFirewallGrantedWithoutUser(): void
    {
        $firewall = $this->createMock(FirewallInterface::class);
        $container = $this->createMock(ContainerInterface::class);
        $event = $this->createMock(RequestEvent::class);
        $request = $this->createMock(Request::class);

        $firewall->expects($this->once())
            ->method('isGranted')
            ->with($request, null)
            ->willReturn(true);

        $event->expects($this->once())
            ->method('getRequest')
            ->willReturn($request);

        $listener = new RequestListener($firewall, $container);
        $listener->firewall($event);
    }

    /**
     * @throws NotFoundExceptionInterface
     * @throws Exception
     * @throws ContainerExceptionInterface
     * @throws FirewallException
     */
    public function testGetUserThrowsLogicExceptionIfNoTokenStorage(): void
    {
        $firewall = $this->createMock(FirewallInterface::class);
        $container = $this->createMock(ContainerInterface::class);
        $event = $this->createMock(RequestEvent::class);
        $request = $this->createMock(Request::class);

        $firewall->method('isGranted')->willReturn(false);
        $event->method('getRequest')->willReturn($request);
        $container->expects($this->once())
            ->method('has')
            ->with('security.token_storage')
            ->willReturn(false);

        $listener = new RequestListener($firewall, $container);
        $this->expectException(\LogicException::class);
        $this->expectExceptionMessage('Missing SecurityBundle.');
        $listener->firewall($event);
    }

    /**
     * @throws NotFoundExceptionInterface
     * @throws ContainerExceptionInterface
     * @throws Exception
     */
    public function testGetUserReturnsNullIfNoToken(): void
    {
        $firewall = $this->createMock(FirewallInterface::class);
        $container = $this->createMock(ContainerInterface::class);
        $tokenStorage = $this->createMock(TokenStorageInterface::class);

        $container->expects($this->once())
            ->method('has')
            ->with('security.token_storage')
            ->willReturn(true);
        $container->expects($this->once())
            ->method('get')
            ->with('security.token_storage')
            ->willReturn($tokenStorage);
        $tokenStorage->expects($this->once())
            ->method('getToken')
            ->willReturn(null);

        $listener = new RequestListener($firewall, $container);

        // Use reflection to access private method
        $reflectionClass = new \ReflectionClass(RequestListener::class);
        $method = $reflectionClass->getMethod('getUser');

        $this->assertNull($method->invoke($listener));
    }

    public function testGetSubscribedEvents(): void
    {
        $this->assertEquals(
            [
                KernelEvents::REQUEST => ['firewall', 5],
            ],
            RequestListener::getSubscribedEvents()
        );
    }

    /**
     * @throws NotFoundExceptionInterface
     * @throws ContainerExceptionInterface
     * @throws Exception
     */
    public function testGetUserReturnsNullForNonObjectUser(): void
    {
        $firewall = $this->createMock(FirewallInterface::class);
        $container = $this->createMock(ContainerInterface::class);
        $tokenStorage = $this->createMock(TokenStorageInterface::class);
        $token = $this->createMock(TokenInterface::class);

        $container->expects($this->once())
            ->method('has')
            ->with('security.token_storage')
            ->willReturn(true);
        $container->expects($this->once())
            ->method('get')
            ->with('security.token_storage')
            ->willReturn($tokenStorage);
        $tokenStorage->expects($this->once())
            ->method('getToken')
            ->willReturn($token);
        $token->expects($this->once())
            ->method('getUser')
            ->willReturn('anonymous');

        $listener = new RequestListener($firewall, $container);

        // Use reflection to access private method
        $reflectionClass = new \ReflectionClass(RequestListener::class);
        $method = $reflectionClass->getMethod('getUser');

        $this->assertNull($method->invoke($listener));
    }
}
