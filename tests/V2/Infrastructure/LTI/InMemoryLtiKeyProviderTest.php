<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\LTI;

use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Infrastructure\LTI\InMemoryLtiKeyProvider;
use PHPUnit\Framework\TestCase;

class InMemoryLtiKeyProviderTest extends TestCase
{
    private const string PRIVATE_KEY = '************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';

    private const string PUBLIC_KEY = '-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtHdXwwPUAVP/cuncey+K
65hgdvxJzMeqC0RZBuNm+0W7YS2g1s18EFbS4n34N+Dd+UQ508JsxxIxitkoaRIy
5m7oPVGNdmtLkNnhGrKUWNQJq4dWLvRfv7RKwTPGVN/QcUwlEw+Y69gVWDc2RZnn
9UpGY9AxYzSnbcK0ZlhtEfQQ2jWtbK0ivPgAuh2m6oktQoTO9p38ZH6AfQCGiIQG
SYuEl5Thvg67oaJ/pUJlScjCIe2cH7SzTvoxFVCBj+ktgm0OV6nNs1Iqx3q0zQJr
pVOMBBQYch5YB5MLQ/CLZk6b5QagWukkEUkDihBSZNZujLVZtuwQyZh9p79R7MBR
UQIDAQAB
-----END PUBLIC KEY-----
';

    public function testGetKeys(): void
    {
        $provider = new InMemoryLtiKeyProvider();
        $this->assertEquals(self::PUBLIC_KEY, $provider->getPublicKeyFile());
        $this->assertEquals(self::PRIVATE_KEY, $provider->getPrivateKeyFile());
    }

    public function testSignAndVerify(): void
    {
        $provider = new InMemoryLtiKeyProvider();
        $signature = '';
        $result = $provider->sign('DATA_TEST', $signature);
        $this->assertTrue($result);
        $this->assertEquals(
            'gKmCTRW+MFLOYnAvkyIsF7l+AZ81Os8XPU0yT7DDlJFuWgCrTNlwl/wBbQy2rkvTidSyXCIiEw/uUnKQNKF0faIfXiWU9TcMGj8pKimYMhWpaB36vGj2hyN0QzkEux7o+Tac27Lv2OI+vGQhpxZTxTiOLa4n4e5J9fBoiDHmv9HZrnZzzEV6FSBqHvH1BwF9C6FLoXFpb7TpJ26Dr/xCkB0RCiRKiLDyUsVMHbLw51CBe06Fgim3HjKsyEZvEu4ZlkrESk96nAhz+vLqT2vAkqymaVtIQwJq6sBDcc+QYERCrUf7ud5KlijJAy1yQ/h5ZqA+3AYSOhaKS5kN1p4gFA==',
            base64_encode($signature),
        );
        $this->assertTrue($provider->verify('DATA_TEST', $signature));
    }

    public function testGenerateKeys(): void
    {
        $provider = new InMemoryLtiKeyProvider();
        $this->expectExceptionObject(new InfrastructureException('Not implemented'));
        $provider->generateKeys();
    }

    public function testAlgorithm(): void
    {
        $provider = new InMemoryLtiKeyProvider();
        $this->assertEquals('RS256', $provider->getAlgorithm());
    }
}
