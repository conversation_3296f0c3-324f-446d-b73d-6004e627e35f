<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\LTI;

use App\Tests\Mother\Entity\UserMother;
use App\V2\Domain\LTI\Exceptions\LtiUnauthorizedException;
use App\V2\Domain\LTI\LtiKeyProvider;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\User\Exception\UserNotFoundException;
use App\V2\Domain\User\UserRepository;
use App\V2\Infrastructure\LTI\UserAuthenticator;
use OAT\Library\Lti1p3Core\Registration\RegistrationInterface;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class UserAuthenticatorTest extends TestCase
{
    /**
     * @throws Exception
     */
    private function getAuthenticator(
        ?UserRepository $userRepository = null,
        ?LtiKeyProvider $ltiKeyProvider = null,
    ): UserAuthenticator {
        return new UserAuthenticator(
            userRepository: $userRepository ?? $this->createMock(UserRepository::class),
            ltiKeyProvider: $ltiKeyProvider ?? $this->createMock(LtiKeyProvider::class),
        );
    }

    /**
     * @throws InfrastructureException
     * @throws Exception
     * @throws LtiUnauthorizedException
     * @throws CriteriaException
     */
    public function testAuthenticate(): void
    {
        $loginHint = json_encode([
            'id' => 1,
            'email' => '<EMAIL>',
            'iat' => (new \DateTime())->getTimestamp(),
        ]);

        $ltiKeyProvider = $this->createMock(LtiKeyProvider::class);
        $ltiKeyProvider->expects($this->once())
            ->method('verify')
            ->willReturn(true);

        $userRepository = $this->createMock(UserRepository::class);
        $userRepository->expects($this->once())
            ->method('findOneBy')
            ->willReturn(
                UserMother::create(
                    id: 1,
                    firstName: 'John',
                    lastName: 'Doe',
                    email: '<EMAIL>',
                )
            );

        $authenticator = $this->getAuthenticator(
            userRepository: $userRepository,
            ltiKeyProvider: $ltiKeyProvider,
        );

        $result = $authenticator->authenticate(
            registration: $this->createMock(RegistrationInterface::class),
            loginHint: base64_encode($loginHint) . '.signature',
        );

        $this->assertTrue($result->isSuccess());
        $this->assertEquals(
            '<EMAIL>',
            $result->getUserIdentity()->getIdentifier()
        );

        $this->assertEquals(
            'John',
            $result->getUserIdentity()->getGivenName()
        );

        $this->assertEquals(
            'Doe',
            $result->getUserIdentity()->getFamilyName()
        );
    }

    /**
     * @throws InfrastructureException
     * @throws Exception
     * @throws CriteriaException
     */
    public function testAuthenticateWrongSignature(): void
    {
        $ltiKeyProvider = $this->createMock(LtiKeyProvider::class);
        $ltiKeyProvider->expects($this->once())
            ->method('verify')
            ->willReturn(false);

        $authenticator = $this->getAuthenticator(ltiKeyProvider: $ltiKeyProvider);

        $this->expectException(LtiUnauthorizedException::class);
        $authenticator->authenticate(
            registration: $this->createMock(RegistrationInterface::class),
            loginHint: base64_encode('test') . '.' . base64_encode('signature'),
        );
    }

    /**
     * @throws InfrastructureException
     * @throws Exception
     * @throws CriteriaException
     */
    public function testAuthenticateUserNotFound(): void
    {
        $ltiKeyProvider = $this->createMock(LtiKeyProvider::class);
        $ltiKeyProvider->expects($this->once())
            ->method('verify')
            ->willReturn(true);

        $userRepository = $this->createMock(UserRepository::class);
        $userRepository->expects($this->once())
            ->method('findOneBy')
            ->willThrowException(new UserNotFoundException());

        $authenticator = $this->getAuthenticator(
            userRepository: $userRepository,
            ltiKeyProvider: $ltiKeyProvider,
        );
        $this->expectException(LtiUnauthorizedException::class);
        $authenticator->authenticate(
            registration: $this->createMock(RegistrationInterface::class),
            loginHint: base64_encode(json_encode(['id' => 1])) . '.' . base64_encode('signature'),
        );
    }
}
