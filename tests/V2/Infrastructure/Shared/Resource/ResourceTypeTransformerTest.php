<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Shared\Resource;

use App\V2\Domain\Shared\Resource\ResourceType;
use App\V2\Infrastructure\Shared\Resource\ResourceTypeTransformer;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class ResourceTypeTransformerTest extends TestCase
{
    #[DataProvider('validResourceTypeProvider')]
    public function testFromStringWithValidResourceType(string $resourceType, ResourceType $expected): void
    {
        $result = ResourceTypeTransformer::fromString($resourceType);

        $this->assertSame($expected, $result);
    }

    #[DataProvider('invalidResourceTypeProvider')]
    public function testFromStringWithInvalidResourceType(string $invalidResourceType): void
    {
        $this->expectException(\UnhandledMatchError::class);

        ResourceTypeTransformer::fromString($invalidResourceType);
    }

    #[DataProvider('validResourceTypeProvider')]
    public function testToString(string $expected, ResourceType $resourceType): void
    {
        $result = ResourceTypeTransformer::toString($resourceType);

        $this->assertSame($expected, $result);
    }

    public static function validResourceTypeProvider(): array
    {
        return [
            'course' => [
                'resourceType' => 'course',
                'expected' => ResourceType::Course,
            ],
        ];
    }

    public static function invalidResourceTypeProvider(): array
    {
        return [
            'user' => [
                'invalidResourceType' => 'user',
            ],
            'lesson' => [
                'invalidResourceType' => 'lesson',
            ],
            'module' => [
                'invalidResourceType' => 'module',
            ],
            'empty string' => [
                'invalidResourceType' => '',
            ],
            'uppercase course' => [
                'invalidResourceType' => 'COURSE',
            ],
            'mixed case course' => [
                'invalidResourceType' => 'Course',
            ],
            'course with spaces' => [
                'invalidResourceType' => ' course ',
            ],
            'invalid format' => [
                'invalidResourceType' => 'invalid_type',
            ],
            'numeric string' => [
                'invalidResourceType' => '123',
            ],
            'special characters' => [
                'invalidResourceType' => 'course@#$',
            ],
        ];
    }
}
