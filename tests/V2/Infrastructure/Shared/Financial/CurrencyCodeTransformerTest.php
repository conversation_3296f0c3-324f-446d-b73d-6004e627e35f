<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Shared\Financial;

use App\V2\Domain\Shared\Financial\CurrencyCode;
use App\V2\Domain\Shared\Financial\Exception\InvalidCurrencyCodeException;
use App\V2\Infrastructure\Shared\Financial\CurrencyCodeTransformer;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class CurrencyCodeTransformerTest extends TestCase
{
    #[DataProvider('validCurrencyCodeProvider')]
    public function testFromStringWithValidCurrencyCode(string $currencyCode, CurrencyCode $expected): void
    {
        $result = CurrencyCodeTransformer::fromString($currencyCode);

        $this->assertSame($expected, $result);
    }

    #[DataProvider('invalidCurrencyCodeProvider')]
    public function testFromStringWithInvalidCurrencyCode(string $invalidCurrencyCode): void
    {
        $this->expectException(InvalidCurrencyCodeException::class);

        CurrencyCodeTransformer::fromString($invalidCurrencyCode);
    }

    #[DataProvider('validCurrencyCodeProvider')]
    public function testToString(string $expected, CurrencyCode $currencyCode): void
    {
        $result = CurrencyCodeTransformer::toString($currencyCode);

        $this->assertSame($expected, $result);
    }

    public static function validCurrencyCodeProvider(): array
    {
        return [
            'EUR' => [
                'currencyCode' => 'EUR',
                'expected' => CurrencyCode::EUR,
            ],
            'USD' => [
                'currencyCode' => 'USD',
                'expected' => CurrencyCode::USD,
            ],
        ];
    }

    public static function invalidCurrencyCodeProvider(): array
    {
        return [
            'GBP' => [
                'invalidCurrencyCode' => 'GBP',
            ],
            'JPY' => [
                'invalidCurrencyCode' => 'JPY',
            ],
            'CAD' => [
                'invalidCurrencyCode' => 'CAD',
            ],
            'empty string' => [
                'invalidCurrencyCode' => '',
            ],
            'lowercase eur' => [
                'invalidCurrencyCode' => 'eur',
            ],
            'lowercase usd' => [
                'invalidCurrencyCode' => 'usd',
            ],
            'mixed case' => [
                'invalidCurrencyCode' => 'Eur',
            ],
            'invalid format' => [
                'invalidCurrencyCode' => 'INVALID',
            ],
        ];
    }
}
