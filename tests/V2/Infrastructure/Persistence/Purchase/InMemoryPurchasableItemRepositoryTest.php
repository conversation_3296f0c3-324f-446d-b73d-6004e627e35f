<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\Purchase;

use App\Tests\V2\Domain\Purchase\PurchasableItemRepositoryTestCase;
use App\V2\Domain\Purchase\PurchasableItemRepository;
use App\V2\Infrastructure\Persistence\Purchase\InMemoryPurchasableItemRepository;

class InMemoryPurchasableItemRepositoryTest extends PurchasableItemRepositoryTestCase
{
    protected function getRepository(): PurchasableItemRepository
    {
        return new InMemoryPurchasableItemRepository();
    }
}
