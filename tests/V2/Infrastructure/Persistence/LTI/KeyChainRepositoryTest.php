<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\LTI;

use App\V2\Infrastructure\LTI\CoreKeyChainGenerator;
use App\V2\Infrastructure\Persistence\LTI\KeyChainRepository;
use OAT\Library\Lti1p3Core\Security\Key\KeyChainFactory;
use OAT\Library\Lti1p3Core\Security\Key\KeyChainInterface;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class KeyChainRepositoryTest extends TestCase
{
    private KeyChainFactory $keyChainFactory;

    protected function setUp(): void
    {
        $this->keyChainFactory = new KeyChainFactory();
        parent::setUp();
    }

    /**
     * @throws Exception
     */
    private function getKeyChainRepository(): KeyChainRepository
    {
        return new KeyChainRepository(
            coreKeyChainGenerator: $this->getCoreKeyChainGenerator(),
        );
    }

    private function getKeyChain(): KeyChainInterface
    {
        return $this->keyChainFactory->create(
            identifier: CoreKeyChainGenerator::KEY_IDENTIFIER,
            keySetName: CoreKeyChainGenerator::KEY_SET_NAME,
            publicKey: 'public_key',
            privateKey: 'private_key',
            privateKeyPassPhrase: 'private_key_passphrase',
            algorithm: 'RS256',
        );
    }

    /**
     * @throws Exception
     */
    private function getCoreKeyChainGenerator(): CoreKeyChainGenerator
    {
        $coreKeyChainGenerator = $this->createMock(CoreKeyChainGenerator::class);
        $coreKeyChainGenerator
            ->method('generateCoreKeyChain')
            ->willReturn(
                $this->getKeyChain()
            )
        ;

        return $coreKeyChainGenerator;
    }

    /**
     * @throws Exception
     */
    public function testFind(): void
    {
        $repository = $this->getKeyChainRepository();

        $this->assertEquals(
            $this->getKeyChain(),
            $repository->find(CoreKeyChainGenerator::KEY_IDENTIFIER)
        );
    }

    /**
     * @throws Exception
     */
    public function testFindByKeySetName(): void
    {
        $repository = $this->getKeyChainRepository();
        $this->assertEquals(
            [$this->getKeyChain()],
            $repository->findByKeySetName(CoreKeyChainGenerator::KEY_SET_NAME)
        );
    }
}
