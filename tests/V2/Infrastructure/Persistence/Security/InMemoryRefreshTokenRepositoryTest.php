<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\Security;

use App\Tests\V2\Domain\Security\RefreshTokenRepositoryTestCase;
use App\V2\Domain\Security\RefreshTokenRepository;
use App\V2\Infrastructure\Persistence\Security\InMemoryRefreshTokenRepository;

class InMemoryRefreshTokenRepositoryTest extends RefreshTokenRepositoryTestCase
{
    protected function getRepository(): RefreshTokenRepository
    {
        return new InMemoryRefreshTokenRepository();
    }
}
