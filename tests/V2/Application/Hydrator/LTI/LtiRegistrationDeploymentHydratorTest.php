<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\Hydrator\LTI;

use App\Tests\V2\Mother\LTI\LtiDeploymentMother;
use App\Tests\V2\Mother\LTI\LtiRegistrationMother;
use App\V2\Application\Hydrator\HydratorPriority;
use App\V2\Application\Hydrator\LTI\LtiRegistrationDeploymentHydrator;
use App\V2\Domain\LTI\LtiDeploymentCollection;
use App\V2\Domain\LTI\LtiDeploymentRepository;
use App\V2\Domain\LTI\LtiRegistrationCollection;
use App\V2\Domain\LTI\LtiRegistrationHydrationCriteria;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Hydrator\HydratorException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class LtiRegistrationDeploymentHydratorTest extends TestCase
{
    /**
     * @throws Exception
     */
    private function getHydrator(
        ?LtiDeploymentRepository $ltiDeploymentRepository = null,
    ): LtiRegistrationDeploymentHydrator {
        return new LtiRegistrationDeploymentHydrator(
            ltiDeploymentRepository: $ltiDeploymentRepository ?? $this->createMock(LtiDeploymentRepository::class),
        );
    }

    /**
     * @throws Exception
     */
    public function testPriority(): void
    {
        $hydrator = $this->getHydrator();
        $this->assertEquals(HydratorPriority::First, $hydrator->getPriority());
    }

    /**
     * @throws Exception
     */
    public function testSupports(): void
    {
        $hydrator = $this->getHydrator();
        $this->assertTrue(
            $hydrator->supports(
                LtiRegistrationHydrationCriteria::createEmpty()
                    ->withDeployments()
            )
        );

        $this->assertFalse(
            $hydrator->supports(
                LtiRegistrationHydrationCriteria::createEmpty()
            )
        );
    }

    /**
     * @throws InfrastructureException
     * @throws HydratorException
     * @throws Exception
     * @throws CollectionException
     */
    public function testEmptyCollection(): void
    {
        $collection = new LtiRegistrationCollection([]);
        $hydrator = $this->getHydrator();
        $hydrator->hydrate(
            $collection,
            LtiRegistrationHydrationCriteria::createEmpty()
                ->withDeployments()
        );
        $this->assertEmpty($collection->all());
    }

    /**
     * @throws InfrastructureException
     * @throws HydratorException
     * @throws Exception
     * @throws CollectionException
     * @throws InvalidUuidException
     */
    public function testHydrateWithDeployments(): void
    {
        $registration1 = LtiRegistrationMother::create();
        $registration2 = LtiRegistrationMother::create();
        $registration3 = LtiRegistrationMother::create();

        $deployment1 = LtiDeploymentMother::create(registrationId: $registration1->getId());
        $deployment2 = LtiDeploymentMother::create(registrationId: $registration1->getId(), deploymentId: 'random-id');
        $deployment3 = LtiDeploymentMother::create(registrationId: $registration2->getId());
        $deployment4 = LtiDeploymentMother::create(registrationId: $registration2->getId(), deploymentId: 'random-id');
        $deployment5 = LtiDeploymentMother::create(registrationId: $registration3->getId());
        $deployment6 = LtiDeploymentMother::create(registrationId: $registration3->getId(), deploymentId: 'random-id');

        $ltiDeploymentRepository = $this->createMock(LtiDeploymentRepository::class);
        $ltiDeploymentRepository->expects($this->once())->method('findBy')
            ->willReturn(new LtiDeploymentCollection([
                $deployment1,
                $deployment2,
                $deployment3,
                $deployment4,
                $deployment5,
                $deployment6,
            ]));

        $collection = new LtiRegistrationCollection([$registration1, $registration2, $registration3]);

        $hydrator = $this->getHydrator(
            ltiDeploymentRepository: $ltiDeploymentRepository,
        );

        $hydrator->hydrate(
            $collection,
            LtiRegistrationHydrationCriteria::createEmpty()
                ->withDeployments()
        );

        $this->assertCount(
            0,
            array_diff(
                [$deployment1, $deployment2],
                $registration1->getDeployments()?->all() ?? [],
            )
        );

        $this->assertCount(
            0,
            array_diff(
                [$deployment3, $deployment4],
                $registration2->getDeployments()?->all() ?? [],
            )
        );

        $this->assertCount(
            0,
            array_diff(
                [$deployment5, $deployment6],
                $registration3->getDeployments()?->all() ?? [],
            )
        );
    }
}
