<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\Hydrator\LTI;

use App\V2\Application\Hydrator\LTI\LtiRegistrationHydratorCollection;
use App\V2\Domain\LTI\LtiRegistrationCollection;
use App\V2\Domain\LTI\LtiRegistrationHydrationCriteria;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Hydrator\HydrationCriteria;
use App\V2\Domain\Shared\Hydrator\HydratorException;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class LtiRegistrationHydratorCollectionTest extends TestCase
{
    /**
     * @throws InfrastructureException
     * @throws Exception
     * @throws CollectionException
     */
    #[DataProvider('provideHydrateException')]
    public function testHydrateException(bool $collectionError, bool $criteriaError): void
    {
        $collection = $collectionError ? $this->createMock(Collection::class) : new LtiRegistrationCollection([]);
        $criteria = $criteriaError ?
            $this->createMock(HydrationCriteria::class)
            : LtiRegistrationHydrationCriteria::createEmpty();

        $hydrator = new LtiRegistrationHydratorCollection([]);
        $this->expectException(HydratorException::class);
        $hydrator->hydrate($collection, $criteria);
    }

    public static function provideHydrateException(): \Generator
    {
        yield 'invalid collection' => [
            'collectionError' => true,
            'criteriaError' => false,
        ];

        yield 'invalid criteria' => [
            'collectionError' => false,
            'criteriaError' => true,
        ];

        yield 'invalid criteria and collection' => [
            'collectionError' => true,
            'criteriaError' => true,
        ];
    }
}
