<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\Hydrator;

use App\Tests\V2\Mother\Course\Creator\CourseCreatorMother;
use App\V2\Application\Hydrator\Hydrator;
use App\V2\Application\Hydrator\HydratorCollection;
use App\V2\Application\Hydrator\HydratorPriority;
use App\V2\Domain\Course\Creator\CourseCreatorCollection;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Hydrator\HydrationCriteria;
use App\V2\Domain\Shared\Hydrator\HydratorException;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class HydratorCollectionTest extends TestCase
{
    /**
     * @throws Exception
     * @throws HydratorException
     * @throws InfrastructureException
     */
    #[DataProvider('hydrateInOrderDataProvider')]
    public function testHydrateInOrder(array $hydratorsData): void
    {
        $hydratedCourseCreators = [];
        $hydrators = [];
        $hydratorsApplied = [];
        foreach ($hydratorsData as $hydratorData) {
            $hydrator = $this->createMock(Hydrator::class);
            $hydrator->method('supports')->willReturn($hydratorData['supports']);
            $hydrator->method('getPriority')->willReturn($hydratorData['priority']);
            $hydrator->method('hydrate')->willReturnCallback(
                function (
                    Collection $collection,
                    HydrationCriteria $criteria
                ) use (
                    $hydratorData,
                    &$hydratedCourseCreators
                ) {
                    foreach ($collection->all() as $courseCreator) {
                        $hydratedCourseCreators["$courseCreator"][] = $hydratorData['name'];
                    }
                }
            );

            $hydrators[] = $hydrator;

            $priorityString = match ($hydratorData['priority']) {
                HydratorPriority::First => 'firstPriorityHydrators',
                HydratorPriority::Second => 'secondPriorityHydrators',
                default => throw new \InvalidArgumentException('Invalid priority'),
            };

            if ($hydratorData['supports']) {
                $hydratorsApplied[$priorityString][] = $hydratorData['name'];
            }
        }

        shuffle($hydrators);

        $collection = new CourseCreatorCollection([
            CourseCreatorMother::create(userId: 1, courseId: 1),
            CourseCreatorMother::create(userId: 2, courseId: 2),
            CourseCreatorMother::create(userId: 3, courseId: 3),
        ]);

        $criteria = $this->createMock(HydrationCriteria::class);

        $hydratorCollection = new HydratorCollection($hydrators);

        $hydratorCollection->hydrate($collection, $criteria);

        foreach ($hydratedCourseCreators as $userIdCourseId => $hydratedData) {
            $firstPriorityHydratedData = array_splice(
                $hydratedData,
                0,
                \count($hydratorsApplied['firstPriorityHydrators'])
            );
            $this->assertEqualsCanonicalizing($hydratorsApplied['firstPriorityHydrators'], $firstPriorityHydratedData);
            $secondPriorityHydratedData = array_splice(
                $hydratedData,
                0,
                \count($hydratorsApplied['secondPriorityHydrators'])
            );
            $this->assertEqualsCanonicalizing(
                $hydratorsApplied['secondPriorityHydrators'],
                $secondPriorityHydratedData
            );
        }
    }

    public static function hydrateInOrderDataProvider(): \Generator
    {
        yield 'First priority hydrators' => [
            [
                [
                    'name' => 'First priority hydrator 1',
                    'priority' => HydratorPriority::First,
                    'supports' => true,
                ],
                [
                    'name' => 'First priority hydrator 2',
                    'priority' => HydratorPriority::First,
                    'supports' => true,
                ],
                [
                    'name' => 'First priority hydrator 3',
                    'priority' => HydratorPriority::First,
                    'supports' => true,
                ],
                [
                    'name' => 'Second priority hydrator 1',
                    'priority' => HydratorPriority::Second,
                    'supports' => true,
                ],
                [
                    'name' => 'Second priority hydrator 2',
                    'priority' => HydratorPriority::Second,
                    'supports' => true,
                ],
            ],
        ];

        yield 'Second priority hydrators' => [
            [
                [
                    'name' => 'First priority hydrator 1',
                    'priority' => HydratorPriority::First,
                    'supports' => true,
                ],
                [
                    'name' => 'First priority hydrator 2',
                    'priority' => HydratorPriority::First,
                    'supports' => true,
                ],
                [
                    'name' => 'Second priority hydrator 1',
                    'priority' => HydratorPriority::Second,
                    'supports' => true,
                ],
                [
                    'name' => 'Second priority hydrator 2',
                    'priority' => HydratorPriority::Second,
                    'supports' => true,
                ],
                [
                    'name' => 'Second priority hydrator 3',
                    'priority' => HydratorPriority::Second,
                    'supports' => true,
                ],
            ],
        ];

        yield 'Mixed priority hydrators' => [
            [
                [
                    'name' => 'First priority hydrator 1',
                    'priority' => HydratorPriority::First,
                    'supports' => true,
                ],
                [
                    'name' => 'Second priority hydrator 1',
                    'priority' => HydratorPriority::Second,
                    'supports' => true,
                ],
                [
                    'name' => 'First priority hydrator 2',
                    'priority' => HydratorPriority::First,
                    'supports' => true,
                ],
                [
                    'name' => 'Second priority hydrator 2',
                    'priority' => HydratorPriority::Second,
                    'supports' => false,
                ],
                [
                    'name' => 'Second priority hydrator 3',
                    'priority' => HydratorPriority::Second,
                    'supports' => true,
                ],
            ],
        ];
    }
}
