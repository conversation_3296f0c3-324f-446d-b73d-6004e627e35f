<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\Hydrator;

use App\V2\Application\Hydrator\HydratorPriority;
use PHPUnit\Framework\TestCase;

class HydratorPriorityTest extends TestCase
{
    public function testGetInOrder(): void
    {
        $this->assertSame(
            [HydratorPriority::First, HydratorPriority::Second, HydratorPriority::Third],
            HydratorPriority::getInOrder()
        );
    }
}
