<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\Hydrator\Announcement\Manager;

use App\Tests\Mother\Entity\UserMother;
use App\Tests\V2\Mother\Announcement\Manager\AnnouncementManagerMother;
use App\V2\Application\Hydrator\Announcement\Manager\AnnouncementManagerHydrator;
use App\V2\Application\Hydrator\HydratorPriority;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerCollection;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerHydrationCriteria;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Hydrator\HydratorException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\User\UserCollection;
use App\V2\Domain\User\UserRepository;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class AnnouncementManagerHydratorTest extends TestCase
{
    /**
     * @throws Exception
     */
    private function getHydrator(
        ?UserRepository $userRepository = null,
    ): AnnouncementManagerHydrator {
        return new AnnouncementManagerHydrator(
            userRepository: $userRepository ?? $this->createMock(UserRepository::class),
        );
    }

    /**
     * @throws Exception
     */
    public function testPriority(): void
    {
        $hydrator = $this->getHydrator();
        $this->assertEquals(HydratorPriority::First, $hydrator->getPriority());
    }

    /**
     * @throws Exception
     */
    public function testSupports(): void
    {
        $hydrator = $this->getHydrator();
        $this->assertTrue(
            $hydrator->supports(
                AnnouncementManagerHydrationCriteria::createEmpty()->withUser()
            )
        );

        $this->assertFalse(
            $hydrator->supports(
                AnnouncementManagerHydrationCriteria::createEmpty()
            )
        );
    }

    /**
     * @throws InfrastructureException
     * @throws CollectionException
     * @throws Exception
     */
    public function testEmptyCollection(): void
    {
        $collection = new AnnouncementManagerCollection([]);
        $hydrator = $this->getHydrator();
        $hydrator->hydrate(
            $collection,
            AnnouncementManagerHydrationCriteria::createEmpty()->withUser()
        );

        $this->assertEmpty($collection->all());
    }

    /**
     * @throws InfrastructureException
     * @throws HydratorException
     * @throws Exception
     * @throws CollectionException
     */
    public function testHydrateWithUser(): void
    {
        $announcementManager1 = AnnouncementManagerMother::create(userId: new Id(1), announcementId: new Id(1));
        $announcementManager2 = AnnouncementManagerMother::create(userId: new Id(2), announcementId: new Id(2));
        $announcementManager3 = AnnouncementManagerMother::create(userId: new Id(1), announcementId: new Id(3));
        $announcementManager4 = AnnouncementManagerMother::create(userId: new Id(2), announcementId: new Id(4));

        $user1 = UserMother::create(id: 1);
        $user2 = UserMother::create(id: 2);

        $userRepository = $this->createMock(UserRepository::class);
        $userRepository->expects($this->once())
            ->method('findBy')
            ->willReturn(new UserCollection([$user1, $user2]));

        $hydrator = $this->getHydrator(
            userRepository: $userRepository
        );

        $hydrator->hydrate(
            collection: new AnnouncementManagerCollection([
                $announcementManager1, $announcementManager2, $announcementManager3,  $announcementManager4,
            ]),
            criteria: AnnouncementManagerHydrationCriteria::createEmpty()->withUser()
        );

        $this->assertNotNull($announcementManager1->getManager());
        $this->assertNotNull($announcementManager2->getManager());
        $this->assertNotNull($announcementManager3->getManager());
        $this->assertNotNull($announcementManager4->getManager());

        $this->assertEquals($user1->getId(), $announcementManager1->getManager()->getId()->value());
        $this->assertEquals($user1->getEmail(), $announcementManager1->getManager()->getEmail()->value());

        $this->assertEquals($user2->getId(), $announcementManager2->getManager()->getId()->value());
        $this->assertEquals($user2->getEmail(), $announcementManager2->getManager()->getEmail()->value());

        $this->assertEquals($user1->getId(), $announcementManager3->getManager()->getId()->value());
        $this->assertEquals($user1->getEmail(), $announcementManager3->getManager()->getEmail()->value());

        $this->assertEquals($user2->getId(), $announcementManager4->getManager()->getId()->value());
        $this->assertEquals($user2->getEmail(), $announcementManager4->getManager()->getEmail()->value());
    }
}
