<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\QueryHandler\Admin;

use App\Entity\LtiTool as LegacyLtiTool;
use App\Repository\ChapterRepository as LegacyChapterRepository;
use App\Repository\CourseRepository as LegacyCourseRepository;
use App\Repository\LtiChapterRepository as LegacyLtiChapterRepository;
use App\Tests\Mother\Entity\ChapterMother;
use App\Tests\Mother\Entity\CourseMother;
use App\Tests\Mother\Entity\LtiChapterMother;
use App\Tests\Mother\Entity\UserMother;
use App\Tests\V2\Mother\LTI\LtiPlatformMother;
use App\Tests\V2\Mother\LTI\LtiToolMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Application\Query\Admin\LaunchLtiChapterQuery;
use App\V2\Application\QueryHandler\Admin\LaunchLtiChapterQueryHandler;
use App\V2\Domain\LTI\Exceptions\LaunchLtiChapterQueryHandlerException;
use App\V2\Domain\LTI\Exceptions\LtiChapterNotFound;
use App\V2\Domain\LTI\Launch\LaunchResultType;
use App\V2\Domain\LTI\LtiKeyProvider;
use App\V2\Domain\LTI\LtiToolRepository;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Url\Url;
use App\V2\Infrastructure\LTI\RegistrationRepository;
use App\V2\Infrastructure\LTI\Transformer\LtiPlatformTransformer;
use App\V2\Infrastructure\LTI\Transformer\LtiToolTransformer;
use OAT\Library\Lti1p3Core\Message\Launch\Builder\LtiResourceLinkLaunchRequestBuilder;
use OAT\Library\Lti1p3Core\Registration\Registration;
use OAT\Library\Lti1p3Core\Registration\RegistrationRepositoryInterface;
use OAT\Library\Lti1p3Core\Security\Key\Key;
use OAT\Library\Lti1p3Core\Security\Key\KeyChain;
use OAT\Library\Lti1p3Core\Security\Key\KeyInterface;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class LaunchLtiChapterQueryHandlerTest extends TestCase
{
    private const string PRIVATE_KEY = '************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';

    private const string PUBLIC_KEY = '-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtHdXwwPUAVP/cuncey+K
65hgdvxJzMeqC0RZBuNm+0W7YS2g1s18EFbS4n34N+Dd+UQ508JsxxIxitkoaRIy
5m7oPVGNdmtLkNnhGrKUWNQJq4dWLvRfv7RKwTPGVN/QcUwlEw+Y69gVWDc2RZnn
9UpGY9AxYzSnbcK0ZlhtEfQQ2jWtbK0ivPgAuh2m6oktQoTO9p38ZH6AfQCGiIQG
SYuEl5Thvg67oaJ/pUJlScjCIe2cH7SzTvoxFVCBj+ktgm0OV6nNs1Iqx3q0zQJr
pVOMBBQYch5YB5MLQ/CLZk6b5QagWukkEUkDihBSZNZujLVZtuwQyZh9p79R7MBR
UQIDAQAB
-----END PUBLIC KEY-----
';

    /**
     * @throws Exception
     */
    private function getHandler(
        ?LegacyCourseRepository $legacyCourseRepository = null,
        ?LegacyChapterRepository $legacyChapterRepository = null,
        ?LegacyLtiChapterRepository $legacyLtiChapterRepository = null,
        ?LtiToolRepository $ltiToolRepository = null,
        ?RegistrationRepositoryInterface $registrationRepository = null,
        ?LtiKeyProvider $ltiKeyProvider = null,
    ): LaunchLtiChapterQueryHandler {
        return new LaunchLtiChapterQueryHandler(
            legacyCourseRepository: $legacyCourseRepository ?? $this->createMock(LegacyCourseRepository::class),
            legacyChapterRepository: $legacyChapterRepository ?? $this->createMock(LegacyChapterRepository::class),
            legacyLtiChapterRepository: $legacyLtiChapterRepository
                ?? $this->createMock(LegacyLtiChapterRepository::class),
            ltiToolRepository: $ltiToolRepository ?? $this->createMock(LtiToolRepository::class),
            registrationRepository: $registrationRepository ?? $this->createMock(RegistrationRepository::class),
            ltiResourceLinkLaunchRequestBuilder: new LtiResourceLinkLaunchRequestBuilder(),
            ltiKeyProvider: $ltiKeyProvider ?? $this->createMock(LtiKeyProvider::class),
        );
    }

    public static function provideTestHandle(): \Generator
    {
        $course = CourseMother::create(id: 1);
        $chapter = ChapterMother::create(id: 1, course: $course);
        $ltiChapter = LtiChapterMother::create(id: 1, chapter: $chapter, ltiToolIdentifierId: UuidMother::create()->value());
        $keychain = new KeyChain(
            identifier: 'id',
            keySetName: 'EasyLearning',
            publicKey: new Key(self::PUBLIC_KEY, null, KeyInterface::ALG_RS256),
            privateKey: new Key(self::PRIVATE_KEY, null, KeyInterface::ALG_RS256),
        );

        $registration = new Registration(
            identifier: UuidMother::create()->value(),
            clientId: 'client-id',
            platform: LtiPlatformTransformer::fromLtiPlatformToCorePlatform(
                LtiPlatformMother::create()
            ),
            tool: LtiToolTransformer::fromLtiToolToCoreTool(
                LtiToolMother::create(
                    oidcInitiationUrl: new Url('https://example-tool.com/lti1p3/oidc/initiation'),
                    launchUrl: new Url('https://example-tool.com/lti1p3/launch'),
                    deepLinkingUrl: new Url('https://example-tool.com/lti1p3/deep-linking'),
                    jwksUrl: new Url('https://example-tool.com/lti1p3/.well-known/jwks/keySetName.json'),
                )
            ),
            deploymentIds: ['deployment-1'],
            platformKeyChain: $keychain,
            toolKeyChain: $keychain,
            platformJwksUrl: 'https//example-platform.com/jwks',
            toolJwksUrl: 'https//example-tool.com/jwks',
        );

        yield 'with lti tool identifier redirect form' => [
            'legacyCourseRepositoryCallback' => fn () => $course,
            'legacyChapterRepositoryCallback' => fn () => $chapter,
            'legacyLtiChapterRepositoryCallback' => fn () => $ltiChapter,
            'ltiToolRepositoryCallback' => fn () => LtiToolMother::create(),
            'registrationRepositoryCallback' => fn () => $registration,
            'ltiKeyProviderSignCallback' => function (string $loginHint, string &$signature) {
                $signature = 'SIGNATURE';

                return true;
            },
            'resultType' => LaunchResultType::RedirectForm,
        ];

        yield 'with lti tool identifier url' => [
            'legacyCourseRepositoryCallback' => fn () => $course,
            'legacyChapterRepositoryCallback' => fn () => $chapter,
            'legacyLtiChapterRepositoryCallback' => fn () => $ltiChapter,
            'ltiToolRepositoryCallback' => fn () => LtiToolMother::create(),
            'registrationRepositoryCallback' => fn () => $registration,
            'ltiKeyProviderSignCallback' => function (string $loginHint, string &$signature) {
                $signature = 'SIGNATURE';

                return true;
            },
            'resultType' => LaunchResultType::URL,
        ];

        yield 'with lti tool identifier html link' => [
            'legacyCourseRepositoryCallback' => fn () => $course,
            'legacyChapterRepositoryCallback' => fn () => $chapter,
            'legacyLtiChapterRepositoryCallback' => fn () => $ltiChapter,
            'ltiToolRepositoryCallback' => fn () => LtiToolMother::create(),
            'registrationRepositoryCallback' => fn () => $registration,
            'ltiKeyProviderSignCallback' => function (string $loginHint, string &$signature) {
                $signature = 'SIGNATURE';

                return true;
            },
            'resultType' => LaunchResultType::HtmlLink,
        ];

        $ltiChapter = LtiChapterMother::create(
            id: 1,
            chapter: $chapter,
            legacyLtiTool: (new LegacyLtiTool())->setClientId('client-id'),
        );

        yield 'with legacy lti tool redirect form' => [
            'legacyCourseRepositoryCallback' => fn () => $course,
            'legacyChapterRepositoryCallback' => fn () => $chapter,
            'legacyLtiChapterRepositoryCallback' => fn () => $ltiChapter,
            'ltiToolRepositoryCallback' => null,
            'registrationRepositoryCallback' => fn () => $registration,
            'ltiKeyProviderSignCallback' => function (string $loginHint, string &$signature) {
                $signature = 'SIGNATURE';

                return true;
            },
            'resultType' => LaunchResultType::RedirectForm,
        ];

        yield 'with legacy lti tool url' => [
            'legacyCourseRepositoryCallback' => fn () => $course,
            'legacyChapterRepositoryCallback' => fn () => $chapter,
            'legacyLtiChapterRepositoryCallback' => fn () => $ltiChapter,
            'ltiToolRepositoryCallback' => null,
            'registrationRepositoryCallback' => fn () => $registration,
            'ltiKeyProviderSignCallback' => function (string $loginHint, string &$signature) {
                $signature = 'SIGNATURE';

                return true;
            },
            'resultType' => LaunchResultType::URL,
        ];

        yield 'with legacy lti tool html link' => [
            'legacyCourseRepositoryCallback' => fn () => $course,
            'legacyChapterRepositoryCallback' => fn () => $chapter,
            'legacyLtiChapterRepositoryCallback' => fn () => $ltiChapter,
            'ltiToolRepositoryCallback' => null,
            'registrationRepositoryCallback' => fn () => $registration,
            'ltiKeyProviderSignCallback' => function (string $loginHint, string &$signature) {
                $signature = 'SIGNATURE';

                return true;
            },
            'resultType' => LaunchResultType::HtmlLink,
        ];
    }

    #[DataProvider('provideTestHandle')]
    public function testHandle(
        callable $legacyCourseRepositoryCallback,
        callable $legacyChapterRepositoryCallback,
        callable $legacyLtiChapterRepositoryCallback,
        ?callable $ltiToolRepositoryCallback,
        callable $registrationRepositoryCallback,
        callable $ltiKeyProviderSignCallback,
        LaunchResultType $resultType = LaunchResultType::RedirectForm,
    ): void {
        $legacyCourseRepository = $this->createMock(LegacyCourseRepository::class);
        $legacyCourseRepository->expects($this->once())
            ->method('find')
            ->willReturnCallback($legacyCourseRepositoryCallback);

        $legacyChapterRepository = $this->createMock(LegacyChapterRepository::class);
        $legacyChapterRepository->expects($this->once())
            ->method('find')
            ->willReturnCallback($legacyChapterRepositoryCallback);

        $legacyLtiChapterRepository = $this->createMock(LegacyLtiChapterRepository::class);
        $legacyLtiChapterRepository->expects($this->once())
            ->method('findOneBy')
            ->willReturnCallback($legacyLtiChapterRepositoryCallback);

        $ltiToolRepository = $this->createMock(LtiToolRepository::class);
        if (null !== $ltiToolRepositoryCallback) {
            $ltiToolRepository->expects($this->once())
                ->method('findOneBy')
                ->willReturnCallback($ltiToolRepositoryCallback);
        }

        $registrationRepository = $this->createMock(RegistrationRepositoryInterface::class);
        $registrationRepository
            ->method('find')
            ->willReturnCallback($registrationRepositoryCallback);
        $registrationRepository
            ->method('findByClientId')
            ->willReturnCallback($registrationRepositoryCallback);

        $ltiKeyProvider = $this->createMock(LtiKeyProvider::class);
        $ltiKeyProvider->expects($this->once())
            ->method('sign')
            ->willReturnCallback($ltiKeyProviderSignCallback);

        $handler = $this->getHandler(
            legacyCourseRepository: $legacyCourseRepository,
            legacyChapterRepository: $legacyChapterRepository,
            legacyLtiChapterRepository: $legacyLtiChapterRepository,
            ltiToolRepository: $ltiToolRepository,
            registrationRepository: $registrationRepository,
            ltiKeyProvider: $ltiKeyProvider,
        );

        $query = new LaunchLtiChapterQuery(
            courseId: new Id(1),
            chapterId: new Id(1),
            user: UserMother::create(
                id: 1,
            ),
            schemeAndHttpHost: 'https://example.com',
            resultType: $resultType,
        );
        $result = $handler->handle($query);

        switch ($resultType) {
            case LaunchResultType::RedirectForm:
                $this->assertStringContainsString('form id="launch_', $result);
                $this->assertStringContainsString('action="https://example-tool.com/lti1p3/oidc/initiation"', $result);
                $this->assertStringContainsString('method="POST"', $result);
                $this->assertStringContainsString(
                    '<input type="hidden" name="target_link_uri" value="https://example-tool.com/lti1p3/launch"/>',
                    $result
                );
                break;
            case LaunchResultType::URL:
                $this->assertStringStartsWith('https://example-tool.com/lti1p3/oidc/initiation', $result);
                $this->assertStringContainsString('login_hint=', $result);
                $this->assertStringContainsString('lti_deployment_id=deployment-1&client_id=client-id', $result);
                break;
            case LaunchResultType::HtmlLink:
                $this->assertStringStartsWith(
                    '<a href="https://example-tool.com/lti1p3/oidc/initiation?iss=EasyLearning&login_hint=',
                    $result
                );
                $this->assertStringContainsString('&lti_deployment_id=deployment-1&client_id=client-id', $result);
                break;
        }
    }

    #[DataProvider('provideTestExceptions')]
    public function testExceptions(
        callable $legacyCourseRepositoryCallback,
        callable $legacyChapterRepositoryCallback,
        callable $legacyLtiChapterRepositoryCallback,
        ?callable $ltiToolRepositoryCallback,
        callable $registrationRepositoryCallback,
        callable $ltiKeyProviderSignCallback,
        $expectedException,
    ): void {
        $legacyCourseRepository = $this->createMock(LegacyCourseRepository::class);
        $legacyCourseRepository
            ->method('find')
            ->willReturnCallback($legacyCourseRepositoryCallback);

        $legacyChapterRepository = $this->createMock(LegacyChapterRepository::class);
        $legacyChapterRepository
            ->method('find')
            ->willReturnCallback($legacyChapterRepositoryCallback);

        $legacyLtiChapterRepository = $this->createMock(LegacyLtiChapterRepository::class);
        $legacyLtiChapterRepository
            ->method('findOneBy')
            ->willReturnCallback($legacyLtiChapterRepositoryCallback);

        $ltiToolRepository = $this->createMock(LtiToolRepository::class);
        if (null !== $ltiToolRepositoryCallback) {
            $ltiToolRepository
                ->method('findOneBy')
                ->willReturnCallback($ltiToolRepositoryCallback);
        }

        $registrationRepository = $this->createMock(RegistrationRepositoryInterface::class);
        $registrationRepository
            ->method('find')
            ->willReturnCallback($registrationRepositoryCallback);
        $registrationRepository
            ->method('findByClientId')
            ->willReturnCallback($registrationRepositoryCallback);

        $ltiKeyProvider = $this->createMock(LtiKeyProvider::class);
        $ltiKeyProvider
            ->method('sign')
            ->willReturnCallback($ltiKeyProviderSignCallback);

        $handler = $this->getHandler(
            legacyCourseRepository: $legacyCourseRepository,
            legacyChapterRepository: $legacyChapterRepository,
            legacyLtiChapterRepository: $legacyLtiChapterRepository,
            ltiToolRepository: $ltiToolRepository,
            registrationRepository: $registrationRepository,
            ltiKeyProvider: $ltiKeyProvider,
        );

        $query = new LaunchLtiChapterQuery(
            courseId: new Id(1),
            chapterId: new Id(1),
            user: UserMother::create(
                id: 1,
            ),
            schemeAndHttpHost: 'https://example.com',
            resultType: LaunchResultType::RedirectForm,
        );
        $this->expectExceptionObject($expectedException);
        $result = $handler->handle($query);
    }

    public static function provideTestExceptions(): \Generator
    {
        $course = CourseMother::create(id: 1);
        $chapter = ChapterMother::create(id: 1, course: $course);
        $ltiChapter = LtiChapterMother::create(id: 1, chapter: $chapter, ltiToolIdentifierId: UuidMother::create()->value());
        $keychain = new KeyChain(
            identifier: 'id',
            keySetName: 'EasyLearning',
            publicKey: new Key(self::PUBLIC_KEY, null, KeyInterface::ALG_RS256),
            privateKey: new Key(self::PRIVATE_KEY, null, KeyInterface::ALG_RS256),
        );

        $registration = new Registration(
            identifier: UuidMother::create()->value(),
            clientId: 'client-id',
            platform: LtiPlatformTransformer::fromLtiPlatformToCorePlatform(
                LtiPlatformMother::create()
            ),
            tool: LtiToolTransformer::fromLtiToolToCoreTool(
                LtiToolMother::create(
                    oidcInitiationUrl: new Url('https://example-tool.com/lti1p3/oidc/initiation'),
                    launchUrl: new Url('https://example-tool.com/lti1p3/launch'),
                    deepLinkingUrl: new Url('https://example-tool.com/lti1p3/deep-linking'),
                    jwksUrl: new Url('https://example-tool.com/lti1p3/.well-known/jwks/keySetName.json'),
                )
            ),
            deploymentIds: ['deployment-1'],
            platformKeyChain: $keychain,
            toolKeyChain: $keychain,
            platformJwksUrl: 'https//example-platform.com/jwks',
            toolJwksUrl: 'https//example-tool.com/jwks',
        );

        yield 'no course' => [
            'legacyCourseRepositoryCallback' => fn () => null,
            'legacyChapterRepositoryCallback' => fn () => null,
            'legacyLtiChapterRepositoryCallback' => fn () => null,
            'ltiToolRepositoryCallback' => null,
            'registrationRepositoryCallback' => fn () => null,
            'ltiKeyProviderSignCallback' => function (string $loginHint, string &$signature) {
                $signature = 'SIGNATURE';

                return true;
            },
            'expectedException' => LaunchLtiChapterQueryHandlerException::courseNotFound(),
        ];

        yield 'no chapter' => [
            'legacyCourseRepositoryCallback' => fn () => $course,
            'legacyChapterRepositoryCallback' => fn () => null,
            'legacyLtiChapterRepositoryCallback' => fn () => null,
            'ltiToolRepositoryCallback' => null,
            'registrationRepositoryCallback' => fn () => null,
            'ltiKeyProviderSignCallback' => function (string $loginHint, string &$signature) {
                $signature = 'SIGNATURE';

                return true;
            },
            'expectedException' => LaunchLtiChapterQueryHandlerException::chapterNotFound(),
        ];

        yield 'no lti chapter' => [
            'legacyCourseRepositoryCallback' => fn () => $course,
            'legacyChapterRepositoryCallback' => fn () => $chapter,
            'legacyLtiChapterRepositoryCallback' => fn () => null,
            'ltiToolRepositoryCallback' => null,
            'registrationRepositoryCallback' => fn () => null,
            'ltiKeyProviderSignCallback' => function (string $loginHint, string &$signature) {
                $signature = 'SIGNATURE';

                return true;
            },
            'expectedException' => new LtiChapterNotFound(),
        ];

        yield 'no configurations chapter' => [
            'legacyCourseRepositoryCallback' => fn () => $course,
            'legacyChapterRepositoryCallback' => fn () => $chapter,
            'legacyLtiChapterRepositoryCallback' => fn () => LtiChapterMother::create(id: 1, chapter: $chapter),
            'ltiToolRepositoryCallback' => null,
            'registrationRepositoryCallback' => fn () => null,
            'ltiKeyProviderSignCallback' => function (string $loginHint, string &$signature) {
                $signature = 'SIGNATURE';

                return true;
            },
            'expectedException' => LaunchLtiChapterQueryHandlerException::ltiChapterNoRelationToConfiguration(),
        ];

        yield 'no registration with lti tool identifier' => [
            'legacyCourseRepositoryCallback' => fn () => $course,
            'legacyChapterRepositoryCallback' => fn () => $chapter,
            'legacyLtiChapterRepositoryCallback' => fn () => $ltiChapter,
            'ltiToolRepositoryCallback' => fn () => LtiToolMother::create(),
            'registrationRepositoryCallback' => fn () => null,
            'ltiKeyProviderSignCallback' => function (string $loginHint, string &$signature) {
                $signature = 'SIGNATURE';

                return true;
            },
            'expectedException' => LaunchLtiChapterQueryHandlerException::noRegistrationForLtiChapter(),
        ];

        $ltiChapter = LtiChapterMother::create(
            id: 1,
            chapter: $chapter,
            legacyLtiTool: (new LegacyLtiTool())->setClientId('client-id'),
        );

        yield 'no registration with legacy lti tool id' => [
            'legacyCourseRepositoryCallback' => fn () => $course,
            'legacyChapterRepositoryCallback' => fn () => $chapter,
            'legacyLtiChapterRepositoryCallback' => fn () => $ltiChapter,
            'ltiToolRepositoryCallback' => null,
            'registrationRepositoryCallback' => fn () => null,
            'ltiKeyProviderSignCallback' => function (string $loginHint, string &$signature) {
                $signature = 'SIGNATURE';

                return true;
            },
            'expectedException' => LaunchLtiChapterQueryHandlerException::noRegistrationForLtiChapter(),
        ];

        yield 'failed signature' => [
            'legacyCourseRepositoryCallback' => fn () => $course,
            'legacyChapterRepositoryCallback' => fn () => $chapter,
            'legacyLtiChapterRepositoryCallback' => fn () => $ltiChapter,
            'ltiToolRepositoryCallback' => null,
            'registrationRepositoryCallback' => fn () => $registration,
            'ltiKeyProviderSignCallback' => fn () => false,
            'expectedException' => LaunchLtiChapterQueryHandlerException::failedToSign(),
        ];
    }
}
