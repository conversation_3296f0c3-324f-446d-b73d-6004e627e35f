<?php

declare(strict_types=1);

namespace App\Tests\Mother\Entity;

use App\Entity\Chapter;
use App\Entity\ChapterType;
use App\Entity\Course;
use App\Entity\Season;

class ChapterMother
{
    public const string DEFAULT_TITLE = 'Chapter 1';
    public const string DEFAULT_DESCRIPTION = 'Chapter 1 description';

    public static function create(
        ?int $id = null,
        ?string $title = null,
        ?string $description = null,
        int $position = 1,
        bool $active = true,
        ?int $maxQuestion = null,
        ?ChapterType $type = null,
        ?Course $course = null,
        ?Season $season = null,
        ?\DateTimeImmutable $deletedAt = null,
    ): Chapter {
        $chapter = new Chapter();
        $type = $type ?? ChapterTypeMother::create();

        if (null !== $id) {
            $chapter->setId($id);
        }

        $chapter->setTitle($title ?? self::DEFAULT_TITLE);
        $chapter->setDescription($description ?? self::DEFAULT_DESCRIPTION);
        $chapter->setPosition($position);
        $chapter->setIsActive($active);
        $chapter->setMaxQuestion($maxQuestion);
        $chapter->setDeletedAt($deletedAt);
        $chapter->setType($type);
        $chapter->setCourse($course);
        $chapter->setSeason($season);

        return $chapter;
    }
}
