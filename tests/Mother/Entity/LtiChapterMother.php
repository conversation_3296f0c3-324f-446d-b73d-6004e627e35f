<?php

declare(strict_types=1);

namespace App\Tests\Mother\Entity;

use App\Entity\Chapter;
use App\Entity\LtiChapter;
use App\Entity\LtiTool as LegacyLtiTool;

class LtiChapterMother
{
    public const string DEFAULT_IDENTIFIER = 'identifier';

    public static function create(
        ?int $id = null,
        ?string $identifier = null,
        ?Chapter $chapter = null,
        ?LegacyLtiTool $legacyLtiTool = null,
        ?string $ltiToolIdentifierId = null,
    ): LtiChapter {
        $lti = new LtiChapter();
        if (null !== $id) {
            $lti->setId($id);
        }

        $lti->setIdentifier($identifier ?? self::DEFAULT_IDENTIFIER)
            ->setChapter($chapter)
            ->setLtiTool($legacyLtiTool)
            ->setLtiToolIdentifierId($ltiToolIdentifierId)
        ;

        return $lti;
    }
}
