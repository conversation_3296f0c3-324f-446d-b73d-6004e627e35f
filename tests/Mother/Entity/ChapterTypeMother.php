<?php

declare(strict_types=1);

namespace App\Tests\Mother\Entity;

use App\Entity\ChapterType;

class ChapterTypeMother
{
    public const string DEFAULT_NAME = 'Content';
    public const string DEFAULT_DESCRIPTION = 'Content description';
    public const string DEFAULT_TYPE = 'content';
    public const string DEFAULT_CODE = 'content';
    public const string DEFAULT_NORMALIZED = 'Content';
    public const float DEFAULT_PERCENTAGE_COMPLETED = 75.0;

    public static function create(
        ?int $id = null,
        ?string $name = null,
        ?string $description = null,
        ?string $type = null,
        ?string $code = null,
        bool $active = true,
        ?string $normalized = null,
        ?string $video = null,
        ?string $videoEn = null,
        ?float $percentageCompleted = null,
        ?string $icon = null,
        ?string $playerUrl = null,
    ): ChapterType {
        $chapterType = new ChapterType();
        if (null !== $id) {
            $chapterType->setId($id);
        }

        $chapterType->setName($name ?? self::DEFAULT_NAME);
        $chapterType->setDescription($description ?? self::DEFAULT_DESCRIPTION);
        $chapterType->setType($type ?? self::DEFAULT_TYPE);
        $chapterType->setCode($code ?? self::DEFAULT_CODE);
        $chapterType->setActive($active);
        $chapterType->setNormalized($normalized ?? self::DEFAULT_NORMALIZED);
        $chapterType->setVideo($video);
        $chapterType->setVideoEn($videoEn);
        $chapterType->setPercentageCompleted(
            '' . ($percentageCompleted ?? self::DEFAULT_PERCENTAGE_COMPLETED)
        );
        $chapterType->setIcon($icon ?? 'icon.png');
        $chapterType->setPlayerUrl($playerUrl);

        return $chapterType;
    }
}
