<?php

declare(strict_types=1);

namespace App\Tests\Mother\Entity;

use App\Entity\RefreshToken;

class RefreshTokenMother
{
    public static function create(
        ?string $username = null,
        ?string $refreshToken = null,
        ?\DateTime $valid = null,
        ?array $extra = null,
    ): RefreshToken {
        $token = new RefreshToken();
        $token->setUsername($username ?? UserMother::DEFAULT_EMAIL)
            ->setRefreshToken($refreshToken ?? bin2hex(random_bytes(64)))
            ->setValid($valid ?? (new \DateTime())->modify('+1 minute'))
            ->setExtra($extra);

        return $token;
    }
}
