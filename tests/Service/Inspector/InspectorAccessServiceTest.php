<?php

declare(strict_types=1);

namespace App\Tests\Service\Inspector;

use App\Entity\Announcement;
use App\Entity\AnnouncementInspectorAccess;
use App\Entity\Course;
use App\Entity\User;
use App\Repository\UserRepository;
use App\Service\Inspector\InspectorAccessService;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityRepository;
use PHPUnit\Framework\TestCase;

class InspectorAccessServiceTest extends TestCase
{
    private const USER_ID = 1;
    private const COURSE_ID = 2;
    private const ANNOUNCEMENT_ID = 3;

    private function getInspectorAccessService(?EntityManager $entityManager = null): InspectorAccessService
    {
        $entityManager = $entityManager ?? $this->createMock(EntityManager::class);

        return new InspectorAccessService($entityManager);
    }

    private function getAnnouncementInspectorAccess(?Announcement $announcement = null): AnnouncementInspectorAccess
    {
        $announcementInspectorAccess = new AnnouncementInspectorAccess();

        if ($announcement) {
            $announcementInspectorAccess->setAnnouncement($announcement);
        }

        return $announcementInspectorAccess;
    }

    private function getEntityManagerMock(?User $user = null): EntityManager
    {
        $userRepository = $this->createMock(UserRepository::class);
        $userRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(['email' => '<EMAIL>'])
            ->willReturn($user);

        $entityManager = $this->createMock(EntityManager::class);
        $entityManager
            ->expects($this->once())
            ->method('getRepository')
            ->with(User::class)
            ->willReturn($userRepository);

        return $entityManager;
    }

    public function testGetInspectorAccessWithNoAnnouncement(): void
    {
        $inspectorAccess = $this->getAnnouncementInspectorAccess(null);

        $inspectorService = $this->getInspectorAccessService();

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Announcement not found');

        $inspectorService->getInspectorAccess($inspectorAccess);
    }

    public function testUserNotFoundException(): void
    {
        $inspectorAccess = $this->getAnnouncementInspectorAccess(new Announcement(), '<EMAIL>');


        $inspectorService = $this->getInspectorAccessService($this->getEntityManagerMock(null));

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('User not found');

        $inspectorService->getInspectorAccess($inspectorAccess);
    }

    public function testOk(): void
    {
        $user = $this->createMock(User::class);
        $user
            ->expects($this->once())
            ->method('getId')
            ->willReturn(self::USER_ID);

        $course = $this->createMock(Course::class);
        $course
            ->expects($this->once())
            ->method('getId')
            ->willReturn(self::COURSE_ID);

        $announcement = $this->createMock(Announcement::class);
        $announcement
            ->expects($this->once())
            ->method('getId')
            ->willReturn(self::ANNOUNCEMENT_ID);
        $announcement
            ->expects($this->once())
            ->method('getCourse')
            ->willReturn($course);

        $announcementInspectorAccess = $this->getAnnouncementInspectorAccess($announcement);

        $inspectorAccessService = $this->getInspectorAccessService($this->getEntityManagerMock($user));

        $response = $inspectorAccessService->getInspectorAccess($announcementInspectorAccess);

        $this->assertEquals(self::USER_ID,  $response['user']);
        $this->assertEquals(self::COURSE_ID, $response['course']);
        $this->assertEquals(self::ANNOUNCEMENT_ID, $response['announcement']);
    }
}
