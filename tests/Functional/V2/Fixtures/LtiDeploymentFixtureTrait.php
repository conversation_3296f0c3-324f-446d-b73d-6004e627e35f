<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Fixtures;

use App\Tests\V2\Mother\LTI\LtiDeploymentMother;
use App\V2\Domain\LTI\LtiDeployment;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;

trait LtiDeploymentFixtureTrait
{
    /**
     * @throws InvalidUuidException
     */
    private function setAndGetLtiDeploymentInRepository(
        ?Uuid $id = null,
        ?Uuid $registrationId = null,
        ?string $name = null,
        ?string $deploymentId = null,
    ): LtiDeployment {
        $deployment = LtiDeploymentMother::create(
            id: $id,
            registrationId: $registrationId,
            name: $name,
            deploymentId: $deploymentId,
        );
        $this->client->getContainer()->get('App\V2\Domain\LTI\LtiDeploymentRepository')
            ->put($deployment);

        return $deployment;
    }
}
