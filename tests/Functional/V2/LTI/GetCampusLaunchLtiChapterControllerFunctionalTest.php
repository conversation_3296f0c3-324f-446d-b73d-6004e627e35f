<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\LTI;

use App\Tests\Functional\HelperTrait\Endpoints\Frontend\FrontendLtiEndpoints;

class GetCampusLaunchLtiChapterControllerFunctionalTest extends LaunchLtiChapterControllerFunctionalTestCase
{
    protected function getEndpoint(int $courseId, int $chapterId): string
    {
        return FrontendLtiEndpoints::getLaunchLtiChapter($courseId, $chapterId);
    }
}
