<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\LTI;

use App\Entity\Chapter;
use App\Entity\ChapterTypeConstants;
use App\Entity\Course;
use App\Entity\LtiChapter;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\ChapterHelperTrait;
use App\Tests\Functional\HelperTrait\ChapterTypeHelperTrait;
use App\Tests\Functional\HelperTrait\CourseHelperTrait;
use App\Tests\Functional\HelperTrait\LtiChapterHelperTrait;
use App\Tests\Functional\HelperTrait\SeasonHelperTrait;
use App\Tests\Functional\HelperTrait\UserHelperTrait;
use App\Tests\Functional\V2\Fixtures\LtiDeploymentFixtureTrait;
use App\Tests\Functional\V2\Fixtures\LtiPlatformFixtureTrait;
use App\Tests\Functional\V2\Fixtures\LtiRegistrationFixtureTrait;
use App\Tests\Functional\V2\Fixtures\LtiToolFixtureTrait;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Shared\Url\Url;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Symfony\Component\HttpFoundation\Response;

abstract class LaunchLtiChapterControllerFunctionalTestCase extends FunctionalTestCase
{
    use LtiRegistrationFixtureTrait;
    use LtiPlatformFixtureTrait;
    use LtiToolFixtureTrait;
    use LtiDeploymentFixtureTrait;
    use UserHelperTrait;
    use CourseHelperTrait;
    use SeasonHelperTrait;
    use ChapterTypeHelperTrait;
    use ChapterHelperTrait;
    use LtiChapterHelperTrait;

    abstract protected function getEndpoint(int $courseId, int $chapterId): string;

    protected function setUp(): void
    {
        parent::setUp();
        $this->getEntityManager()
            ->getConnection()
            ->executeStatement('SET FOREIGN_KEY_CHECKS=0;');
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws InvalidUuidException
     */
    public function testGetLaunchUrl(): void
    {
        $course = $this->createAndGetCourse(
            name: 'Test LTI Course'
        );

        $season = $this->createAndGetSeason(course: $course);

        $chapterType = $this->createAndGetChapterTypeRevised(
            id: ChapterTypeConstants::LTI_TYPE,
            name: 'LTI',
        );

        $chapter = $this->createAndGetChapter(
            course: $course,
            season: $season,
            title: 'Test LTI',
            chapterType: $chapterType,
        );

        $registration = $this->setAndGetLtiRegistrationInRepository(
            name: 'Registration 1',
            clientId: 'registration-1'
        );

        $platform = $this->setAndGetLtiPlatformInRepository(
            registrationId: $registration->getId(),
            name: 'Platform 1',
            audience: 'https://audience.com',
            oidcAuthenticationUrl: new Url('https://example-platform.com/authentication'),
            oauth2AccessTokenUrl: new Url('https://example-platform.com/oauth2/token'),
            jwksUrl: new Url('https://example-platform.com/jwks'),
        );

        $tool = $this->setAndGetLtiToolInRepository(
            registrationId: $registration->getId(),
            name: 'Tool 1',
            audience: 'https://audience.com',
            oidcInitiationUrl: new Url('https://example-tool.com/login-initiation'),
            launchUrl: new Url('https://example-tool.com/launch'),
            deepLinkingUrl: new Url('https://example-tool.com/deep-linking'),
            jwksUrl: new Url('https://example-tool.com/jwks'),
        );

        $deployment1 = $this->setAndGetLtiDeploymentInRepository(
            registrationId: $registration->getId(),
            name: 'Deployment 1',
            deploymentId: 'deployment-1'
        );

        $identifier = UuidMother::create();
        $this->createAndGetLtiChapter(
            chapter: $chapter,
            identifier: $identifier->value(),
            ltiToolIdentifier: $tool->getId()->value(),
        );

        $token = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'GET',
            uri: $this->getEndpoint($course->getId(), $chapter->getId()),
            bearerToken: $token,
        );

        $result = $response->getContent();
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertStringContainsString('form id="launch_', $result);
        $this->assertStringContainsString('action="https://example-tool.com/login-initiation"', $result);
        $this->assertStringContainsString('method="POST"', $result);
        $this->assertStringContainsString(
            '<input type="hidden" name="target_link_uri" value="https://example-tool.com/launch"/>',
            $result
        );
    }

    public function testUnprocessableEntity(): void
    {
        $token = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'GET',
            uri: $this->getEndpoint(9999, 9999),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Course not found', $content['message']);

        $course = $this->createAndGetCourse(
            name: 'Test LTI Course'
        );

        $response = $this->makeRequest(
            method: 'GET',
            uri: $this->getEndpoint($course->getId(), 9999),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Chapter not found', $content['message']);
    }

    public function testBadRequest(): void
    {
        $token = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'GET',
            uri: $this->getEndpoint(-1, 1),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Validation failed', $content['message']);
        $this->assertArrayHasKey('metadata', $content);
        $this->assertEquals([
            'violations' => [
                '[id]' => 'id must be greater than 0.',
            ],
        ], $content['metadata']);

        $response = $this->makeRequest(
            method: 'GET',
            uri: $this->getEndpoint(1, -1),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Validation failed', $content['message']);
        $this->assertArrayHasKey('metadata', $content);
        $this->assertEquals([
            'violations' => [
                '[id]' => 'id must be greater than 0.',
            ],
        ], $content['metadata']);
    }

    protected function tearDown(): void
    {
        $this->getEntityManager()
            ->getConnection()
            ->executeStatement('SET FOREIGN_KEY_CHECKS=1;');

        $this->truncateEntities([
            Chapter::class, Course::class, LtiChapter::class,
        ]);
        parent::tearDown();
    }
}
