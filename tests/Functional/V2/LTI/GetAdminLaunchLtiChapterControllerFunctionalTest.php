<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\LTI;

use App\Tests\Functional\HelperTrait\Endpoints\Admin\LtiEndpoints;

class GetAdminLaunchLtiChapterControllerFunctionalTest extends LaunchLtiChapterControllerFunctionalTestCase
{
    protected function getEndpoint(int $courseId, int $chapterId): string
    {
        return LtiEndpoints::getLaunchLtiChapter($courseId, $chapterId);
    }
}
