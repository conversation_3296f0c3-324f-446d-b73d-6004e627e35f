<?php

declare(strict_types=1);

namespace App\Tests\Functional\Service;

use App\Entity\Task;
use App\Entity\ZipFileTask;
use App\Exception\NoAvailableSlotException;
use App\Service\SlotManagerService;
use App\Tests\Functional\FunctionalTestCase;

class TaskTimeoutAndSlotTest extends FunctionalTestCase
{
    private const TEST_DEBUG_MODE = false; // Desactivate debug mode
    private const DEFAULT_TIMEOUT_SECONDS = 3600; // 1 hour in seconds
    private const CUSTOM_TIMEOUT_SECONDS = 1800; // 30 minutes in seconds
    private const SHORT_TIMEOUT_SECONDS = 60; // 1 minute in seconds
    private const DEFAULT_SLOT_QUANTITY = 3;

    protected function setUp(): void
    {
        self::$debugMode = self::TEST_DEBUG_MODE;
        parent::setUp();
        $this->truncateEntities([Task::class, ZipFileTask::class]);

        // Configure default settings
        $this->updateSettingsInDatabase([
            'app.export.task.timeout_seconds' => (string) self::DEFAULT_TIMEOUT_SECONDS,
            'app.export.task.slot_quantity' => (string) self::DEFAULT_SLOT_QUANTITY
        ]);

        // Verify the settings were saved correctly
        $settings = $this->getService('App\Service\SettingsService');
        $settings->loadSettings(); // Reload settings from database
        $this->log('Default timeout seconds: ' . $settings->get('app.export.task.timeout_seconds'));
        $this->log('Default slot quantity: ' . $settings->get('app.export.task.slot_quantity'));
    }

    protected function tearDown(): void
    {
        $this->truncateEntities([Task::class, ZipFileTask::class]);
        parent::tearDown();
    }

    /**
     * Test that the SlotManagerService correctly handles Tasks in TIMEOUT status
     * based on the configured timeout period.
     */
    public function testTaskTimeoutInSlotManager(): void
    {
        $em = $this->getEntityManager();
        $slotManagerService = $this->getService(SlotManagerService::class);
        $user = $this->getDefaultUser();

        // Create a Task with TIMEOUT status that started less than the default timeout ago
        $taskRepository = $em->getRepository(Task::class);
        $recentTimeoutTask = $taskRepository->newTask('export-file', ['param1' => 'value1']);
        $recentTimeoutTask->setCreatedBy($user)
            ->setStatus(Task::TASK_TIMEOUT)
            ->setStartedAt(new \DateTimeImmutable('-' . (self::DEFAULT_TIMEOUT_SECONDS - 600) . ' seconds')); // 10 minutes before timeout
        $em->persist($recentTimeoutTask);
        $em->flush();

        // Try to get an available slot - should consider the recent task as running
        try {
            $slot = $slotManagerService->getAvailableTaskExecutionSlot();
            $this->assertNotNull($slot, 'Should get a slot since only one task is running');
            $this->log('Got slot with one TIMEOUT task running');
        } catch (\Exception $e) {
            $this->fail('Should not throw an exception: ' . $e->getMessage());
        }

        // Create more tasks to reach the slot limit (but leave one slot for the TIMEOUT task)
        for ($i = 0; $i < self::DEFAULT_SLOT_QUANTITY - 1; ++$i) {
            $task = $taskRepository->newTask('export-file', ['param1' => 'value1']);
            $task->setCreatedBy($user)
                ->setStatus(Task::TASK_INPROGRESS)
                ->setStartedAt(new \DateTimeImmutable());
            $em->persist($task);
        }
        $em->flush();

        // Now we should have DEFAULT_SLOT_QUANTITY running tasks (1 in TIMEOUT status within timeout period, rest in INPROGRESS)
        // Try to get another slot - should throw an exception
        $settings = $this->getService('App\Service\SettingsService');
        $settings->loadSettings(); // Reload settings from database
        $this->log('Current slot quantity: ' . $settings->get('app.export.task.slot_quantity'));
        $this->log('Current timeout seconds: ' . $settings->get('app.export.task.timeout_seconds'));

        // Log all tasks in the database
        $allTasks = $em->getRepository(Task::class)->findAll();
        $this->log('Total tasks in database: ' . \count($allTasks));
        foreach ($allTasks as $index => $task) {
            $this->log(\sprintf(
                'Task %d: ID=%d, Status=%d, StartedAt=%s',
                $index + 1,
                $task->getId(),
                $task->getStatus(),
                $task->getStartedAt() ? $task->getStartedAt()->format('Y-m-d H:i:s') : 'null'
            ));
        }

        // Count running tasks
        $timeoutSeconds = (int) $settings->get('app.export.task.timeout_seconds');
        $this->log('Timeout seconds: ' . $timeoutSeconds);
        $runningTasks = $em->getRepository(Task::class)->createQueryBuilder('t')
            ->where('t.status = :inProgressStatus')
            ->setParameter('inProgressStatus', Task::TASK_INPROGRESS)
            ->orWhere('t.status = :timeoutStatus AND t.startedAt IS NOT NULL AND t.startedAt > :timeoutAgo')
            ->setParameter('timeoutStatus', Task::TASK_TIMEOUT)
            ->setParameter('timeoutAgo', new \DateTimeImmutable('-' . $timeoutSeconds . ' seconds'))
            ->getQuery()->getResult();
        $this->log('Running tasks count: ' . \count($runningTasks));

        try {
            // Log the query that will be executed
            $timeoutAgo = new \DateTimeImmutable('-' . $timeoutSeconds . ' seconds');
            $this->log('Timeout ago: ' . $timeoutAgo->format('Y-m-d H:i:s'));

            // Log the slot quantity
            $slotQuantity = (int) $settings->get('app.export.task.slot_quantity');
            $this->log('Slot quantity: ' . $slotQuantity);

            // Try to get a slot
            $slot = $slotManagerService->getAvailableTaskExecutionSlot();
            $this->log('Got slot: ' . ($slot ? 'yes' : 'no'));
            $this->fail('Should throw an exception because all slots are used');
        } catch (NoAvailableSlotException $e) {
            $this->assertStringContainsString('TASK-SLOTS-EXCEEDED', $e->getMessage());
            $this->log('Correctly got exception: ' . $e->getMessage());
        }

        // Change the timeout to a shorter value so the TIMEOUT task is no longer considered running
        $this->updateSettingsInDatabase([
            'app.export.task.timeout_seconds' => (string) self::SHORT_TIMEOUT_SECONDS
        ]);

        // Reload settings in the service to ensure it uses the new timeout value
        $settings = $this->getService('App\Service\SettingsService');
        $settings->loadSettings();
        $this->log('Reduced timeout to: ' . $settings->get('app.export.task.timeout_seconds'));

        // Now we should be able to get a slot
        try {
            $slot = $slotManagerService->getAvailableTaskExecutionSlot();
            $this->assertNotNull($slot, 'Should get a slot since the TIMEOUT task is now expired');
            $this->log('Got slot after reducing timeout period');
        } catch (\Exception $e) {
            $this->fail('Should not throw an exception: ' . $e->getMessage());
        }
    }

    /**
     * Test that the slot quantity setting is respected for Tasks.
     */
    public function testTaskSlotQuantityLimit(): void
    {
        $em = $this->getEntityManager();
        $slotManagerService = $this->getService(SlotManagerService::class);
        $user = $this->getDefaultUser();

        // Set a custom slot quantity
        $customSlotQuantity = 2;
        $this->updateSettingsInDatabase([
            'app.export.task.slot_quantity' => (string) $customSlotQuantity
        ]);
        $this->log('Set custom slot quantity: ' . $customSlotQuantity);

        // Create tasks up to the slot limit
        $taskRepository = $em->getRepository(Task::class);
        for ($i = 0; $i < $customSlotQuantity; ++$i) {
            $task = $taskRepository->newTask('export-file', ['param1' => 'value1']);
            $task->setCreatedBy($user)
                ->setStatus(Task::TASK_INPROGRESS)
                ->setStartedAt(new \DateTimeImmutable());
            $em->persist($task);
        }
        $em->flush();

        // Try to get another slot - should throw an exception
        $settings = $this->getService('App\Service\SettingsService');
        $settings->loadSettings(); // Reload settings from database
        $this->log('Current slot quantity: ' . $settings->get('app.export.task.slot_quantity'));

        // Log all tasks in the database
        $allTasks = $em->getRepository(Task::class)->findAll();
        $this->log('Total tasks in database: ' . \count($allTasks));
        foreach ($allTasks as $index => $task) {
            $this->log(\sprintf(
                'Task %d: ID=%d, Status=%d, StartedAt=%s',
                $index + 1,
                $task->getId(),
                $task->getStatus(),
                $task->getStartedAt() ? $task->getStartedAt()->format('Y-m-d H:i:s') : 'null'
            ));
        }

        try {
            // Try to get a slot
            $slot = $slotManagerService->getAvailableTaskExecutionSlot();
            $this->log('Got slot: ' . ($slot ? 'yes' : 'no'));
            $this->fail('Should throw an exception because all slots are used');
        } catch (NoAvailableSlotException $e) {
            $this->assertStringContainsString('TASK-SLOTS-EXCEEDED', $e->getMessage());
            $this->log('Correctly got exception: ' . $e->getMessage());
        }

        // Increase the slot quantity
        $settingRepository = $em->getRepository('App\Entity\Setting');
        $slotSetting = $settingRepository->findOneBy(['code' => 'app.export.task.slot_quantity']);
        $slotSetting->setValue((string) ($customSlotQuantity + 1));
        $em->persist($slotSetting);
        $em->flush();

        // Reload settings in the service
        $settings = $this->getService('App\Service\SettingsService');
        $settings->loadSettings();
        $this->log('Increased slot quantity to: ' . $settings->get('app.export.task.slot_quantity'));

        // Now we should be able to get a slot
        try {
            $slot = $slotManagerService->getAvailableTaskExecutionSlot();
            $this->assertNotNull($slot, 'Should get a slot after increasing the slot quantity');
            $this->log('Got slot after increasing slot quantity');
        } catch (\Exception $e) {
            $this->fail('Should not throw an exception: ' . $e->getMessage());
        }
    }

    /**
     * Test the interaction between timeout and slot quantity settings for Tasks.
     */
    public function testTaskTimeoutAndSlotInteraction(): void
    {
        $em = $this->getEntityManager();
        $slotManagerService = $this->getService(SlotManagerService::class);
        $user = $this->getDefaultUser();

        // Set a custom slot quantity and timeout
        $customSlotQuantity = 2;
        $this->updateSettingsInDatabase([
            'app.export.task.slot_quantity' => (string) $customSlotQuantity,
            'app.export.task.timeout_seconds' => (string) self::CUSTOM_TIMEOUT_SECONDS
        ]);
        $this->log('Set custom slot quantity: ' . $customSlotQuantity);
        $this->log('Set custom timeout: ' . self::CUSTOM_TIMEOUT_SECONDS);

        // Create a task with TIMEOUT status that started less than the custom timeout ago
        $taskRepository = $em->getRepository(Task::class);
        $recentTimeoutTask = $taskRepository->newTask('export-file', ['param1' => 'value1']);
        $recentTimeoutTask->setCreatedBy($user)
            ->setStatus(Task::TASK_TIMEOUT)
            ->setStartedAt(new \DateTimeImmutable('-' . (self::CUSTOM_TIMEOUT_SECONDS - 300) . ' seconds')); // 5 minutes before timeout
        $em->persist($recentTimeoutTask);
        $em->flush();

        // Verify we can get a slot with just one task
        try {
            $slot = $slotManagerService->getAvailableTaskExecutionSlot();
            $this->assertNotNull($slot, 'Should get a slot since only one task is running');
            $this->log('Got slot with one TIMEOUT task running');
        } catch (\Exception $e) {
            $this->fail('Should not throw an exception: ' . $e->getMessage());
        }

        // Create one more task in INPROGRESS status to reach the slot limit
        $inProgressTask = $taskRepository->newTask('export-file', ['param1' => 'value1']);
        $inProgressTask->setCreatedBy($user)
            ->setStatus(Task::TASK_INPROGRESS)
            ->setStartedAt(new \DateTimeImmutable());
        $em->persist($inProgressTask);
        $em->flush();

        // Reload settings to ensure we have the correct values
        $settings = $this->getService('App\Service\SettingsService');
        $settings->loadSettings();

        // Try to get another slot - should throw an exception
        try {
            $slotManagerService->getAvailableTaskExecutionSlot();
            $this->fail('Should throw an exception because all slots are used');
        } catch (NoAvailableSlotException $e) {
            $this->assertStringContainsString('TASK-SLOTS-EXCEEDED', $e->getMessage());
            $this->log('Correctly got exception: ' . $e->getMessage());
        }

        // Change the timeout to a shorter value so the TIMEOUT task is no longer considered running
        $settingRepository = $em->getRepository('App\Entity\Setting');
        $timeoutSetting = $settingRepository->findOneBy(['code' => 'app.export.task.timeout_seconds']);
        $timeoutSetting->setValue((string) self::SHORT_TIMEOUT_SECONDS);
        $em->persist($timeoutSetting);
        $em->flush();

        // Reload settings in the service
        $settings = $this->getService('App\Service\SettingsService');
        $settings->loadSettings();
        $this->log('Reduced timeout to: ' . $settings->get('app.export.task.timeout_seconds'));

        // Now we should be able to get a slot
        try {
            $slot = $slotManagerService->getAvailableTaskExecutionSlot();
            $this->assertNotNull($slot, 'Should get a slot since the TIMEOUT task is now expired');
            $this->log('Got slot after reducing timeout period');
        } catch (\Exception $e) {
            $this->fail('Should not throw an exception: ' . $e->getMessage());
        }

        // Create another task in INPROGRESS status to reach the slot limit again
        $inProgressTask2 = $taskRepository->newTask('export-file', ['param1' => 'value1']);
        $inProgressTask2->setCreatedBy($user)
            ->setStatus(Task::TASK_INPROGRESS)
            ->setStartedAt(new \DateTimeImmutable());
        $em->persist($inProgressTask2);
        $em->flush();

        // Try to get another slot - should throw an exception again
        try {
            $slotManagerService->getAvailableTaskExecutionSlot();
            $this->fail('Should throw an exception because all slots are used');
        } catch (NoAvailableSlotException $e) {
            $this->assertStringContainsString('TASK-SLOTS-EXCEEDED', $e->getMessage());
            $this->log('Correctly got exception: ' . $e->getMessage());
        }
    }

    /**
     * Test that the countPendingTasksByUser method correctly respects the timeout setting.
     */
    public function testCountPendingTasksByUserRespectsTimeout(): void
    {
        $em = $this->getEntityManager();
        $user = $this->getDefaultUser();

        // Create tasks with different statuses and start times
        $taskRepository = $em->getRepository(Task::class);
        $pendingTask = $taskRepository->newTask('export-file', ['param1' => 'value1']);
        $pendingTask->setCreatedBy($user)
            ->setStatus(Task::TASK_PENDING);
        $em->persist($pendingTask);

        $inProgressTask = $taskRepository->newTask('export-file', ['param1' => 'value1']);
        $inProgressTask->setCreatedBy($user)
            ->setStatus(Task::TASK_INPROGRESS)
            ->setStartedAt(new \DateTimeImmutable());
        $em->persist($inProgressTask);

        $recentTimeoutTask = $taskRepository->newTask('export-file', ['param1' => 'value1']);
        $recentTimeoutTask->setCreatedBy($user)
            ->setStatus(Task::TASK_TIMEOUT)
            ->setStartedAt(new \DateTimeImmutable('-' . (self::DEFAULT_TIMEOUT_SECONDS - 600) . ' seconds')); // 10 minutes before timeout
        $em->persist($recentTimeoutTask);

        $oldTimeoutTask = $taskRepository->newTask('export-file', ['param1' => 'value1']);
        $oldTimeoutTask->setCreatedBy($user)
            ->setStatus(Task::TASK_TIMEOUT)
            ->setStartedAt(new \DateTimeImmutable('-' . (self::DEFAULT_TIMEOUT_SECONDS + 600) . ' seconds')); // 10 minutes after timeout
        $em->persist($oldTimeoutTask);

        $completedTask = $taskRepository->newTask('export-file', ['param1' => 'value1']);
        $completedTask->setCreatedBy($user)
            ->setStatus(Task::TASK_SUCCESS) // Usar TASK_SUCCESS en lugar de TASK_COMPLETED
            ->setStartedAt(new \DateTimeImmutable('-1 hour'))
            ->setFinishedAt(new \DateTimeImmutable()); // Usar setFinishedAt en lugar de setCompletedAt
        $em->persist($completedTask);

        $em->flush();

        // Count pending tasks with default timeout
        $taskRepository = $em->getRepository(Task::class);
        $pendingCount = $taskRepository->countPendingTasksByUser($user);
        $this->assertEquals(3, $pendingCount, 'Should count PENDING, INPROGRESS, and recent TIMEOUT tasks as pending');
        $this->log('Pending count with default timeout: ' . $pendingCount);

        // Change the timeout to a shorter value
        $settingRepository = $em->getRepository('App\Entity\Setting');
        $timeoutSetting = $settingRepository->findOneBy(['code' => 'app.export.task.timeout_seconds']);
        $timeoutSetting->setValue((string) self::SHORT_TIMEOUT_SECONDS);
        $em->persist($timeoutSetting);
        $em->flush();

        // Reload settings in the service
        $settings = $this->getService('App\Service\SettingsService');
        $settings->loadSettings();
        $this->log('Reduced timeout to: ' . $settings->get('app.export.task.timeout_seconds'));

        // Count pending tasks with shorter timeout
        $pendingCount = $taskRepository->countPendingTasksByUser($user);
        $this->assertEquals(2, $pendingCount, 'Should only count PENDING and INPROGRESS tasks as pending with shorter timeout');
        $this->log('Pending count with shorter timeout: ' . $pendingCount);

        // Create a new timeout task within the shorter timeout period
        $veryRecentTimeoutTask = $taskRepository->newTask('export-file', ['param1' => 'value1']);
        $veryRecentTimeoutTask->setCreatedBy($user)
            ->setStatus(Task::TASK_TIMEOUT)
            ->setStartedAt(new \DateTimeImmutable('-' . (self::SHORT_TIMEOUT_SECONDS - 10) . ' seconds')); // 10 seconds before timeout
        $em->persist($veryRecentTimeoutTask);
        $em->flush();

        // Count pending tasks again
        $pendingCount = $taskRepository->countPendingTasksByUser($user);
        $this->assertEquals(3, $pendingCount, 'Should count PENDING, INPROGRESS, and very recent TIMEOUT tasks as pending');
        $this->log('Pending count after adding very recent timeout task: ' . $pendingCount);
    }
}
