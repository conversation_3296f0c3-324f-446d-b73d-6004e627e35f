<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait;

use App\Entity\Chapter;
use App\Entity\LtiChapter;
use App\Entity\LtiTool as LegacyLtiTool;
use App\Tests\Mother\Entity\LtiChapterMother;

trait LtiChapterHelperTrait
{
    protected function createAndGetLtiChapter(
        ?Chapter $chapter,
        ?string $identifier = null,
        ?LegacyLtiTool $legacyLtiTool = null,
        ?string $ltiToolIdentifier = null,
    ): LtiChapter {
        $ltiChapter = LtiChapterMother::create(
            identifier: $identifier,
            chapter: $chapter,
            legacyLtiTool: $legacyLtiTool,
            ltiToolIdentifierId: $ltiToolIdentifier
        );

        $em = $this->getEntityManager();

        $em->persist($ltiChapter);
        $em->flush();

        return $ltiChapter;
    }
}
