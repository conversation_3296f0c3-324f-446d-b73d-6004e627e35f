<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait;

use App\Entity\Filter;
use App\Entity\FilterCategory;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;

trait FilterHelperTrait
{
    private const string FILTER_CATEGORY_NAME = 'Test Category';
    private const string FILTER_NAME = 'Test Filter';
    private const int FILTER_SORT = 1;
    private const bool IS_RANKING_FILTER = true;

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function createAndGetFilterCategory(
        string $name = self::FILTER_CATEGORY_NAME,
        ?int $sort = null,
        ?bool $is_ranking = null,
    ): FilterCategory {
        $filterCategory = new FilterCategory();
        $filterCategory->setName($name)
        ->setSort($sort ?? self::FILTER_SORT)
        ->setIsRanking($is_ranking ?? self::IS_RANKING_FILTER);

        $em = $this->getEntityManager();
        $em->persist($filterCategory);
        $em->flush();

        return $filterCategory;
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function createAndGetFilter(
        string $name = self::FILTER_NAME,
        ?FilterCategory $category = null,
    ): Filter {
        $filter = new Filter();
        $filter->setName($name);

        if (null === $category) {
            $category = $this->createAndGetFilterCategory();
        }

        $filter->setFilterCategory($category);

        $em = $this->getEntityManager();
        $em->persist($filter);
        $em->flush();

        return $filter;
    }
}
