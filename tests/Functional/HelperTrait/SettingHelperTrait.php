<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait;

use App\Entity\Setting;
use App\Entity\SettingGroup;
use App\Tests\Mother\SettingMother;

trait SettingHelperTrait
{
    protected function createAndGetSetting(
        ?int $id = null,
        string $code = 'app.setting.default',
        string $name = 'Default Setting',
        ?string $description = 'Description',
        int $sort = 999,
        array $options = [],
        string $type = 'bool',
        string $value = 'false',
        ?SettingGroup $settingGroup = null
    ): Setting {
        $em = $this->getEntityManager();
        if (null !== $id) {
            $setting = $em->getRepository(Setting::class)->find($id);
            if (null !== $setting) {
                return $setting;
            }
        }

        $setting = SettingMother::create(
            id: $id,
            code: $code,
            name: $name,
            description: $description,
            sort: $sort,
            options: $options,
            type: $type,
            value: $value,
            settingGroup: $settingGroup
        );

        $originalMetadata = $this->setCustomIdToEntity($setting);
        $em->persist($setting);
        $em->flush();
        $this->restoreEntityMetadata($setting, $originalMetadata);

        return $setting;
    }
}
