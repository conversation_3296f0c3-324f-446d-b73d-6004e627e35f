<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait;

use App\Entity\Itinerary;
use App\Tests\Mother\Entity\ItineraryMother;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;

/**
 * Trait with methods to create Itinerary.
 */
trait ItineraryHelperTrait
{
    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function createAndGetItinerary(
        ?int $id = null,
        ?string $name = null,
        ?string $description = null,
        ?bool $active = null,
        ?int $sort = null,
    ): Itinerary {
        $em = $this->getEntityManager();
        $itinerary = ItineraryMother::create(
            id: $id,
            name: $name,
            description: $description,
            active: $active,
            sort: $sort,
            createdBy: $this->getDefaultUser()
        );

        $originalMetadata = $this->setCustomIdToEntity($itinerary);
        $em->persist($itinerary);
        $em->flush();
        $this->restoreEntityMetadata($itinerary, $originalMetadata);

        return $itinerary;
    }
}
