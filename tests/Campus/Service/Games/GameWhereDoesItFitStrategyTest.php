<?php

declare(strict_types=1);

namespace App\Tests\Campus\Service\Games;

use App\Campus\Games\GameStrategyInterface;
use App\Campus\Games\WhereDoesItFit;
use App\Entity\Chapter;
use App\Entity\ChapterType;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;

class GameWhereDoesItFitStrategyTest extends TestGameStrategyInterface
{
    public function getStrategy(): GameStrategyInterface
    {
        return new WhereDoesItFit(
            $this->createMock(EntityManagerInterface::class),
            $this->createMock(SettingsService::class)
        );
    }

    public static function getValidData(): \Generator
    {
        $chapter = new Chapter();
        $chapterType = new ChapterType();
        $chapterType->setCode('999');
        $chapterType->setPercentageCompleted('0.75');
        $chapter->setType($chapterType);

        $answers = [
            [
                'id' => 1,
                'questionId' => 1,
                'time' => 2,
                'correct' => false,
            ],
        ];

        $attempts = [
            [
                'id' => 1,
                'questionId' => 1,
                'time' => 5,
                'correct' => true,
            ],
            [
                'id' => 2,
                'questionId' => 2,
                'time' => 2,
                'correct' => true,
            ],
        ];

        yield 'result ko, no chapter passed' => [
            'data' => ['answers' => $answers,
                'totalQuestions' => 1,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => null,
            'expectedPoints' => null,
        ];

        yield 'result ko, no total time passed' => [
            'data' => ['answers' => $answers,
                'totalQuestions' => 1,
                'timeTotal' => null,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0,
        ];

        yield 'result ko, no attempts passed' => [
            'data' => ['answers' => $answers,
                'totalQuestions' => 1,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => null,
            ],
            'args' => $chapter,
            'expectedPoints' => 0,
        ];

        yield 'result ko, no answers passed' => [
            'data' => ['answers' => null,
                'totalQuestions' => 1,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0,
        ];

        yield 'result ko all answers incorrect. With 2 attempts' => [
            'data' => ['answers' => $answers,
                'totalQuestions' => 1,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0,
        ];

        $answers = [
            [
                'id' => 1,
                'questionId' => 1,
                'time' => 2,
                'correct' => true,
            ],
        ];

        yield 'result ok 1 correct answers. With 2 attempts' => [
            'data' => ['answers' => $answers,
                'totalQuestions' => 1,
                'timeTotal' => 30,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0.89,
        ];

        $attempts = [
            [
                'id' => 1,
                'questionId' => 1,
                'time' => 5,
                'correct' => true,
            ],
            [
                'id' => 2,
                'questionId' => 2,
                'time' => 2,
                'correct' => true,
            ],
            [
                'id' => 3,
                'questionId' => 1,
                'time' => 5,
                'correct' => true,
            ],
            [
                'id' => 4,
                'questionId' => 2,
                'time' => 2,
                'correct' => true,
            ],
        ];

        yield 'result ok 1 correct answers and less time. With 4 attempts' => [
            'data' => ['answers' => $answers,
                'totalQuestions' => 1,
                'timeTotal' => 10,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0.71,
        ];
    }
}
