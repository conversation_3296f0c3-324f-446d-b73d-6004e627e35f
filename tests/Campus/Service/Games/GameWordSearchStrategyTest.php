<?php

declare(strict_types=1);

namespace App\Tests\Campus\Service\Games;

use App\Campus\Games\GameStrategyInterface;
use App\Campus\Games\WordSearch;
use App\Entity\Chapter;
use App\Entity\ChapterType;
use Doctrine\ORM\EntityManagerInterface;

class GameWordSearchStrategyTest extends TestGameStrategyInterface
{
    public function getStrategy(): GameStrategyInterface
    {
        return new WordSearch($this->createMock(EntityManagerInterface::class));
    }

    public static function getValidData(): \Generator
    {
        $chapter = new Chapter();
        $chapterType = new ChapterType();
        $chapterType->setCode('999');
        $chapterType->setPercentageCompleted('0.75');
        $chapter->setType($chapterType);

        $answers = [
            [
                'questionId' => 1,
                'attempts' => [
                    [
                        'word' => 'asd',
                        'time' => 5,
                        'correct' => false,
                    ],
                    [
                        'word' => 'qwe',
                        'time' => 10,
                        'correct' => false,
                    ],
                    [
                        'word' => 'zxc',
                        'time' => 15,
                        'correct' => false,
                    ],
                ],
            ],
        ];

        $attempts = [
            [
                'questionId' => 1,
                'attempts' => [
                    [
                        'word' => 'asd',
                        'time' => 30,
                        'correct' => false,
                    ],
                ],
            ],
            [
                'questionId' => 2,
                'attempts' => [
                    [
                        'word' => 'qwe',
                        'time' => 30,
                        'correct' => false,
                    ],
                ],
            ],
        ];

        yield 'result ko, no chapter passed' => [
            'data' => ['answers' => $answers,
                'words' => 1,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => null,
            'expectedPoints' => null,
        ];

        yield 'result ko, no total time passed' => [
            'data' => ['answers' => $answers,
                'words' => 1,
                'timeTotal' => null,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0,
        ];

        yield 'result ko, no attempts passed' => [
            'data' => ['answers' => $answers,
                'words' => 1,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => null,
            ],
            'args' => $chapter,
            'expectedPoints' => 0,
        ];

        yield 'result ko, no answers passed' => [
            'data' => ['answers' => null,
                'words' => 1,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0,
        ];

        yield 'result ko No correct answers.' => [
            'data' => ['answers' => $answers,
                'words' => 1,
                'totalQuestions' => 1,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0,
        ];

        $answers = [
            [
                'questionId' => 1,
                'attempts' => [
                    [
                        'word' => 'asd',
                        'time' => 5,
                        'correct' => false,
                    ],
                    [
                        'word' => 'qwe',
                        'time' => 10,
                        'correct' => false,
                    ],
                    [
                        'word' => 'zxc',
                        'time' => 14,
                        'correct' => true,
                    ],
                ],
            ],
        ];

        yield 'result ok with 2 fail. With 2 attempts' => [
            'data' => ['answers' => $answers,
                'words' => 1,
                'totalQuestions' => 1,
                'timeTotal' => 30,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0.75,
        ];

        yield 'result ok with 2 fail and less time. With 2 attempts' => [
            'data' => ['answers' => $answers,
                'words' => 1,
                'totalQuestions' => 1,
                'timeTotal' => 15,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0.69,
        ];
    }
}
