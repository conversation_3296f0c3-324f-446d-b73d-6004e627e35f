<?php

declare(strict_types=1);

namespace App\Tests\Campus\Service\UserCourseChapter;

use App\Campus\Games\GameService;
use App\Campus\Service\UserCourseChapter\UserCourseService;
use App\Entity\UserCourse;
use App\Enum\ChapterContent;
use App\Service\SettingsService;
use App\Tests\Mother\Entity\ChapterMother;
use App\Tests\Mother\Entity\ChapterTypeMother;
use App\Tests\Mother\Entity\ContentMother;
use App\Tests\Mother\Entity\CourseMother;
use App\Tests\Mother\Entity\SeasonMother;
use App\Tests\Mother\Entity\UserCourseMother;
use App\Tests\Mother\Entity\UserMother;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Exception\ORMException;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Security\Core\Security;

class UserCourseServiceTest extends TestCase
{
    private function getUserCourseService(
        ?EntityManagerInterface $em = null,
        ?SettingsService $settings = null,
        ?Security $security = null,
        ?GameService $chapterTypeService = null,
    ): UserCourseService {
        return new UserCourseService(
            em: $em ?? $this->createMock(EntityManagerInterface::class),
            settings: $settings ?? $this->createMock(SettingsService::class),
            security: $security ?? $this->createMock(Security::class),
            chapterTypeService: $chapterTypeService ?? $this->createMock(GameService::class),
        );
    }

    /**
     * @throws \DateMalformedStringException
     * @throws ORMException
     */
    public function testFinishUserCourseNotAllowedToFinish(): void
    {
        $user = UserMother::create(id: 1);
        $course = CourseMother::create();
        $season1 = SeasonMother::create(course: $course);
        $chapterType = ChapterTypeMother::create(id: ChapterContent::CONTENT_TYPE);
        $chapter1 = ChapterMother::create(
            id: 1,
            title: 'Chapter 1',
            active: false,
            type: $chapterType,
            course: $course,
            season: $season1,
        );
        $contentChapter1 = ContentMother::create(
            id: 1,
            chapter: $chapter1,
        );
        $chapter1->addContent($contentChapter1);

        $chapter2 = ChapterMother::create(
            id: 2,
            title: 'Chapter 2',
            position: 2,
            type: $chapterType,
            course: $course,
            season: $season1,
        );

        $contentChapter2 = ContentMother::create(
            id: 2,
            chapter: $chapter2,
        );
        $chapter2->addContent($contentChapter2);

        $chapter3 = ChapterMother::create(
            id: 3,
            title: 'Chapter 3',
            position: 3,
            type: $chapterType,
            course: $course,
            season: $season1,
        );
        $contentChapter3 = ContentMother::create(
            id: 2,
            chapter: $chapter3,
        );
        $chapter3->addContent($contentChapter3);

        $course->addChapter($chapter1);
        $course->addChapter($chapter2);
        $course->addChapter($chapter3);

        $userCourse = UserCourseMother::create(
            id: 1,
            user: $user,
            course: $course,
        );

        $userCourseChapter1 = UserCourseMother::createUserCourseChapter(
            id: 1,
            userCourse: $userCourse,
            chapter: $chapter1,
            startedAt: new \DateTimeImmutable(),
            finishedAt: (new \DateTimeImmutable())->modify('+1 hour'),
            timeSpent: 60,
            points: 10,
        );

        $userCourseChapter2 = UserCourseMother::createUserCourseChapter(
            id: 2,
            userCourse: $userCourse,
            chapter: $chapter2,
            startedAt: (new \DateTimeImmutable())->modify('+2 hour'),
            finishedAt: (new \DateTimeImmutable())->modify('+3 hour'),
            timeSpent: 60,
            points: 10,
        );

        $userCourse->addChapter($userCourseChapter1);
        $userCourse->addChapter($userCourseChapter2);

        $em = $this->createMock(EntityManager::class);
        $em->expects($this->never())
            ->method('persist');

        $service = $this->getUserCourseService(
            em: $em,
        );

        $service->setFinishUserCourse($userCourse);
    }

    /**
     * @throws \DateMalformedStringException
     * @throws ORMException
     */
    public function testFinishCourseOk(): void
    {
        $user = UserMother::create(id: 1);
        $course = CourseMother::create();
        $season1 = SeasonMother::create(course: $course);
        $chapterType = ChapterTypeMother::create(id: ChapterContent::CONTENT_TYPE);
        $chapter1 = ChapterMother::create(
            id: 1,
            title: 'Chapter 1',
            active: false,
            type: $chapterType,
            course: $course,
            season: $season1,
        );
        $contentChapter1 = ContentMother::create(
            id: 1,
            chapter: $chapter1,
        );
        $chapter1->addContent($contentChapter1);

        $chapter2 = ChapterMother::create(
            id: 2,
            title: 'Chapter 2',
            position: 2,
            type: $chapterType,
            course: $course,
            season: $season1,
        );

        $contentChapter2 = ContentMother::create(
            id: 2,
            chapter: $chapter2,
        );
        $chapter2->addContent($contentChapter2);

        $chapter3 = ChapterMother::create(
            id: 3,
            title: 'Chapter 3',
            position: 3,
            type: $chapterType,
            course: $course,
            season: $season1,
        );
        $contentChapter3 = ContentMother::create(
            id: 3,
            chapter: $chapter3,
        );
        $chapter3->addContent($contentChapter3);

        $chapter4 = ChapterMother::create(
            id: 4,
            title: 'Chapter 4',
            position: 4,
            type: $chapterType,
            course: $course,
            season: $season1,
        );
        $contentChapter4 = ContentMother::create(
            id: 4,
            chapter: $chapter4,
        );
        $chapter4->addContent($contentChapter4);

        $course->addChapter($chapter1);
        $course->addChapter($chapter2);
        $course->addChapter($chapter3);
        $course->addChapter($chapter4);

        $userCourse = UserCourseMother::create(
            id: 1,
            user: $user,
            course: $course,
        );

        $userCourseChapter1 = UserCourseMother::createUserCourseChapter(
            id: 1,
            userCourse: $userCourse,
            chapter: $chapter1,
            startedAt: new \DateTimeImmutable(),
            finishedAt: (new \DateTimeImmutable())->modify('+1 hour'),
            timeSpent: 60,
            points: 10,
        );

        $userCourseChapter2 = UserCourseMother::createUserCourseChapter(
            id: 2,
            userCourse: $userCourse,
            chapter: $chapter2,
            startedAt: (new \DateTimeImmutable())->modify('+2 hour'),
            finishedAt: (new \DateTimeImmutable())->modify('+3 hour'),
            timeSpent: 60,
            points: 10,
        );

        $userCourseChapter3 = UserCourseMother::createUserCourseChapter(
            id: 3,
            userCourse: $userCourse,
            chapter: $chapter3,
            startedAt: (new \DateTimeImmutable())->modify('+5 hour'),
            finishedAt: (new \DateTimeImmutable())->modify('+6 hour'),
            timeSpent: 60,
            points: 10,
        );

        $userCourseChapter4 = UserCourseMother::createUserCourseChapter(
            id: 4,
            userCourse: $userCourse,
            chapter: $chapter4,
            startedAt: (new \DateTimeImmutable())->modify('+5 hour'),
            finishedAt: (new \DateTimeImmutable())->modify('+6 hour'),
            timeSpent: 60,
            points: 10,
        );

        $userCourse->addChapter($userCourseChapter1);
        $userCourse->addChapter($userCourseChapter2);
        $userCourse->addChapter($userCourseChapter3);
        $userCourse->addChapter($userCourseChapter4);

        $em = $this->createMock(EntityManager::class);
        $em->expects($this->once())
            ->method('persist')
            ->willReturnCallback(function (UserCourse $param) use ($userCourse) {
                $this->assertNotNull($param->getFinishedAt());
                $this->assertEquals($param->getId(), $userCourse->getId());
                $this->assertEquals($param->getCourse()->getId(), $userCourse->getCourse()->getId());
                $this->assertEquals($param->getUser()->getId(), $userCourse->getUser()->getId());
            });

        $settings = $this->createMock(SettingsService::class);
        $settings->expects($this->once())->method('get')
            ->with('app.opinions.platform')
            ->willReturn(true);

        $service = $this->getUserCourseService(
            em: $em,
            settings: $settings,
        );

        $service->setFinishUserCourse($userCourse);
    }

    /**
     * @throws \DateMalformedStringException
     * @throws ORMException
     */
    public function testFinishCourseChaptersNotCompleted(): void
    {
        $user = UserMother::create(id: 1);
        $course = CourseMother::create();
        $season1 = SeasonMother::create(course: $course);
        $chapterType = ChapterTypeMother::create(id: ChapterContent::CONTENT_TYPE);
        $chapter1 = ChapterMother::create(
            id: 1,
            title: 'Chapter 1',
            active: false,
            type: $chapterType,
            course: $course,
            season: $season1,
        );
        $contentChapter1 = ContentMother::create(
            id: 1,
            chapter: $chapter1,
        );
        $chapter1->addContent($contentChapter1);

        $chapter2 = ChapterMother::create(
            id: 2,
            title: 'Chapter 2',
            position: 2,
            type: $chapterType,
            course: $course,
            season: $season1,
        );

        $contentChapter2 = ContentMother::create(
            id: 2,
            chapter: $chapter2,
        );
        $chapter2->addContent($contentChapter2);

        $chapter3 = ChapterMother::create(
            id: 3,
            title: 'Chapter 3',
            position: 3,
            type: $chapterType,
            course: $course,
            season: $season1,
        );
        $contentChapter3 = ContentMother::create(
            id: 2,
            chapter: $chapter3,
        );
        $chapter3->addContent($contentChapter3);

        $chapter4 = ChapterMother::create(
            id: 4,
            title: 'Chapter 4',
            position: 3,
            type: $chapterType,
            course: $course,
            season: $season1,
        );

        $contentChapter4 = ContentMother::create(
            id: 3,
            chapter: $chapter4,
        );
        $chapter4->addContent($contentChapter4);

        $course->addChapter($chapter1);
        $course->addChapter($chapter2);
        $course->addChapter($chapter3);
        $course->addChapter($chapter4);

        $userCourse = UserCourseMother::create(
            id: 1,
            user: $user,
            course: $course,
        );

        $userCourseChapter1 = UserCourseMother::createUserCourseChapter(
            id: 1,
            userCourse: $userCourse,
            chapter: $chapter1,
            startedAt: new \DateTimeImmutable(),
            finishedAt: (new \DateTimeImmutable())->modify('+1 hour'),
            timeSpent: 60,
            points: 10,
        );

        $userCourseChapter2 = UserCourseMother::createUserCourseChapter(
            id: 2,
            userCourse: $userCourse,
            chapter: $chapter2,
            startedAt: (new \DateTimeImmutable())->modify('+2 hour'),
            finishedAt: (new \DateTimeImmutable())->modify('+3 hour'),
            timeSpent: 60,
            points: 10,
        );

        $userCourseChapter3 = UserCourseMother::createUserCourseChapter(
            id: 3,
            userCourse: $userCourse,
            chapter: $chapter3,
            startedAt: (new \DateTimeImmutable())->modify('+5 hour'),
            finishedAt: (new \DateTimeImmutable())->modify('+6 hour'),
            timeSpent: 60,
            points: 10,
        );

        $userCourseChapter4 = UserCourseMother::createUserCourseChapter(
            id: 4,
            userCourse: $userCourse,
            chapter: $chapter4,
            startedAt: (new \DateTimeImmutable())->modify('+6 hour'),
            timeSpent: 60,
            points: 10,
        );

        $userCourse->addChapter($userCourseChapter1);
        $userCourse->addChapter($userCourseChapter2);
        $userCourse->addChapter($userCourseChapter3);
        $userCourse->addChapter($userCourseChapter4);

        $em = $this->createMock(EntityManager::class);
        $em->expects($this->never())
            ->method('persist');

        $service = $this->getUserCourseService(
            em: $em,
        );

        $service->setFinishUserCourse($userCourse);
    }

    /**
     * @throws \DateMalformedStringException
     * @throws ORMException
     */
    public function testSetFinishUserCourseWithChapterInactive()
    {
        $user = UserMother::create(id: 1);
        $course = CourseMother::create();
        $season1 = SeasonMother::create(course: $course);
        $chapterType1 = ChapterTypeMother::create(id: ChapterContent::CONTENT_TYPE);
        $chapterType2 = ChapterTypeMother::create(id: ChapterContent::PDF_TYPE, active: false);
        $chapter1 = ChapterMother::create(
            id: 1,
            title: 'Chapter 1',
            active: false,
            type: $chapterType1,
            course: $course,
            season: $season1,
        );
        $contentChapter1 = ContentMother::create(
            id: 1,
            chapter: $chapter1,
        );
        $chapter1->addContent($contentChapter1);

        $chapter2 = ChapterMother::create(
            id: 2,
            title: 'Chapter 2',
            active: false,
            type: $chapterType2,
            course: $course,
            season: $season1,
        );

        $course->addChapter($chapter1);
        $course->addChapter($chapter2);

        $userCourse = UserCourseMother::create(
            id: 1,
            user: $user,
            course: $course,
        );

        $userCourseChapter1 = UserCourseMother::createUserCourseChapter(
            id: 1,
            userCourse: $userCourse,
            chapter: $chapter1,
            startedAt: new \DateTimeImmutable(),
            finishedAt: (new \DateTimeImmutable())->modify('+1 hour'),
            timeSpent: 60,
            points: 10,
        );

        $userCourseChapter2 = UserCourseMother::createUserCourseChapter(
            id: 2,
            userCourse: $userCourse,
            chapter: $chapter2,
            startedAt: (new \DateTimeImmutable())->modify('+2 hour'),
            finishedAt: (new \DateTimeImmutable())->modify('+3 hour'),
            timeSpent: 60,
            points: 10,
        );

        $userCourse->addChapter($userCourseChapter1);
        $userCourse->addChapter($userCourseChapter2);

        $em = $this->createMock(EntityManager::class);
        $em->expects($this->atLeastOnce())
            ->method('persist');

        $service = $this->getUserCourseService(
            em: $em,
        );

        $service->setFinishUserCourse($userCourse);
    }

    public static function provideTestCalculatePointsUserCourse(): \Generator
    {
        $typeContent = ChapterTypeMother::create(id: ChapterContent::CONTENT_TYPE, type: 'content');
        $user = UserMother::create(id: 1);
        $course = CourseMother::create(id: 1);
        $season = SeasonMother::create(id: 1, course: $course);
        $userCourse = UserCourseMother::create(id: 1, user: $user, course: $course);
        yield 'no chapter no points' => [
            'userCourse' => UserCourseMother::create(id: 1, user: $user, course: $course),
            'expectedPoints' => 0,
        ];

        $chapter1 = ChapterMother::create(id: 1, type: $typeContent, course: $course, season: $season);
        $chapter1->addContent(ContentMother::create(id: 1));
        $course->addChapter($chapter1);

        yield 'one chapter 10 points' => [
            'userCourse' => UserCourseMother::create(id: 1, user: $user, course: $course)
                ->addChapter(
                    UserCourseMother::createUserCourseChapter(
                        id: 1,
                        userCourse: $userCourse,
                        chapter: $chapter1,
                        points: 10,
                    )
                ),
            'expectedPoints' => 10,
        ];

        $chapter2 = ChapterMother::create(id: 2, active: false, type: $typeContent, course: $course, season: $season);
        $chapter2->addContent(ContentMother::create(id: 2));
        $course->addChapter($chapter2);

        yield '1 active chapter 1 inactive chapter 10 points' => [
            'userCourse' => UserCourseMother::create(id: 1, user: $user, course: $course)
                ->addChapter(
                    UserCourseMother::createUserCourseChapter(
                        id: 1,
                        userCourse: $userCourse,
                        chapter: $chapter1,
                        points: 10,
                    )
                )
                ->addChapter(
                    UserCourseMother::createUserCourseChapter(
                        id: 2,
                        userCourse: $userCourse,
                        chapter: $chapter2,
                        points: 50,
                    )
                ),
            'expectedPoints' => 10,
        ];

        $inactiveType = ChapterTypeMother::create(id: ChapterContent::ROLEPLAY_TYPE, active: false);
        $chapter3 = ChapterMother::create(id: 3, type: $inactiveType, course: $course, season: $season);

        yield '1 active chapter, 1 inactive chapter, 1 active chapter with type deactivated and 10 points' => [
            'userCourse' => UserCourseMother::create(id: 1, user: $user, course: $course)
                ->addChapter(
                    UserCourseMother::createUserCourseChapter(
                        id: 1,
                        userCourse: $userCourse,
                        chapter: $chapter1,
                        points: 10,
                    )
                )
                ->addChapter(
                    UserCourseMother::createUserCourseChapter(
                        id: 2,
                        userCourse: $userCourse,
                        chapter: $chapter2,
                        points: 50,
                    )
                )->addChapter(
                    UserCourseMother::createUserCourseChapter(
                        id: 3,
                        userCourse: $userCourse,
                        chapter: $chapter3,
                        points: 100,
                    )
                ),
            'expectedPoints' => 10,
        ];

        $chapter4 = ChapterMother::create(id: 4, type: $typeContent, course: $course, season: $season);

        yield '1 active chapter, 1 inactive chapter, 1 active chapter with type deactivated, 1 active chapter
         with no content and 10 points' => [
            'userCourse' => UserCourseMother::create(id: 1, user: $user, course: $course)
                ->addChapter(
                    UserCourseMother::createUserCourseChapter(
                        id: 1,
                        userCourse: $userCourse,
                        chapter: $chapter1,
                        points: 10,
                    )
                )
                ->addChapter(
                    UserCourseMother::createUserCourseChapter(
                        id: 2,
                        userCourse: $userCourse,
                        chapter: $chapter2,
                        points: 50,
                    )
                )->addChapter(
                    UserCourseMother::createUserCourseChapter(
                        id: 3,
                        userCourse: $userCourse,
                        chapter: $chapter3,
                        points: 100,
                    )
                )->addChapter(
                    UserCourseMother::createUserCourseChapter(
                        id: 3,
                        userCourse: $userCourse,
                        chapter: $chapter4,
                        points: 100,
                    )
                ),
            'expectedPoints' => 10,
        ];
    }

    /**
     * @dataProvider provideTestCalculatePointsUserCourse
     *
     * @throws ORMException
     * @throws Exception
     */
    public function testCalculatePointsUserCourse(
        UserCourse $userCourse,
        int|float $expectedPoints = 0
    ): void {
        $em = $this->createMock(EntityManager::class);
        $em->expects($this->atLeastOnce())
            ->method('persist');
        $service = $this->getUserCourseService(em: $em);
        $service->calculatePointsUserCourse($userCourse);

        $this->assertEquals($expectedPoints, $userCourse->getPoints());
    }
}
