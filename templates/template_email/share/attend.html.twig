<div style="background: hsl(198, 99%, 85%); padding: 1rem">
  <h4>{{ 'email.template_email.question_assitance'|trans({}, 'email', locale) }}</h4>
  <div class="btns">
    <a class="button btn-no" href="{{ app.request.getSchemeAndHttpHost() }}{{ path('register-assistance-announcement', { idUserHash: idUserHash, confirmation: 'no' }) }}">{{ 'email.template_email.question_assitance_no'|trans({}, 'email', locale) }}</a>

    <a class="button btn-yes" href="{{ app.request.getSchemeAndHttpHost() }}{{ path('register-assistance-announcement', { idUserHash: idUserHash, confirmation: 'yes' }) }}">{{ 'email.template_email.question_assitance_yes'|trans({}, 'email', locale) }}</a>
  </div>
</div>
