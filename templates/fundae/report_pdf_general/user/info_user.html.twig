<table width="100%">
  <tbody>
    <tr>
      <td>
        <strong>DNI :</strong>
      </td>
      <td>
        {% if announcementUser.user.registerKey is defined and announcementUser.user.registerKey is not null %}
          {{ announcementUser.user.registerKey }}
        {% else %}
          -
        {% endif %}
      </td>
    </tr>
    <tr>
      <td>
        <strong>{{ 'user.configureFields.email'|trans({}, 'messages', app.user.locale) }}:</strong>
      </td>
      <td>{{ announcementUser.user.email }}</td>
    </tr>

    <tr>
      <td>
        <strong>{{ 'announcements.configureFields.start_at'|trans({}, 'messages', app.user.locale) }}:</strong>
      </td>
      <td>
        {% if userCourses[announcementUser.user.id].startedAt is defined and userCourses[announcementUser.user.id].startedAt is not null %}
          {{ userCourses[announcementUser.user.id].startedAt|date('Y-m-d H:i') }}
        {% else %}
          -
        {% endif %}
      </td>
    </tr>
    <tr>
      <td>
        <strong>{{ 'announcements.configureFields.finish_at'|trans({}, 'messages', app.user.locale) }} :</strong>
      </td>
      <td>
        {% if userCourses[announcementUser.user.id].finishedAt is defined and userCourses[announcementUser.user.id].finishedAt is not null %}
          {{ userCourses[announcementUser.user.id].finishedAt|date('Y-m-d H:i') }}
        {% else %}
          -
        {% endif %}
      </td>
    </tr>

    <tr>
      <td>
        <strong>{{ 'user.configureFields.finished'|trans({}, 'messages', app.user.locale) }} :</strong>
      </td>
      <td class="text-center">
        {% if userCourses[announcementUser.user.id].finishedAt is defined and userCourses[announcementUser.user.id].finishedAt is not null %}
          {{ 'Yes'|trans({}, 'messages', app.user.locale) }}
        {% else %}
          {{ 'No'|trans({}, 'messages', app.user.locale) }}
        {% endif %}
      </td>
      <td></td>
    </tr>

    <tr>
      <td>
        <strong>{{ 'user.configureFields.time_spent'|trans({}, 'messages', app.user.locale) }}:</strong>
      </td>
      <td>
        {% if userCourses[announcementUser.user.id].id is defined %}
          {{ informationExtraUser[announcementUser.user.id].timeSpent }}
        {% else %}
          -
        {% endif %}
      </td>
    </tr>
    <tr>
      <td>
        <strong>{{ 'user.configureFields.content_viewed'|trans({}, 'messages', app.user.locale) }}:</strong>
      </td>
      <td>
        {% if userCourses[announcementUser.user.id].id is defined %}
          {{ informationExtraUser[announcementUser.user.id].progressTotalCourse }}%
        {% else %}
          -
        {% endif %}
      </td>
    </tr>

    <tr>
      <td>
        <strong>Horas completadas :</strong>
      </td>
      <td>
        {% if userCourses[announcementUser.user.id].id is defined %}
          {{ informationExtraUser[announcementUser.user.id].progressTotalHour }}%
        {% else %}
          -
        {% endif %}
      </td>
    </tr>
    <tr>
      <td>
        <strong>Diploma:</strong>
      </td>
      <td>
        {% if userCourses[announcementUser.user.id].id is defined %}
          {% if informationExtraUser[announcementUser.user.id].downloadDiploma %}
            {{ 'Yes'|trans({}, 'messages', app.user.locale) }}
          {% else %}
            {{ 'No'|trans({}, 'messages', app.user.locale) }}
          {% endif %}
        {% else %}
          -
        {% endif %}
      </td>
    </tr>

    <tr>
      <td>
        <strong>{{ 'user.configureFields.interaction_with_teacher'|trans({}, 'messages', app.user.locale) }} :</strong>
      </td>
      <td>
        {% if userCourses[announcementUser.user.id].id is defined %}
          {{ messages|length }}
        {% else %}
          -
        {% endif %}
      </td>
      <td>
        <strong>{{ 'user.configureFields.interaction_in_forum'|trans({}, 'messages', app.user.locale) }} :</strong>
      </td>
      {# <td>
            {% if userCourses[announcementUser.user.id].id is defined %}
                {{ forumPosts | length}}
            {% else %}
                -
            {% endif %}
        </td> #}
    </tr>
    <tr>
      <td>
        <strong>{% trans %}Apto{% endtrans %}:</strong>
      </td>
      <td>
        {% if userCourses[announcementUser.user.id].finishedAt is defined and userCourses[announcementUser.user.id].finishedAt is not null %}
          {{ 'Yes'|trans({}, 'messages', app.user.locale) }}
        {% else %}
          {{ 'No'|trans({}, 'messages', app.user.locale) }}
        {% endif %}
      </td>
    </tr>
  </tbody>
</table>
