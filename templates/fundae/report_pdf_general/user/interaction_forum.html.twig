<div class=" course-contents">
    <h3>{{'user.configureFields.interaction_in_forum'|trans({}, 'messages',  app.user.locale) }}</h3>
    <table class="table datagrid with-rounded-top" width="100%">
        <thead class="thead-light">
        <tr>
            <th><span>{% trans %}Date{% endtrans %}</span></th>
            <th><span>{{'forum.configureFields.message'|trans({}, 'messages',  app.user.locale) }}</span></th>
        </tr>
        </thead>

        <tbody>
        {% for forumPost in forumPosts[announcementUser.user.id] %}
            <tr>
                <td>
                    {{ forumPost.createdAt | date('Y-m-d H:i') }}
                </td>
                <td>
                    {{ forumPost.message }}
                </td>
            </tr>
        {% endfor %}
        </tbody>
    </table>
</div>