<div class="course-contents">
  <h2>{{ 'taskCourse.labelInPlural'|trans({}, 'messages') }}</h2>
  <table class="table datagrid with-rounded-top" width="100%">
    <thead class="thead-light">
      <tr>
        <th>{{ 'taskCourse.labelInSingular'|trans({}, 'messages') }}</th>
        <th>
          <span>{{ 'user.configureFields.date'|trans({}, 'messages') }}</span>
        </th>
        <th>
          <span>{{ 'state'|trans({}, 'messages') }}</span>
        </th>
      </tr>
    </thead>

    <tbody>
      {% for task in taskUser %}
        <tr>
          <td>{{ task.title|raw }}</td>
          <td>{{ task.updatedAt|date('d-m-Y H:i') }}</td>
          <td>
            {% set state = 'taskCourse.configureFields.state_' ~ task.state %}
            {{ state|trans({}, 'messages') }}
          </td>
        </tr>
      {% endfor %}
    </tbody>
  </table>
</div>
