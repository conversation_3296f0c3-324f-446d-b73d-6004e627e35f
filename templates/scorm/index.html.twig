{% extends 'scorm/base.html.twig' %}

{% macro menu_links(menu, folder) %}
    <ul>
        {% for link in menu.submenu %}
            {% if link.href != '' %}
                <li class="link"><a class="frmOpnr mnlvl" href="" data-dest="{{ folder }}/{{ link.href }}">{{ link.title }}</a>
            {% else %}
                <li>{{ link.title }}
            {% endif %}
            {% if link.submenu %}
                {{ _self.menu_links(link, folder) }}
            {% endif %}
            </li>
        {% endfor %}
    </ul>
{% endmacro %}

{% block title %}Hello ScormController!{% endblock %}

{% block body %}
<div id="scorm-player" class="row h-100 m-0" data-chapter="{{ chapter.id }}" data-image="{{ chapter.image }}">
    {% if scorm.showMenu %}
    <div id="show-menu" class="d-none">
        <i class="fa fa-angle-right"></i>
    </div>
    {% endif %}
    <div id="navigation" class="col-sm-12 col-md-3 {% if not scorm.showMenu %}d-none{% endif %}">
        <div id="scorm-menu">
            {% if scorm is defined %}
                {%  for menu in scorm.menu %}
                    <div class="card m-2 shadow-sm">
                        <div class="card-header">
                            {{ menu.title }} <i id="hide-menu" class="fa fa-bars"></i>
                        </div>
                        <div class="card-body d-none d-md-block">
                            {{ _self.menu_links(menu, scorm.folder) }}
                        </div>
                    </div>
                {% endfor %}
            {% endif %}
        </div>

        <div id="arrow-menu" class="card m-2">
            <div class="card-footer">
                <div class="row">
                    <div class="col-4 text-left"><i class="fa fa-arrow-circle-left prev"></i></div>
                    <div class="col-4"></div>
                    <div class="col-4 text-right"><i class="fa fa-arrow-circle-right next"></i></div>
                </div>
            </div>
        </div>
{#        <div class="card m-2 d-none d-md-block">#}
{#            <div class="card-header">#}
{#                Scorm Log#}
{#            </div>#}
{#            <div id="infolog" class="card-body">#}

{#            </div>#}
{#        </div>#}
    </div>
{#    <div class="col-sm-12 col-md-9 h-100 overflow-hidden">#}
    <div id="scorm-load-panel" class="col-12 h-100 overflow-hidden {% if scorm.showMenu %}col-md-9{% endif %} p-0">
        <div class="mb-2 h-100 ">
            <iframe id="scorm-wrapper" class="w-100 h-100 border-0" src="">

            </iframe>
        </div>
    </div>
</div>
<style>
    #scorm-wrapper{
    width: 100%;
    height: 100%;
    background-color: black;
    background-size: cover;
    background-repeat: no-repeat;
    }
</style>
<script>
    {% if token is not null %}    
     localStorage.setItem('token', '{{ token }}');    
    {%  else %}
    localStorage.setItem('token', localStorage.getItem('user-token'));
    {% endif %}
</script> 
{% endblock %}