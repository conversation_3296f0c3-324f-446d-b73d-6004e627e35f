{% block head_stylesheets %}
  {{ encore_entry_link_tags('diploma') }}
{% endblock %}
<link href="{{ asset('build/diploma.css') }}" rel="stylesheet">

<div class="fondo" style="background-image: url('{{ assets_dir }}/diploma/easylearning/footer_diploma.svg');">
  <div class="head">
    <img src="{{ assets_dir }}/diploma/easylearning/logo_diploma.png" width="16rem" class="logoDiploma" />
{#    <img src="./assets/diploma/logo_diploma.png" width="16rem" class="logoDiploma" />#}
    <p class="textDiploma">{{ 'message_api.diploma.diploma'|trans({}, 'message_api', locale) }}</p>
  </div>

  <div class="bodyDiploma">
    <p class="concedido">{{ 'message_api.diploma.granted'|trans({}, 'message_api', locale) }}:</p>
    <p class="profile-name">
      <strong>{{ user.firstName }} {{ user.lastName }}</strong>
    </p>

 

    <p class="profile-supered">{{ 'message_api.diploma.supered'|trans({}, 'message_api', locale) }}</p>
    <p class="profile-course">
      <strong>{{ course }}</strong>
    </p>

    <p class="profile-date">{{ 'message_api.diploma.date'|trans({}, 'message_api', locale) }}: {{ date }}</p>
    {% if showDuration and courseDurationHours %}
    <p class="profile-duration">{{ 'message.api.diploma.duration'|trans({}, 'message_api', locale) }}: {{ courseDurationHours|number_format(1) }} {{ 'message_api.diploma.hours_unit'|trans({}, 'message_api', locale) }}</p>
    {% endif %}
  </div>
</div>


