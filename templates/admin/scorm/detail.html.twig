{% block head_stylesheets %}
    {{ encore_entry_link_tags('scormPackage') }}
{% endblock %}

{% macro menu_links(menu) %}
    <ul>
        {% for link in menu.submenu %}
            {% if link.href != '' %}
                <li class="link">{{ link.title }}
            {% else %}
                <li>{{ link.title }}
            {% endif %}
            {% if link.submenu %}
                {{ _self.menu_links(link) }}
            {% endif %}
            </li>
        {% endfor %}
    </ul>
{% endmacro %}

<div class="d-flex flex-row justify-content-between align-content-center w-100 pb-3">
    <div class="content-header-title">
        <h1 class="title">
            {{ 'message_api.scorm.title_package'|trans({}, 'message_api',  app.user.locale) }}
        </h1>
    </div>
    <input type="hidden" name="referrer" id="referrerInput" value="{{ app.request.uri }}" />
    {% if not scorm %}
    <div class="page-actions">
   
       {#  <a class=" action-new btn btn-primary" href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\ScormCrudController').setAction('new').set('chapterId', chapter.id).set('referrer', referrer) }}"> {{ 'message_api.scorm.button_add_scorm'|trans({}, 'message_api',  app.user.locale) }}</a> #}
        <button type="button" class="btn btn-primary"  data-bs-toggle="modal" data-bs-target="#modal-chapter-{{ chapter.type.id }}"> {{ 'message_api.scorm.button_add_scorm'|trans({}, 'message_api',  app.user.locale) }} </button>

    </div>
      {%  else %}
       <div class="page-actions">
         <a class="action-delete btn btn-danger" href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\ScormCrudController').setAction('delete').setEntityId(scorm.id).set('referrer', referrerChapter) }}" formaction="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\ScormCrudController').setAction('delete').setEntityId(scorm.id).set('referrer', referrerChapter) }}" data-bs-toggle="modal" data-bs-target="#modal-delete"><i class="fa fa-trash"></i> {{ 'message_api.scorm.button_delete_scorm'|trans({}, 'message_api',  app.user.locale) }}</a>
           <a class="btn btn-primary" href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\ScormCrudController').setAction('downloadScorm').setEntityId(scorm.id).set('referrer', referrerChapter) }}"><i class="fa fa-download"></i> {{ 'material_course.download'|trans({}, 'messages',  app.user.locale) }}</a>
  </div>
    {% endif %}
</div>

{% if scorm %}
    <table class="table datagrid with-rounded-top">
    <thead>

    <tr>
        <th><span></span></th>
        <th><span></span></th>
    </tr>

    </thead>

      <tbody >
       {% for menu in scorm.menu %}
           <tr class="col-md-12 col-lg-12" >
               <td colspan="2"  class="col-md-8 col-lg-8">
                   <strong>Menu: </strong> {{menu.title}}
               </td>
               <td colspan="1" class="col-md-4 col-lg-4">
                   <div class="form-check form-switch float-right">
                       <input type="checkbox" class="form-check-input" id="forceMenuVisualization" data-scorm="{{ scorm.id }}" {% if scorm.showMenu %}checked{% endif %}>
                       <label class="form-check-label" for="forceMenuVisualization" >{{ 'message_api.forcingdisplaymenu'|trans({}, 'translations',  app.user.locale) }}</label>
                   </div>
               </td>
           </tr>
            <tr class="col-md-12 col-lg-12" >
                <td  colspan="3">
                    {{ _self.menu_links(menu) }}
                </td>
            </tr>
        {% endfor %}
            <tr>
               <td>
                   <strong>{{ 'message_api.scorm.config_label'|trans({}, 'translations',  app.user.locale) }}</strong>
               </td>
            </tr> 
             <!--
            <tr>
                <td>
                  {{ 'message_api.scorm.config_desc'|trans({}, 'translations',  app.user.locale) }}
               </td>
            </tr>
            
            <tr lass="col-md-12 col-lg-12" >
                <td>
                   <div class="form-check form-switch ">
                       <input type="checkbox" class="form-check-input" id="configFlagView" data-scorm="{{ scorm.id }}" {% if scorm.showMenu %}checked{% endif %}>
                       <label class="form-check-label" for="configFlagView" >Ve la actividad</label>
                   </div>
               </td>
            </tr>
            -->
            <tr class="col-md-12 col-lg-12" >
               <td class="col-md-9 col-lg-9" colspan="3" >
                <div>
                    {{ 'message_api.scorm.score_system_desc'|trans({}, 'translations',  app.user.locale) }}                  
                </div>
               </td>              
            </tr>
            <tr class="col-md-12 col-lg-12" >
                <td class="col-md-4 col-lg-4">
                   <div class="form-check form-switch ">
                       <input type="checkbox" class="form-check-input" id="configFlagScore" data-scorm="{{ scorm.id }}" {% if scorm.getRawScore() %}checked{% endif %}>
                       <label class="form-check-label" for="configFlagScore" >{{ 'message_api.scorm.score_system_label'|trans({}, 'translations',  app.user.locale) }}</label>
                   </div>
               </td>
                <td class="col-md-4 col-lg-4">
                    <div class="form-range float-right " >
                        <input type="range" class="form-range" data-scorm="{{ scorm.id }}" id="minScoreRequired" value="{{scorm.getRawScore()}}" min="0" max="100" 
                        style="{% if scorm.getRawScore() == null %}display: none{% endif %};" >   
                    </div>
               </td>
                <td class="col-md-4 col-lg-4">
                    <div class="form ">
                        <label class="form-label text-end" id="minScoreCurrentValue" for="minScoreRequired">{{scorm.getRawScore()}}</label> 
                    </div>
                </td>
            </tr>
             <!--
            <tr>
                <td>
                   <div class="form-check form-switch ">
                       <input type="checkbox" class="form-check-input" id="configFlagPassed" data-scorm="{{ scorm.id }}" {% if scorm.showMenu %}checked{% endif %}>
                       <label class="form-check-label" for="configFlagPassed" >Pasado</label>
                   </div>
               </td>
            </tr>
            
            <tr>
                <td>
                   <div class="form-check form-switch ">
                       <input type="checkbox" class="form-check-input" id="configFlagCompleted" data-scorm="{{ scorm.id }}" {% if scorm.showMenu %}checked{% endif %}>
                       <label class="form-check-label" for="configFlagCompleted" >Completado</label>
                   </div>
               </td>
            </tr>
            <tr>
                <td >
                    <div class="form">
                    <label class="form-label text-end" >{{ 'message_api.scorm.score_system_label'|trans({}, 'translations',  app.user.locale) }}</label> 
                    </div>
                </td>
                <td >
                    <div class="form-range float-right">
                        <input type="range" class="form-range" data-scorm="{{ scorm.id }}" id="minScoreRequired" value="{{scorm.getRawScore()}}" min="0" max="100">   
                    </div>
               </td>
                <td >
                    <div class="form">
                        <label class="form-label text-end" id="minScoreCurrentValue" for="minScoreRequired">{{scorm.getRawScore()}}</label> 
                    </div>
                </td>                
           </tr>
             -->
           <!-- 
           <tr>
               <td>
               {{ 'message_api.scorm.allow_reset_desc'|trans({}, 'translations',  app.user.locale) }}                
               </td>
            
               <td>
                   <div class="form-check form-switch float-right">
                       <input type="checkbox" class="form-check-input" id="allowResetAfterCompleted" data-scorm="{{ scorm.id }}" {% if scorm.getAllowReset() %}checked{% endif %}>                       
                       <label class="form-check-label" for="allowResetAfterCompleted" >{{ 'message_api.scorm.allow_reset_label'|trans({}, 'translations',  app.user.locale) }}</label>
                   </div>
               </td>            
           </tr>
              -->
        </tbody>
    </table>


{% else %}
    No
{% endif %}

{% include 'admin/scorm/modal.html.twig' %}

{% block body_javascript %}
    {{ encore_entry_script_tags('scormPackage') }}
    {{ encore_entry_script_tags('scormDetail') }}
{% endblock %}
