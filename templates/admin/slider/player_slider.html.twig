{% block head_stylesheets %}       
    {{ encore_entry_link_tags('visorImage') }}
{% endblock %}
 
<style>
    body{
        margin:0px;
        background: #ffff; 
        overflow:hidden;      
    }

.not-content{
    display: flex;
    justify-content: center;
    aling-items: center;
    margin: auto;
    color: white;
    align-items: center;
    flex-direction: column;    
    height: 100%;

}
</style>

{% if user != null %}
{% if slider %} 

<div id="visor-image">
     <visor-image :chapter="{{chapter.id}}"> 
 
    </visor-image> 
    
</div>
{% else %}
<div class="not-content">   
    <h1>{{ 'messages.notcontainchapter'|trans({}, 'messages',  app.user.locale) }}</h1>
</div>

  {% endif %}

  {% else %}
        <p>{{ 'messages.nologinuser'|trans({}, 'messages',  app.user.locale) }}</p>
{%  endif %}

 {% block body_javascript %}
    
    {{ encore_entry_script_tags('visorImage') }}
{% endblock %} 

<script>
        localStorage.setItem('token', '{{ token }}');
</script>