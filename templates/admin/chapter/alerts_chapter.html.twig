{% if chapter.getPdf == null and chapter.type == 'Pdf' %}
  <div>
    <div class="alert alert-warning alert-dismissible fade show text-dark alert-question" role="alert">
      <strong class=""></strong> <img src="{{ asset('assets/chapters/advertencia.svg') }}" />
      {{ 'message_api.alert.pdf'|trans({}, 'message_api', app.user.locale) }}
      <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
    </div>
  </div>
{% endif %}

{% if chapter.getVideo == null and chapter.type == 'Vídeo' %}
  <div>
    <div class="alert alert-warning alert-dismissible fade show text-dark alert-question" role="alert">
      <strong class=""></strong> <img src="{{ asset('assets/chapters/advertencia.svg') }}" />
      {{ 'message_api.alert.video'|trans({}, 'message_api', app.user.locale) }}
      <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
    </div>
  </div>
{% endif %}

{% if chapter.type == 'Slider' and chapter.hasContentCompleted == false %}
  <div>
    <div class="alert alert-warning alert-dismissible fade show text-dark alert-question" role="alert">
      <strong class=""></strong> <img src="{{ asset('assets/chapters/advertencia.svg') }}" />
      {{ 'message_api.alert.slider'|trans({ '%number%': chapter.type.getMinimiunImageSlider }, 'message_api', app.user.locale)|raw }}
      <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
    </div>
  </div>
{% endif %}

{% if chapter.getScorm == null and chapter.type == 'Scorm' %}
  <div>
    <div class="alert alert-warning alert-dismissible fade show text-dark alert-question" role="alert">
      <strong class=""></strong> <img src="{{ asset('assets/chapters/advertencia.svg') }}" />
      {{ 'message_api.alert.scorm'|trans({}, 'message_api', app.user.locale) }}
      <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
    </div>
  </div>
{% endif %}

{% if chapter.type == 'Contents' %}
  <div id="chapter-content-alert" {% if chapter.hasContentCompleted == true %}style="display: none"{% endif %}>
    <div class="alert alert-warning alert-dismissible fade show text-dark alert-question" role="alert">
      <strong class=""></strong> <img src="{{ asset('assets/chapters/advertencia.svg') }}" />
      {{ 'message_api.alert.chapter_content'|trans({ '%number%': chapter.type.getMinimiunImageSlider }, 'message_api', app.user.locale)|raw }}
      <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
      <div></div>
    </div>
  </div>
{% endif %}