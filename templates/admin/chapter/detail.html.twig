{% extends '@!EasyAdmin/crud/detail.html.twig' %}

{% block content_title %}
    <a href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\CourseCrudController').setAction('detail').setEntityId(chapter.course.id) }}" class="card-link">{{ chapter.course.name }}</a> > {{ chapter.title }}
{% endblock content_title %}


{% block main %}
    <div class="content-panel p-2">

        <div class="card mb-3">
            <div class="row no-gutters">
                <div class="col-md-4">

                {% if chapter.image %}
                    <a href="#" class="ea-lightbox-thumbnail" data-featherlight="#ea-lightbox-{{ chapter.id }}" data-featherlight-close-on-click="anywhere">
                        <img src="{{ chapter_uploads_path }}/{{ chapter.image }}" class="card-img" alt="{{ chapter.title }} image">
                    </a>
                {% else %}
                    <img src="{{ asset('assets/chapters/default-image.svg') }}" class="card-img" alt="{{ chapter.title }} image">
                {% endif %}

                    <div id="ea-lightbox-{{ chapter.id }}" class="ea-lightbox">
                        <img src="{{ chapter_uploads_path }}/{{ chapter.image }}">
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="card-body">
                        <h5 class="card-title">{{ chapter.title }}</h5>
                        {{ chapter.description|raw }}

                        <p class="card-text">{{ 'chapter.configureFields.season'|trans({}, 'chapters',  app.user.locale) }} : {{ chapter.season }}</p>

                        <p class="card-text"><small class="text-muted">{{ 'common_areas.created_by'|trans({}, 'messages',  app.user.locale) }} {{ creator }} at {{ chapter.createdAt|date('Y-m-d H:i:s') }}</small></p>


                    </div>

                         {% if chapter.hasMinimumQuestion == false %}
                     <div>
                        <div class="alert alert-danger alert-dismissible fade show text-dark alert-question" role="alert">
                            <strong class=""></strong> <img src="{{ asset('assets/chapters/advertencia.svg') }}">
                             {{ 'message_api.alert.minimal_question'|trans({'%number%': chapter.getMinimumQuestions}, 'message_api', app.user.locale)|raw }}

                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                     </div>
                 {% endif %}


                    {% if  chapter.getPuzzle == null and  chapter.type == 'Puzzle' %}
                        <div>
                        <div class="alert alert-danger alert-dismissible fade show text-dark alert-question" role="alert">
                            <strong class=""></strong> <img src="{{ asset('assets/chapters/advertencia.svg') }}">
                                    {{ 'message_api.alert.image_puzzle'|trans({}, 'message_api',  app.user.locale) }}
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                     </div>
                     {% endif %}

                       {% if  chapter.getPdf == null and  chapter.type == 'Pdf' %}
                        <div>
                        <div class="alert alert-danger alert-dismissible fade show text-dark alert-question" role="alert">
                            <strong class=""></strong> <img src="{{ asset('assets/chapters/advertencia.svg') }}">
                                    {{ 'message_api.alert.pdf'|trans({}, 'message_api',  app.user.locale) }}
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                     </div>
                     {% endif %}

                        {% if  chapter.getVideo == null and  chapter.type == 'Vídeo' %}
                        <div>
                        <div class="alert alert-danger alert-dismissible fade show text-dark alert-question" role="alert">
                             <strong class=""></strong> <img src="{{ asset('assets/chapters/advertencia.svg') }}">
                                    {{ 'message_api.alert.video'|trans({}, 'message_api',  app.user.locale) }}
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                         </div>
                     </div>
                     {% endif %}

                       {% if  chapter.type == 'Slider' and chapter.hasContentCompleted == false %}
                        <div>
                        <div class="alert alert-danger alert-dismissible fade show text-dark alert-question" role="alert">
                       <strong class=""></strong> <img src="{{ asset('assets/chapters/advertencia.svg') }}">
                                     {{ 'message_api.alert.slider'|trans({'%number%': chapter.type.getMinimiunImageSlider}, 'message_api', app.user.locale)|raw }}
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                     </div>
                     {% endif %}

                    {% if  chapter.getScorm == null and  chapter.type == 'Scorm' %}
                     <div>
                        <div class="alert alert-danger alert-dismissible fade show text-dark alert-question" role="alert">
                       <strong class=""></strong> <img src="{{ asset('assets/chapters/advertencia.svg') }}">
                                     {{ 'message_api.alert.scorm'|trans({}, 'message_api',  app.user.locale) }}
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                                  </div>
                     </div>
                     {% endif %}

                      {% if  chapter.type == 'Contents' and chapter.hasContentCompleted == false %}
                     <div>
                        <div class="alert alert-danger alert-dismissible fade show text-dark alert-question" role="alert">
                       <strong class=""></strong> <img src="{{ asset('assets/chapters/advertencia.svg') }}">
                                     {{ 'message_api.alert.chapter_content'|trans({'%number%': chapter.type.getMinimiunImageSlider}, 'message_api', app.user.locale)|raw }}
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                                 <div>
                         </div>
                     </div>
                     {% endif %}


                </div>
            </div>
        </div>

    </div>
    {% include table_template %}

    {% block delete_form %}
        {{ include('@EasyAdmin/crud/includes/_delete_form.html.twig', with_context = false) }}
    {% endblock delete_form %}

{% endblock main %}

{% block body_javascript %}

    {{ parent() }}
   
    <script type="text/javascript">

        $(function() {
            $('.action-delete').on('click', function(e) {
                e.preventDefault();

                 const url = $(this).attr('href');

                $('#modal-delete').modal({ backdrop: true, keyboard: true })
                    .off('click', '#modal-delete-button')
                    .on('click', '#modal-delete-button', function () {
                        let deleteForm = $('#delete-form');
                        deleteForm.attr('action', url);
                        deleteForm.trigger('submit');
                    });
            });
        });

    </script>
{% endblock %}
