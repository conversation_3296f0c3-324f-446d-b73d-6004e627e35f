<div class="content-panel mt-2 p-3 course-users">
	<h2>{{ announcementUser.user.fullName }}</h2>
	<div class="p-4">
		<div class="row">
			<div class="col-md-4">
				<div class="mb-3">
					<strong>{{ 'course.label_in_singular'|trans({}, 'messages',  app.user.locale) }} :</strong> {{ announcement.course.name  }}
				</div>
				<div class="mb-3">
					<strong>{{ 'announcements.configureFields.notified'|trans({}, 'messages',  app.user.locale) }} :</strong> {{ announcementUser.notified  | date('Y-m-d H:i')  }}
				</div>
				<div class="mb-3">
					<strong>{{ 'announcements.configureFields.start_at'|trans({}, 'messages',  app.user.locale) }}:</strong>
					{{ userCourse is not null and userCourse.startedAt ? userCourse.startedAt|date('Y-m-d H:i') : '-' }}
				</div>

				<div class="mb-3">
					<strong>{{ 'announcements.configureFields.finish_at'|trans({}, 'messages',  app.user.locale) }} :</strong>
					{{ userCourse is not null and userCourse.finishedAt ? userCourse.finishedAt|date('Y-m-d H:i') : '-' }}
				</div>
			 	<div class="mb-3">
					<strong>{{ 'announcements.configureFields.tutor'|trans({}, 'messages',  app.user.locale) }} :</strong>
					 {% for announcementTutor in announcement.tutors %}
                		{{ announcementTutor.tutor.fullName }}
            		{% endfor %}
				</div>
			</div>

			<div class="col-md-4">
				<div class="mb-3">
					<strong>{{ 'user.configureFields.time_spent'|trans({}, 'messages',  app.user.locale) }}:</strong>
					{{ userChapters | propertySum('timeSpent') | niceTime }}
				</div>

				<div class="mb-3">
					<strong>{{ 'user.configureFields.content_viewed'|trans({}, 'messages',  app.user.locale) }}:</strong>
					{{ (userChapters | length / announcementUser.announcement.course.chapters | length * 100) | round }}
					%
					({{ userChapters | length }}/{{ announcementUser.announcement.course.chapters | length }})
				</div>

				<div class="mb-3">
					<strong>{{'user.configureFields.interaction_with_teacher'|trans({}, 'messages',  app.user.locale) }} :</strong>
					{{ messages | length }}
				</div>
			</div>

			<div class="col-md-4">
				{# <div class="mb-3">
					<strong>{{'user.configureFields.interaction_in_forum'|trans({}, 'messages',  app.user.locale) }} :</strong>
					{{ forumPosts | length}}
				</div> #}

				<div class="mb-3">
					<strong>{% trans %}Apt{% endtrans %}:</strong>
					{% if userCourse is not null and userCourse.finishedAt %}
						<span class="fas fa-check-circle text-success"></span>
					{% else %}
						<span class="fas fa-times-circle text-danger"></span>
					{% endif %}
				</div>
			</div>
		</div>
	</div>
</div>