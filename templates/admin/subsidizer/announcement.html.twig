{% extends '@!EasyAdmin/crud/detail.html.twig' %}
{% set announcement = entity.instance %}

{% block head_stylesheets %}
    {{ parent() }}
    {{ encore_entry_link_tags('player') }}
    {{ encore_entry_link_tags('material-course') }}
{% endblock %}

{% block content_title %}
     {{ 'announcements.label_in_singular'|trans({}, 'messages',  app.user.locale) }} : {{ announcement.course.name }}
{% endblock content_title %}

{% block page_actions %}
    {% if announcement.course.typeCourse != 2 %}
    <a class="action-report btn btn-primary" href="{{ path('report-pdf-announcement', {'id': announcement.id}) }}"><i
                class="fas fa-file-pdf"></i> {{ 'announcements.configureFields.report'|trans({}, 'messages',  app.user.locale) }}
    </a>
     <a class="action-report btn btn-success" href="{{ path('report-excel-announcement', {'id': announcement.id}) }}"><i class="fas fa-file-excel"></i>
        {{ 'announcements.configureFields.report'|trans({}, 'messages',  app.user.locale) }}
    </a>
    {% endif %}

    {{ parent() }}
{% endblock %}


{% block main %}
    <div class="row mt-2">
        {% include "admin/subsidizer/announcement/information_announcement.html.twig" %}
        {% include "admin/subsidizer/announcement/tabs.html.twig" %}

    </div>
    {% block chapter_player %}
        {{ include('admin/player.html.twig', with_context = true) }}
    {% endblock %}

{% endblock main %}

{% block body_javascript %}
 <script>
	let idCourse = {{ announcement.course.id | json_encode | raw }};
    </script>
    {{ parent() }}
    {{ encore_entry_script_tags('player') }}
    {{ encore_entry_script_tags('courseChapters') }}
    {{ encore_entry_script_tags('material-course') }}
{% endblock %}
