<div class="col-lg-12" v-if="tab === 'managers'">
    <itinerary-manager-filter
            id="{{ itinerary.id }}"
            url-get-filters="/admin/itinerary/available-filters"
            url-get-users="/admin/itinerary/{{ itinerary.id }}/get-managers"
            url-search-users="/admin/itinerary/{{ itinerary.id }}/search-managers"
            url-add-user="/admin/itinerary/{{ itinerary.id }}/add-manager"
            url-add-all="/admin/itinerary/{{ itinerary.id }}/add-managers"
            url-remove-user="/admin/itinerary/{{ itinerary.id }}/remove-manager"
            url-remove-all="/admin/itinerary/{{ itinerary.id }}/remove-all-managers"
            :translations="translations.manager_filter"
            :use-i18n="false"
            i18n-prefix="ITINERARY.MANAGER"
            v-on:users-updated="managersUpdated"
            v-bind:show-custom-content="true"
            v-on:on-change-page="onChangeManagerPage"
            v-bind:paginated-users="managers"
    >
        <template v-slot:custom-content>
            <div class="col-md-12 px-0">
                <table class="table">
                    <thead>
                    <tr>
                        <th scope="col">{{ 'user.configureFields.email'|trans({}, 'messages',  app.user.locale) }}</th>
                        <th scope="col">{{ 'user.configureFields.first_name'|trans({}, 'messages',  app.user.locale) }}</th>
                        <th scope="col">{{ 'user.configureFields.last_name'|trans({}, 'messages',  app.user.locale) }}</th>
                        <th></th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr v-for="(user, index) in pageOfManagers" :key="index">
                        <td>${ user.email }</td>
                        <td>${ user.firstName }</td>
                        <td>${ user.lastName }</td>
                        <td>
                            <button @click="deleteManager(user.id, '{{  'itinerary.manager.confirm_delete'|trans({}, 'messages',  app.user.locale) }}')" class="btn btn-sm btn-danger"><i class="fa fa-trash"></i></button>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <div class="col-md-12 d-flex align-items-center justify-content-center" v-if="loadingManagers">
                    <Loader :is-loaded="loadingManagers"></Loader>
                </div>
            </div>
        </template>
    </itinerary-manager-filter>
</div>
