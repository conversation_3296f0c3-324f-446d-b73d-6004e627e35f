<div class="mt-2 mb-3">
    <div class="filters-section">
        <div class="row">
            {% if user_use_filters  and filterCategories %}
                {% for filter_category in filterCategories %}
                    <div class="col-sm-6 col-md-4 col-lg-3">
                        <div class="form-group text-nowrap py-0">
                            <label for="filter{{ filter_category.id }}" class="text-left">{{ filter_category.name }}</label>
                            <select  v-model="bd_filters.category_{{ filter_category.id }}"
                                    name="{{ filter_category.name }}"
                                    class="form-control" data-ea-widget="ea-autocomplete" data-allow-clear="true"
                                    id="filter_category_{{ filter_category.id }}">
                                <option value=""></option>
                                {% for lowFilter in filter_category.filters %}
                                    <option value="{{ lowFilter.id }}">{{ lowFilter.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                {% endfor %}
            {% endif %}
        </div>
        <div class="row">
            <div class="actions text-right">
                <button type="button" class="btn btn-danger mr-1" data-dismiss="modal" @click="clearFilters">
                    <i class="fa fa-brush"></i>
                    {{ 'stats.clear_filters'|trans({}, 'messages',  app.user.locale) }}
                </button>
                <button type="button" class="btn btn-primary ml-1" data-dismiss="modal" @click="applyFilters">
                    <i class="fa fa-check"></i>
                    {{ 'stats.apply_filters'|trans({}, 'messages',  app.user.locale) }}
                </button>
            </div>
        </div>
    </div>
</div>
