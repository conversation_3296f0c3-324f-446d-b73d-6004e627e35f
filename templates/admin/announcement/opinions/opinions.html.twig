{% if valorationAnnouncement %}
<div class="mt-2" style="padding: 1rem">
    <div class="accordion" id="accordionExample">
        {% for valoration in  valorationAnnouncement %}
        <div class="accordion-item" style="background: #fff">
            <h2 class="accordion-header" id="heading{{ valoration.user.id }}">
                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ valoration.user.id }}" aria-expanded="true" aria-controls="collapse{{ valoration.user.id }}">
                   {{ valoration.user.firstName }} {{ valoration.user.lastName }}
                </button>
            </h2>
            <div id="collapse{{ valoration.user.id }}" class="accordion-collapse collapse" aria-labelledby="heading{{ valoration.user.id }}" data-bs-parent="#accordionExample">
                <div class="accordion-body">
                    <table class="table">
                        <thead>
                        <tr>
                            <th scope="col">{{ 'question.label_in_singular'|trans({}, 'messages') }}</th>
                            <th scope="col">{{ 'games.text_common.answer'|trans({}, 'messages') }}</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for nps in valoration.nps %}
                        <tr>
                            <td width="70%">
                                <i class="fas fa-arrow-circle-right" style="color:var(--color-primary)"></i>  {{ nps.question }}
                            </td>
                            <td width="30%" class=" table-secondary {{ nps.question.type != 'text' ? 'text-left'  : '' }}">
                                {% if nps.question.source  == 1 and nps.question.type == 'nps' and nps.question.main == 1  %}
                                    <div>
                                        {% for star in 1..5 %}
                                                <i class="fas fa-star text-secondary"></i>
                                        {% endfor %}
                                    </div>
                                    <div style="margin-top: -1.5rem;">
                                    {% for star in 1..nps.value %}
                                        {%  if (star%2) == 0 %}
                                            <i class="fas fa-star" style="color:var(--color-primary); "></i>
                                        {% endif %}
                                    {% endfor %}
                                    </div>
                                    {% elseif  nps.question.source  == 2 and nps.question.type == 'nps' and nps.question.main == 1 %}
                                        <div>
                                            {% for star in 1..4 %}
                                                <i class="fas fa-star text-secondary"></i>
                                            {% endfor %}
                                        </div>
                                        <div style="margin-top: -1.5rem">
                                        {% for star in 1..nps.value %}
                                            {%  if (star%2) == 0 %}
                                                <i class="fas fa-star" style="color:var(--color-primary)"></i>
                                            {% endif %}
                                        {% endfor %}
                                        </div>

                                        {% elseif nps.question.type == 'checkbox'  %}
                                            {% if nps.value == 1 %}
                                                <i class="fas fa-check" style="color: var(--color-primary)"></i>
                                            {% else %}
                                                <i class="fas fa-times text-danger"></i>
                                            {% endif %}
                                    {% else %}
                                        {{ nps.value }}
                                {% endif %}
                                </td>
                        </tr>
                        {%  endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% else %}
<div class="mt-5">
    <div class="card text-center">
        <div class="card-header" style="height:20rem">
            <h3 style="padding-top:8rem"> {{ 'no_content'|trans({}, 'messages') }}</h3>
        </div>
    </div>
</div>
{% endif %}
