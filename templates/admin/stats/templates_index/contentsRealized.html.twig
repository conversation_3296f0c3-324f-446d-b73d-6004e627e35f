<div class="contents-realized">
                <div class="box-info type--3">
                    <div v-if="general.totalAnnouncements !== undefined" class="content">
                        <div class="box-icon"><i class="fas fa-bullhorn"></i></div>

                        <div class="title">
                            <div class="number">
                                <b>${general.totalAnnouncements | formatNumber}</b>
                                <span>{{ 'stats.title_made_f'|trans({}, 'messages',  app.user.locale) }}</span>
                            </div>
                        </div>

                        <div class="name">{{ 'user.configureFields.announcements'|trans({}, 'messages',  app.user.locale) }}</div>
                    </div>
                </div>

                <div class="box-info type--1">
                    <div v-if="general.nps !== undefined" class="content">
                        <div class="box-icon"><i class="fas fa-thumbs-up"></i></div>

                        <div class="title">
                            <div class="number">
                                <b>${general.nps}</b>
                                <span>%</span>
                            </div>
                        </div>

                        <div class="name">NPS</div>
                    </div>
                </div>

                <div class="box-info type--4">
                    <div v-if="general.ratioCourses !== undefined" class="content">
                        <div class="box-icon"><i class="fas fa-balance-scale"></i></div>

                        <div class="title">
                            <div class="number">
                                <b>${general.ratioCourses}</b>
                            </div>
                        </div>

                        <div class="name">{{ 'user.configureFields.ratio_course'|trans({}, 'messages',  app.user.locale) }}</div>
                    </div>
                </div>

                <div class="box-info type--5">
                    <div v-if="general.npsAVG !== undefined" class="content">
                        <div class="box-icon"><i class="fas fa-star"></i></div>

                        <div class="title">
                            <div class="number">
                                <b>${(general.npsAVG || 0).toFixed(2)}</b>
                            </div>
                        </div>

                        <div class="name">{{ 'user.configureFields.avg_stars'|trans({}, 'messages',  app.user.locale) }}</div>
                    </div>
                </div>
            </div>