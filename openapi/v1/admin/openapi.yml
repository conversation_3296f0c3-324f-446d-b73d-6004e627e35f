openapi: 3.0.0
info:
  title: Easylearning Admin API v1
  description: API for Easylearning administration
  version: 1.0.0

servers:
  - url: /admin
    description: Admin server V1

security:
  - bearerAuth: [ ]

paths:
  /announcement/form/available-students:
    post:
      tags:
        - Announcement
      summary: Get available students for announcement
      description: Retrieves paginated list of available students for an announcement with filtering capabilities
      operationId: getAvailableStudents
      parameters:
        - name: announcement
          in: query
          required: true
          schema:
            type: integer
            format: int64
          description: "Announcement ID"
        - name: page
          in: query
          required: true
          schema:
            type: integer
            minimum: 1
          description: "Page number for pagination"
        - name: page-size
          in: query
          required: true
          schema:
            type: integer
            minimum: 1
            maximum: 100
          description: "Number of items per page"
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                searchQuery:
                  type: string
                  description: "Search query to filter users"
                  nullable: true
                filters:
                  type: object
                  description: "Filter criteria by categories"
                  additionalProperties:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                statusFilters:
                  type: object
                  description: "Status filter for users"
                  properties:
                    Usuarios:
                      type: object
                      properties:
                        id:
                          type: integer
                          nullable: true
      responses:
        '200':
          description: Available students retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AvailableStudentsResponse'
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 400
                  error:
                    type: boolean
                    example: true
                  data:
                    type: string
                    description: "Error message describing the validation issue"
                    enum:
                      - "The \"announcement\" parameter is required and must be a valid ID."
                      - "The announcement does not have valid dates configured."
                      - "Invalid page or page-size parameters."
                      - "Invalid \"course\" or \"announcement\" parameters."
                    example: "The \"announcement\" parameter is required and must be a valid ID."
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Insufficient permissions
        '404':
          description: Announcement not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 404
                  error:
                    type: boolean
                    example: true
                  data:
                    type: string
                    example: "Announcement not found with ID: 123"
        '5XX':
          description: Internal server error

  /help-categories/{id}:
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
        description: "Help category ID"
    delete:
      description: "Delete help category"
      tags: ["help-categories"]
      responses:
        204:
          description: "Help category deleted successfully"
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 204
                  error:
                    type: boolean
                    example: false
                  data:
                    type: string
                    nullable: true
                    example: null
        400:
          description: "Invalid request"
        401:
          description: "Unauthorized"
        403:
          description: "Forbidden"
        500:
          description: "Help category not found - handled by global exception listener"
        422:
          description: "Cannot delete category with associated help texts"
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 422
                  error:
                    type: boolean
                    example: true
                  data:
                    type: string
                    description: "Translated error message from message_api.help_category.delete_error"
                    example: "No se puede eliminar la categoría Nombre de la categoría, tiene contenido vinculado"
        5XX:
          description: "Internal server error"

  /announcement/form/students:
    post:
      tags:
        - Announcement
      summary: Save announcement students
      description: Saves the students assigned to an announcement into groups
      operationId: saveAnnouncementStudents
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - id
                - students
              properties:
                id:
                  type: integer
                  description: Announcement ID
                students:
                  type: array
                  description: Groups of students
                  items:
                    type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                              description: User ID
      responses:
        '200':
          description: Groups retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnnouncementGroup'
        '422':
          description: Duplicated users
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 422
                  error:
                    type: boolean
                    example: true
                  data:
                    type: string
                    example: "ANNOUNCEMENT.FORM.DUPLICATED_USER"
        '400':
          description: Invalid request parameters
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Insufficient permissions
        '404':
          description: Announcement not found

  /courses/{id}/active:
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
        description: "Course ID"
    put:
      tags:
        - Course
      summary: Set active course state
      description: Publish or unpublish a course in the platform.
      operationId: getActiveCourse
      responses:
        '200':
          description: Course retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CourseActive'

components:
  schemas:
    AvailableStudentsResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        error:
          type: boolean
          example: false
        data:
          type: object
          properties:
            totalItems:
              type: integer
              description: "Total number of available students"
              example: 76
            data:
              type: array
              description: "Array of available students"
              example:
                - id: 1
                  firstName: "John"
                  lastName: "Smith"
                  email: "<EMAIL>"
                  isActive: true
                  code: null
                  isDuplicated: true
                - id: 2
                  firstName: "Maria"
                  lastName: "Garcia"
                  email: "<EMAIL>"
                  isActive: true
                  code: null
                  isDuplicated: true
                - id: 3
                  firstName: "David"
                  lastName: "Johnson"
                  email: "<EMAIL>"
                  isActive: true
                  code: null
                  isDuplicated: false
              items:
                type: object
                properties:
                  id:
                    type: integer
                    description: "User ID"
                  firstName:
                    type: string
                    description: "User first name"
                  lastName:
                    type: string
                    description: "User last name"
                  email:
                    type: string
                    description: "User email address"
                  isActive:
                    type: boolean
                    description: "Whether the user is active"
                  code:
                    type: string
                    nullable: true
                    description: "User code, can be null or email for external users"
                  isDuplicated:
                    type: boolean
                    description: "Indicates if user is already assigned to overlapping announcements"
    AnnouncementGroup:
      type: object
      properties:
        status:
          type: integer
          example: 200
        error:
          type: boolean
          example: false
        data:
          type: array
          items:
            type: object
            properties:
              id:
                type: integer
                example: 1
              companyProfile:
                type: object
                nullable: true
              code:
                type: string
                example: "1"
              companyCif:
                type: string
                nullable: true
              denomination:
                type: string
                nullable: true
              fileNumber:
                type: string
                nullable: true
              tutor:
                type: object
                properties:
                  identificationValue:
                    type: string
                    example: ""
                  email:
                    type: string
                    example: "<EMAIL>"
                  telephone:
                    type: string
                    example: ""
                  filename:
                    type: string
                    example: "/uploads/users/cv/"
                  tutoringTime:
                    type: string
                    example: ""
                  name:
                    type: string
                    example: "tutor name"
                  tutorId:
                    type: integer
                    example: 1
                  cv_filesManagerId:
                    type: integer
                    nullable: true
                  idCompany:
                    type: integer
                    example: 1
                  cv:
                    type: object
                    properties:
                      name:
                        type: string
                        nullable: true
                      filename:
                        type: string
                        example: "/uploads/users/cv/"
                      url:
                        type: string
                        example: "/uploads/users/cv//uploads/users/cv/"
                  company:
                    type: object
                    properties:
                      id:
                        type: integer
                        example: 1
                      name:
                        type: string
                        example: "Gestionet"
              data:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: integer
                      example: 1
                    email:
                      type: string
                      example: "<EMAIL>"
                    firstName:
                      type: string
                      example: "Name"
                    lastName:
                      type: string
                      example: "Lastname"
              numberOfSessions:
                type: integer
                nullable: true
              place:
                type: string
                nullable: true
              cost:
                type: string
                example: "0.00"
              typeMoney:
                type: string
                nullable: true
              sessions:
                type: array
                items:
                  type: object

    CourseActive:
      type: object
      properties:
        status:
          type: integer
          example: 200
        error:
          type: boolean
          example: false
        message:
          type: string
          example: "Curso marcado como no publicado"
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
