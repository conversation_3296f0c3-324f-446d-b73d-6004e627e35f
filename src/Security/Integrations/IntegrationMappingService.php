<?php

declare(strict_types=1);

namespace App\Security\Integrations;

use App\Entity\Filter;
use App\Entity\FilterCategory;
use App\Entity\IntegrationGroup;
use App\Entity\IntegrationMapping;
use App\Entity\ProfessionalCategory;
use App\Entity\User;
use App\Entity\UserExtra;
use App\Entity\UserFieldsFundae;
use App\Security\Integrations\Clients\MappingResultModifierInterface;
use App\Security\Integrations\Exceptions\IntegrationMappingException;
use App\Security\Integrations\Exceptions\IntegrationUserException;
use App\Security\Integrations\Utils\Mapper;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;

class IntegrationMappingService
{
    public const ENTITY_USER = 'User';
    public const ENTITY_USER_EXTRA = 'UserExtra';
    public const ENTITY_USER_FIELDS_FUNDAE = 'UserFieldsFundae';

    private EntityManagerInterface $em;
    private LoggerInterface $logger;
    private Mapper $mapper;
    private SettingsService $settingsService;

    public function __construct(EntityManagerInterface $em, LoggerInterface $logger, SettingsService $settingsService)
    {
        $this->em = $em;
        $this->logger = $logger;
        $this->mapper = new Mapper($this->logger);
        $this->settingsService = $settingsService;
    }

    public function getFilterMapping(IntegrationGroup $group, ?string $tag = null): ?IntegrationMapping
    {
        return $this->getIntegrationMapping($group, $tag, IntegrationMapping::TYPE_FILTER);
    }

    public function getIntegrationMapping(IntegrationGroup $group, ?string $entity = null, string $type = IntegrationMapping::TYPE_ENTITY): ?IntegrationMapping
    {
        return $this->em->getRepository(IntegrationMapping::class)->findOneBy([
            'integrationGroup' => $group,
            'entity' => $entity,
            'type' => $type,
        ]);
    }

    /**
     * @param int|IntegrationGroup $group
     *
     * @return IntegrationMappingResult[]
     *
     * @throws IntegrationMappingException
     */
    public function doMapping($group, array $values = [], bool $persistToDatabase = false, ?MappingResultModifierInterface $modifier = null, ?IntegrationUserPasswordInterface $integrationUserPassword = null): array
    {
        if (!($group instanceof IntegrationGroup)) {
            $group = $this->em->getRepository(IntegrationGroup::class)->find($group);
        }

        if (!\is_array(end($values))) {
            $values = [$values];
        }

        $results = [];

        $userMapping = $this->getIntegrationMapping($group, self::ENTITY_USER);
        if (!$userMapping) {
            throw IntegrationMappingException::userMappingRequired();
        }
        $userExtraMapping = $this->getIntegrationMapping($group, self::ENTITY_USER_EXTRA);
        $userFieldsFundaeMapping = $this->getIntegrationMapping($group, self::ENTITY_USER_FIELDS_FUNDAE);
        $filtersMapping = $this->getIntegrationMapping($group, null, IntegrationMapping::TYPE_FILTER);

        foreach ($values as $value) {
            try {
                $mappingResult = new IntegrationMappingResult();
                $user = $this->getUser($userMapping, $value, $integrationUserPassword);
                if ($modifier) {
                    $user->setIsActive($modifier->isUserActive($value));
                }
                $mappingResult->setUser($user);
                // Generate other data
                $userExtra = $user->getExtra();
                if (!$userExtra) {
                    $userExtra = new UserExtra();
                    $userExtra->setCategory($this->em->getRepository(ProfessionalCategory::class)->find(1));
                }
                $userFieldsFundae = $user->getUserFieldsFundae() ?? new UserFieldsFundae();
                $this->getUserExtra($userExtraMapping, $value, $userExtra);
                $this->getUserFieldsFundae($userFieldsFundaeMapping, $value, $userFieldsFundae);
                $filters = $this->getFilters($filtersMapping, $value);

                $mappingResult->setUser($user)
                    ->setUserExtra($userExtra)
                    ->setUserFieldsFundae($userFieldsFundae)
                    ->setFilters($filters)
                ;

                $mappingResult->applyDataToUser();

                if ($modifier) {
                    $modifier->handleMappingResult($mappingResult, $value);
                }

                // Handle password correction
                if (empty($user->getPassword())) {
                    $user->setPassword($this->generatePassword());
                    $mappingResult->setUser($user);
                }

                if ($persistToDatabase) {
                    $this->em->persist($mappingResult->getUser());
                }

                $results[] = $mappingResult;
            } catch (IntegrationUserException $e) {
                // avoid stopping the process
                $this->logger->error($e->getMessage());
            }
        }

        if ($persistToDatabase) {
            $this->em->flush();
        }

        return $results;
    }

    public function assignToObject(&$object, array $mapping = [], array $values = [])
    {
        $this->mapper->assignToObject($object, $mapping, $values);
    }

    /**
     * @param array<string, string|array> $data
     *
     * @return array<array<string, string>>
     */
    public function toEditableMapping(array $data, string $type = IntegrationMapping::TYPE_ENTITY): array
    {
        $editable = [];
        foreach ($data as $remote => $local) {
            if (IntegrationMapping::TYPE_ENTITY === $type) {
                $editable[] = ['local' => $local, 'remote' => $remote];
            } else {
                $l = [];
                foreach ($local as $id) {
                    $category = $this->em->getRepository(FilterCategory::class)->find($id);
                    if (!$category) {
                        continue;
                    }
                    $l[] = [
                        'id' => $category->getId(),
                        'name' => $category->getName(),
                    ];
                }
                $editable[] = ['local' => $l, 'remote' => $remote];
            }
        }

        return $editable;
    }

    /**
     * @throws IntegrationMappingException
     */
    public function generatePassword(int $size = 10): string
    {
        try {
            return bin2hex(random_bytes($size));
        } catch (\Exception $e) {
            throw IntegrationMappingException::cannotGenerateUserPassword();
        }
    }

    /**
     * @param array<string, mixed> $values
     *
     * @throws IntegrationUserException
     */
    public function getUser(IntegrationMapping $userMapping, array $values = [], ?IntegrationUserPasswordInterface $integrationUserPassword = null): User
    {
        if (empty($values)) {
            throw IntegrationUserException::noValues();
        }
        $mapping = $userMapping->getMapping();

        $remote2email = $this->getRemoteField(User::IDENTIFIER_EMAIL, $mapping);
        $remote2code = $this->getRemoteField(User::IDENTIFIER_CODE, $mapping);

        $emailValue = $values[$remote2email] ?? null;
        $codeValue = $values[$remote2code] ?? null;

        if (empty($emailValue) && empty($codeValue)) {
            throw IntegrationUserException::noEmailOrCode();
        }

        $userRepository = $this->em->getRepository(User::class);
        $identifier = $userMapping->getIdentifier() ?? User::IDENTIFIER_EMAIL;
        $user = $userRepository->findOneBy([
            $identifier => User::IDENTIFIER_EMAIL === $identifier ? $emailValue : $codeValue,
        ]);

        if (!$user) {
            // Try to find the user with the other identifier
            $identifier = User::IDENTIFIER_EMAIL === $identifier ? User::IDENTIFIER_CODE : User::IDENTIFIER_EMAIL;
            $user = $userRepository->findOneBy([
                $identifier => User::IDENTIFIER_EMAIL === $identifier ? $emailValue : $codeValue,
            ]);
        }

        $integrationsResetPassword = $_ENV['INTEGRATIONS_RESET_PASSWORD'] ?? false;

        if (!$user) {
            $user = new User();

            if (null !== $integrationUserPassword) {
                $integrationUserPassword->setPassword($user, $values);
            }

            $user
                ->setEmail($emailValue)
                ->setCode($codeValue . '')
                ->setIsActive(false)
                ->setValidated(true)
                ->setOpen(false)
                ->setStarteam(false)
                ->setLocale($this->settingsService->get('app.defaultLanguage') ?? 'en')
            ;
        } elseif ($integrationsResetPassword && null !== $integrationUserPassword) {
            $integrationUserPassword->setNewPassword($user, $values);
        }

        $this->assignToObject($user, $mapping, $values);

        $emailValue = trim($user->getEmail());
        if (empty($emailValue)) {
            $user->setEmail('user' . $codeValue . '@user.com');
        }

        return $user;
    }

    /**
     * @param array<string, mixed> $values
     *
     * @return void
     */
    public function getUserExtra(?IntegrationMapping $mapping, array $values, ?UserExtra &$userExtra)
    {
        if (!$mapping) {
            return;
        }
        $this->assignToObject($userExtra, $mapping->getMapping() ?? [], $values);
    }

    /**
     * @param array<string, mixed> $values
     *
     * @return void
     */
    public function getUserFieldsFundae(?IntegrationMapping $mapping, array $values, ?UserFieldsFundae &$userFieldsFundae)
    {
        if (!$mapping) {
            return;
        }
        $this->assignToObject($userFieldsFundae, $mapping->getMapping() ?? [], $values);
    }

    /**
     * @param array<string, mixed> $values
     *
     * @return Filter[]
     */
    public function getFilters(?IntegrationMapping $filterMapping, array $values = []): array
    {
        if (!$filterMapping) {
            return [];
        }

        /** @var array<string, array<int>> $mapping */
        $mapping = $filterMapping->getMapping() ?? [];
        if (empty($mapping)) {
            return [];
        }

        /** @var Filter[] $filters */
        $filters = [];

        foreach ($mapping as $remoteField => $categories) {
            $value = $this->mapper->getValue($remoteField, $values);
            if (empty($value)) {
                continue;
            }

            if (!\is_array($value)) {
                $value = [$value];
            }

            $temp = [];
            foreach ($value as $v) {
                $temp = array_merge($temp, $this->addFilterToCategories($v, $categories));
            }
            $filters = array_merge($filters, $temp);
        }

        return $filters;
    }

    /**
     * @param array<int> $categories
     *
     * @return Filter[] Already stored in database to avoid duplicates
     */
    private function addFilterToCategories(string $name, array $categories): array
    {
        $categoryRepository = $this->em->getRepository(FilterCategory::class);
        $filters = [];
        foreach ($categories as $catId) {
            $category = $categoryRepository->find($catId);
            if (!$category) {
                continue;
            }
            $filters[] = $this->findOrCreateFilter($category, trim($name), trim($name));
        }
        $this->em->flush(); // Available in db for next cycle

        return $filters;
    }

    public function findOrCreateFilter(FilterCategory $category, string $name, ?string $code = null, string $source = Filter::SOURCE_REMOTE): Filter
    {
        if (empty($code)) {
            $code = $name;
        }
        $filter = $this->em->getRepository(Filter::class)->findOneBy([
            'code' => $code,
            'name' => $name,
            'filterCategory' => $category,
        ]);
        if ($filter) {
            return $filter;
        }
        $filter = new Filter();
        $filter->setFilterCategory($category)
            ->setCode($name)
            ->setSort(0)
            ->setName($code)
            ->setSource($source)
        ;
        $this->em->persist($filter);

        return $filter;
    }

    private function getRemoteField(string $pLocal, array $fields = []): ?string
    {
        foreach ($fields as $remote => $local) {
            if (false !== strpos($local, ':')) {
                $separated = explode(':', $local);
                if ($separated[0] === $pLocal) {
                    return $remote;
                }
            } elseif ($pLocal === $local) {
                return $remote;
            }
        }

        return null;
    }
}
