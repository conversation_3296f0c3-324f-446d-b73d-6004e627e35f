<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250614163548 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE `lti_chapter` ADD lti_tool_identifier_id VARCHAR(36) DEFAULT NULL,
               ADD CONSTRAINT FK_LTI_CHAPTER_LTI_TOOL_V2_ID FOREIGN KEY (lti_tool_identifier_id) REFERENCES 
               `lti_tool_v2`(id) ON DELETE RESTRICT
        SQL);

        $this->addSql(<<<'SQL'
            ALTER TABLE lti_chapter MODIFY lti_tool_id INT NULL DEFAULT NULL
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE `lti_chapter` DROP FOREIGN KEY FK_LTI_CHAPTER_LTI_TOOL_V2_ID,
                DROP lti_tool_identifier_id
        SQL);
    }
}
