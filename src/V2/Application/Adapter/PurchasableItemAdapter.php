<?php

declare(strict_types=1);

namespace App\V2\Application\Adapter;

use App\Entity\Course;
use App\V2\Domain\Purchase\Exception\InvalidPurchasableItemException;
use App\V2\Domain\Purchase\PurchasableItem;
use App\V2\Domain\Shared\Financial\Money;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Resource\Resource;
use App\V2\Domain\Shared\Resource\ResourceType;
use App\V2\Domain\Shared\Uuid\UuidGenerator;

readonly class PurchasableItemAdapter
{
    public function __construct(
        private UuidGenerator $uuidGenerator,
    ) {
    }

    /**
     * @throws InvalidPurchasableItemException
     */
    public function fromCourse(Course $course, Money $price): PurchasableItem
    {
        return new PurchasableItem(
            id: $this->uuidGenerator->generate(),
            name: $course->getName(),
            description: $course->getDescription() ?? '',
            price: $price,
            resource: new Resource(
                type: ResourceType::Course,
                id: new Id($course->getId())
            ),
            createdAt: new \DateTimeImmutable(),
        );
    }
}
