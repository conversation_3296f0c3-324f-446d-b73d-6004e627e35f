<?php

declare(strict_types=1);

namespace App\V2\Application\DTO\User;

use App\Entity\User;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\User\UserCollection;

class UserListItemDTOFactory
{
    /**
     * Create a UserListItemDTO from a User entity with actions based on the requesting user.
     */
    public static function createFromUser(User $user, ?User $requestUser = null): UserListItemDTO
    {
        // Determine actions based on roles and creator relationship
        $canEdit = false;
        $canDelete = false;
        $canImpersonate = false;

        if (null !== $requestUser) {
            $canImpersonate = $requestUser->isAdmin() || $requestUser->isSupport();
            $canEdit = $requestUser->isAdmin() || ($requestUser->isManager() && $user->getCreatedBy()?->getId() === $requestUser->getId());
            $canDelete = $canEdit;
        }

        return new UserListItemDTO(
            $user->getId(),
            $user->getAvatar(),
            $user->getEmail(),
            $user->getFirstName(),
            $user->getLastName(),
            $user->getRoles(),
            $user->getIsActive(),
            $user->getPoints(),
            $canEdit,
            $canDelete,
            $canImpersonate
        );
    }

    /**
     * Create a UserListItemDTOCollection from a UserCollection with actions based on the requesting user.
     *
     * @throws CollectionException
     */
    public static function createCollectionFromUserCollection(
        UserCollection $userCollection,
        ?User $requestUser = null
    ): UserListItemDTOCollection {
        return new UserListItemDTOCollection(
            array_map(
                fn (User $user) => self::createFromUser($user, $requestUser),
                $userCollection->all()
            )
        );
    }
}
