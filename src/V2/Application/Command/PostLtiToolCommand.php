<?php

declare(strict_types=1);

namespace App\V2\Application\Command;

use App\V2\Domain\Bus\Command;
use App\V2\Domain\Shared\Url\Url;
use App\V2\Domain\Shared\Uuid\Uuid;

readonly class PostLtiToolCommand implements Command
{
    public function __construct(
        private Uuid $registrationId,
        private string $name,
        private string $audience,
        private Url $oidcInitiationUrl,
        private Url $launchUrl,
        private Url $deepLinkingUrl,
        private Url $jwksUrl,
    ) {
    }

    public function getRegistrationId(): Uuid
    {
        return $this->registrationId;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getAudience(): string
    {
        return $this->audience;
    }

    public function getOidcInitiationUrl(): Url
    {
        return $this->oidcInitiationUrl;
    }

    public function getLaunchUrl(): Url
    {
        return $this->launchUrl;
    }

    public function getDeepLinkingUrl(): Url
    {
        return $this->deepLinkingUrl;
    }

    public function getJwksUrl(): Url
    {
        return $this->jwksUrl;
    }
}
