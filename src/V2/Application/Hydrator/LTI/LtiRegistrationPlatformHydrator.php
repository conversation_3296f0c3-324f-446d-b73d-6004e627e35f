<?php

declare(strict_types=1);

namespace App\V2\Application\Hydrator\LTI;

use App\V2\Application\Hydrator\Hydrator;
use App\V2\Application\Hydrator\HydratorPriority;
use App\V2\Domain\LTI\LtiPlatformCriteria;
use App\V2\Domain\LTI\LtiPlatformRepository;
use App\V2\Domain\LTI\LtiRegistration;
use App\V2\Domain\LTI\LtiRegistrationCollection;
use App\V2\Domain\LTI\LtiRegistrationHydrationCriteria;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Hydrator\HydrationCriteria;
use App\V2\Domain\Shared\Uuid\UuidCollection;

class LtiRegistrationPlatformHydrator implements Hydrator
{
    private const HydratorPriority PRIORITY = HydratorPriority::First;

    public function __construct(
        private readonly LtiPlatformRepository $ltiPlatformRepository,
    ) {
    }

    public function getPriority(): HydratorPriority
    {
        return self::PRIORITY;
    }

    public function supports(HydrationCriteria $criteria): bool
    {
        return $criteria instanceof LtiRegistrationHydrationCriteria && $criteria->needsPlatform();
    }

    public function hydrate(Collection $collection, HydrationCriteria $criteria): void
    {
        if (!$collection instanceof LtiRegistrationCollection) {
            return;
        }

        if ($collection->isEmpty()) {
            return;
        }

        $ids = $collection->reduce(
            callback: function (array $carry, LtiRegistration $registration) {
                $carry[] = $registration->getId();

                return $carry;
            },
            initial: []
        );

        $ids = array_unique($ids);

        $platformCollection = $this->ltiPlatformRepository->findBy(
            LtiPlatformCriteria::createEmpty()->filterByRegistrationIds(new UuidCollection($ids))
        );

        $platformByRegistration = [];
        foreach ($platformCollection->all() as $platform) {
            $platformByRegistration[$platform->getRegistrationId()->value()] = $platform;
        }

        foreach ($collection->all() as $registration) {
            $platform = $platformByRegistration[$registration->getId()->value()] ?? null;
            if (!$platform) {
                continue;
            }
            $registration->setPlatform($platform);
        }
    }
}
