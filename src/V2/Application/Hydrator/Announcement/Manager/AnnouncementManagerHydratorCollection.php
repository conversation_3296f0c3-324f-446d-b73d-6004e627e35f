<?php

declare(strict_types=1);

namespace App\V2\Application\Hydrator\Announcement\Manager;

use App\V2\Application\Hydrator\HydratorCollection;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerCollection;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerHydrationCriteria;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Hydrator\HydrationCriteria;
use App\V2\Domain\Shared\Hydrator\HydratorException;

class AnnouncementManagerHydratorCollection extends HydratorCollection
{
    public function hydrate(Collection $collection, HydrationCriteria $criteria): void
    {
        if (!$collection instanceof AnnouncementManagerCollection || !$criteria instanceof AnnouncementManagerHydrationCriteria) {
            throw new HydratorException();
        }

        parent::hydrate($collection, $criteria);
    }
}
