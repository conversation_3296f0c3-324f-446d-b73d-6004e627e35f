<?php

declare(strict_types=1);

namespace App\V2\Application\QueryHandler\Admin;

use App\Entity\LtiTool as LegacyLtiTool;
use App\Repository\ChapterRepository as LegacyChapterRepository;
use App\Repository\CourseRepository as LegacyCourseRepository;
use App\Repository\LtiChapterRepository as LegacyLtiChapterRepository;
use App\V2\Application\Query\Admin\LaunchLtiChapterQuery;
use App\V2\Domain\LTI\Exceptions\LaunchLtiChapterQueryHandlerException;
use App\V2\Domain\LTI\Exceptions\LtiChapterNotFound;
use App\V2\Domain\LTI\Exceptions\LtiToolNotFoundException;
use App\V2\Domain\LTI\Launch\LaunchResultType;
use App\V2\Domain\LTI\LtiKeyProvider;
use App\V2\Domain\LTI\LtiToolCriteria;
use App\V2\Domain\LTI\LtiToolRepository;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use OAT\Library\Lti1p3Core\Exception\LtiExceptionInterface;
use OAT\Library\Lti1p3Core\Message\Launch\Builder\LtiResourceLinkLaunchRequestBuilder;
use OAT\Library\Lti1p3Core\Message\Payload\LtiMessagePayloadInterface;
use OAT\Library\Lti1p3Core\Registration\RegistrationRepositoryInterface;
use OAT\Library\Lti1p3Core\Resource\LtiResourceLink\LtiResourceLink;

readonly class LaunchLtiChapterQueryHandler
{
    public function __construct(
        private LegacyCourseRepository $legacyCourseRepository,
        private LegacyChapterRepository $legacyChapterRepository,
        private LegacyLtiChapterRepository $legacyLtiChapterRepository,
        private LtiToolRepository $ltiToolRepository,
        private RegistrationRepositoryInterface $registrationRepository,
        private LtiResourceLinkLaunchRequestBuilder $ltiResourceLinkLaunchRequestBuilder,
        private LtiKeyProvider $ltiKeyProvider,
    ) {
    }

    /**
     * @throws InfrastructureException
     * @throws LtiToolNotFoundException
     * @throws CriteriaException
     * @throws InvalidUuidException
     * @throws LaunchLtiChapterQueryHandlerException
     * @throws LtiChapterNotFound
     */
    public function handle(LaunchLtiChapterQuery $query): string
    {
        $course = $this->legacyCourseRepository->find($query->getCourseId()->value());
        if (null === $course) {
            throw LaunchLtiChapterQueryHandlerException::courseNotFound();
        }

        $chapter = $this->legacyChapterRepository->find($query->getChapterId()->value());
        if (null === $chapter) {
            throw LaunchLtiChapterQueryHandlerException::chapterNotFound();
        }

        $ltiChapter = $this->legacyLtiChapterRepository
            ->findOneBy(['chapter' => $query->getChapterId()->value()]);
        if (null === $ltiChapter) {
            throw new LtiChapterNotFound();
        }

        if (
            null === $ltiChapter->getLtiTool()
            && null === $ltiChapter->getLtiToolIdentifierId()
        ) {
            throw LaunchLtiChapterQueryHandlerException::ltiChapterNoRelationToConfiguration();
        }

        if (null !== $ltiChapter->getLtiToolIdentifierId()) {
            $ltiTool = $this->ltiToolRepository->findOneBy(
                LtiToolCriteria::createById(new Uuid($ltiChapter->getLtiToolIdentifierId()))
            );

            $registration = $this->registrationRepository->find($ltiTool->getRegistrationId()->value());
        } else {
            /** @var LegacyLtiTool $legacyLtiTool */
            $legacyLtiTool = $ltiChapter->getLtiTool();
            $registration = $this->registrationRepository->findByClientId($legacyLtiTool->getClientId());
        }

        if (null === $registration) {
            throw LaunchLtiChapterQueryHandlerException::noRegistrationForLtiChapter();
        }

        $resourceLinkId = $ltiChapter->getIdentifier();

        /**
         * For multiple deployments in same tool, is required for ltiTool to save who is the deployment it will query.
         */
        $deploymentId = $registration->getDefaultDeploymentId();

        $resourceLink = new LtiResourceLink(
            identifier: $resourceLinkId,
            properties: [
                'url' => $registration->getTool()->getLaunchUrl(),
            ]
        );

        $loginHint = json_encode([
            'id' => $query->getUser()->getId(),
            'email' => $query->getUser()->getEmail(),
            'iat' => (new \DateTimeImmutable())->getTimestamp(),
        ]);

        $loginHintSignature = '';
        if (!$this->ltiKeyProvider->sign($loginHint, $loginHintSignature)) {
            throw LaunchLtiChapterQueryHandlerException::failedToSign();
        }

        try {
            $message = $this->ltiResourceLinkLaunchRequestBuilder->buildLtiResourceLinkLaunchRequest(
                ltiResourceLink: $resourceLink,
                registration: $registration,
                loginHint: base64_encode($loginHint) . '.' . base64_encode($loginHintSignature),
                deploymentId: $deploymentId,
                roles: [
                    'http://purl.imsglobal.org/vocab/lis/v2/membership#Learner',
                ],
                optionalClaims: [
                    LtiMessagePayloadInterface::CLAIM_LTI_CUSTOM => [
                        'id' => $ltiChapter->getIdentifier(),
                    ],
                    LtiMessagePayloadInterface::CLAIM_LTI_AGS => [
                        'scope' => [
                            'https://purl.imsglobal.org/spec/lti-ags/scope/lineitem',
                            'https://purl.imsglobal.org/spec/lti-ags/scope/result.readonly',
                            'https://purl.imsglobal.org/spec/lti-ags/scope/score',
                        ],
                        'lineitems' => $query->getSchemeAndHttpHost()
                            . '/lti/' . $query->getUser()->getId() . '/' . $resourceLinkId . '/lineitems/',
                        'lineitem' => $query->getSchemeAndHttpHost()
                            . '/lti/' . $query->getUser() . '/' . $resourceLinkId . '/lineitems',
                    ],
                ]
            );
        } catch (LtiExceptionInterface $e) {
            throw LaunchLtiChapterQueryHandlerException::fromPrevious($e);
        }

        return match ($query->getResultType()) {
            LaunchResultType::HtmlLink => $message->toHtmlLink('Launch Chapter'),
            LaunchResultType::URL => $message->toUrl(),
            default => $message->toHtmlRedirectForm(),
        };
    }
}
