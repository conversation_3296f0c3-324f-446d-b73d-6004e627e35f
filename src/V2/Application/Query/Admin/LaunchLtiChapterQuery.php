<?php

declare(strict_types=1);

namespace App\V2\Application\Query\Admin;

use App\Entity\User;
use App\V2\Domain\Bus\Query;
use App\V2\Domain\LTI\Launch\LaunchResultType;
use App\V2\Domain\Shared\Id\Id;

readonly class LaunchLtiChapterQuery implements Query
{
    public function __construct(
        private Id $courseId,
        private Id $chapterId,
        private User $user,
        private string $schemeAndHttpHost,
        private LaunchResultType $resultType = LaunchResultType::RedirectForm,
    ) {
    }

    public function getCourseId(): Id
    {
        return $this->courseId;
    }

    public function getChapterId(): Id
    {
        return $this->chapterId;
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function getSchemeAndHttpHost(): string
    {
        return $this->schemeAndHttpHost;
    }

    public function getResultType(): LaunchResultType
    {
        return $this->resultType;
    }
}
