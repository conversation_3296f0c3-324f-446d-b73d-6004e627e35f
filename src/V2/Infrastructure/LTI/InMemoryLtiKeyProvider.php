<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\LTI;

use App\V2\Domain\LTI\Exceptions\LtiException;
use App\V2\Domain\LTI\LtiKeyProvider;
use App\V2\Domain\Shared\Exception\InfrastructureException;

class InMemoryLtiKeyProvider implements LtiKeyProvider
{
    public const string ALGORITHM = 'RS256';

    private const string PRIVATE_KEY = '************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';

    private const string PUBLIC_KEY = '-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtHdXwwPUAVP/cuncey+K
65hgdvxJzMeqC0RZBuNm+0W7YS2g1s18EFbS4n34N+Dd+UQ508JsxxIxitkoaRIy
5m7oPVGNdmtLkNnhGrKUWNQJq4dWLvRfv7RKwTPGVN/QcUwlEw+Y69gVWDc2RZnn
9UpGY9AxYzSnbcK0ZlhtEfQQ2jWtbK0ivPgAuh2m6oktQoTO9p38ZH6AfQCGiIQG
SYuEl5Thvg67oaJ/pUJlScjCIe2cH7SzTvoxFVCBj+ktgm0OV6nNs1Iqx3q0zQJr
pVOMBBQYch5YB5MLQ/CLZk6b5QagWukkEUkDihBSZNZujLVZtuwQyZh9p79R7MBR
UQIDAQAB
-----END PUBLIC KEY-----
';

    public function getAlgorithm(): string
    {
        return self::ALGORITHM;
    }

    public function generateKeys(): void
    {
        throw new InfrastructureException('Not implemented');
    }

    public function getPublicKeyFile(): string
    {
        return self::PUBLIC_KEY;
    }

    public function getPrivateKeyFile(): string
    {
        return self::PRIVATE_KEY;
    }

    public function sign(string $data, string &$signature): bool
    {
        return openssl_sign(
            data: $data,
            signature: $signature,
            private_key: $this->getPrivateKeyFile(),
            algorithm: OPENSSL_ALGO_SHA256
        );
    }

    /**
     * @throws LtiException
     */
    public function verify(string $data, string $signature): bool
    {
        $result = openssl_verify(
            data: $data,
            signature: $signature,
            public_key: $this->getPublicKeyFile(),
            algorithm: OPENSSL_ALGO_SHA256
        );

        if ($result < 0) {
            throw new LtiException('Failed to verify.');
        }

        return 1 === $result;
    }
}
