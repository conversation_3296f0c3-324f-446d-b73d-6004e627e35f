<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\LTI;

use App\V2\Domain\LTI\LtiKeyProvider;
use OAT\Library\Lti1p3Core\Security\Jwks\Exporter\Jwk\JwkRS256Exporter;
use OAT\Library\Lti1p3Core\Security\Key\KeyChainFactoryInterface;
use OAT\Library\Lti1p3Core\Security\Key\KeyChainInterface;

class CoreKeyChainGenerator
{
    public const string KEY_IDENTIFIER = 'EasyLearning-Sig';
    public const string KEY_SET_NAME = 'EasyLearning';

    public function __construct(
        private LtiKeyProvider $ltiKeyProvider,
        private KeyChainFactoryInterface $keyChainFactory,
        private string $identifier = self::KEY_IDENTIFIER,
    ) {
    }

    public function generateCoreKeyChain(): KeyChainInterface
    {
        return $this->keyChainFactory->create(
            identifier: $this->identifier,
            keySetName: self::KEY_SET_NAME,
            publicKey: $this->ltiKeyProvider->getPublicKeyFile(),
            privateKey: $this->ltiKeyProvider->getPrivateKeyFile(),
            algorithm: $this->ltiKeyProvider->getAlgorithm(),
        );
    }

    public function exportJWKSPublicKey(): array
    {
        $exporter = new JwkRS256Exporter();

        return $exporter->export($this->generateCoreKeyChain());
    }
}
