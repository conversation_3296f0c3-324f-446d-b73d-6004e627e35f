<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller;

use App\V2\Application\Query\GetHealthCheck;
use App\V2\Domain\Shared\Email\Email;
use App\V2\Domain\User\UserCriteria;
use App\V2\Infrastructure\Bus\QueryBusAccessor;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class HealthCheckController extends QueryBusAccessor
{
    public function __invoke(Request $request): Response
    {
        $emailSearch = new Email('<EMAIL>');
        $userCriteria = UserCriteria::createEmpty()->filterByEmail($emailSearch);

        $this->ask(new GetHealthCheck(
            criteria: $userCriteria
        ));

        return new JsonResponse(
            status: Response::HTTP_NO_CONTENT,
        );
    }
}
