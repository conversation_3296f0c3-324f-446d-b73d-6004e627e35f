<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence;

use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\Criteria;
use App\V2\Domain\Shared\Criteria\CriteriaId;
use App\V2\Domain\Shared\Criteria\SortDirection;
use App\V2\Domain\Shared\Entity\EntityWithId;
use App\V2\Domain\Shared\Identifier;

class InMemoryCommonCriteriaBuilder
{
    /**
     * @throws CollectionException
     */
    public static function filterByCommonCriteria(Criteria $criteria, array $objects, bool $onlyFilter = false): array
    {
        if ($criteria instanceof CriteriaId) {
            $objects = self::filterByCommonCriteriaId($criteria, $objects);
        }

        if ($onlyFilter) {
            return $objects;
        }

        if ($criteria->getSortBy()) {
            usort(
                $objects,
                function (EntityWithId $object1, EntityWithId $object2) use ($criteria) {
                    foreach ($criteria->getSortBy()->all() as $sortItem) {
                        $direction = $sortItem->getDirection();
                        $fieldGetter = 'get' . ucfirst($sortItem->getField()->value());

                        if (!method_exists($object1, $fieldGetter) || !method_exists($object2, $fieldGetter)) {
                            continue;
                        }

                        if (\is_string($object2->$fieldGetter()) && \is_string($object1->$fieldGetter())) {
                            $result = strcmp($object1->$fieldGetter(), $object2->$fieldGetter());
                            if (0 !== $result) {
                                return SortDirection::DESC === $direction ? -$result : $result;
                            }
                        } elseif ($object1->$fieldGetter() !== $object2->$fieldGetter()) {
                            if (SortDirection::DESC === $direction) {
                                return $object1->$fieldGetter() < $object2->$fieldGetter() ? 1 : -1;
                            } else {
                                return $object1->$fieldGetter() > $object2->$fieldGetter() ? 1 : -1;
                            }
                        }
                    }

                    return 0; // Objects are equal according to all sort criteria
                }
            );
        }

        if ($criteria->getPagination()) {
            $objects = \array_slice(
                $objects,
                $criteria->getPagination()->offset(),
                $criteria->getPagination()->limit()
            );
        }

        return $objects;
    }

    /**
     * @throws CollectionException
     */
    private static function filterByCommonCriteriaId(CriteriaId $criteria, array $objects): array
    {
        return array_filter(
            $objects,
            function (EntityWithId $object) use ($criteria) {
                return (
                    \is_null($criteria->getId())
                    || $object->getId()->equals($criteria->getId())
                ) && (
                    empty($criteria->getIds())
                    || !$criteria->getIds()->filter(
                        function (Identifier $id) use ($object) {
                            return $object->getId()->equals($id);
                        }
                    )->isEmpty()
                );
            }
        );
    }
}
