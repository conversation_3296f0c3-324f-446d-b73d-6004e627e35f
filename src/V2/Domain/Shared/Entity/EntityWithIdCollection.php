<?php

declare(strict_types=1);

namespace App\V2\Domain\Shared\Entity;

use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Id\IdCollection;

/**
 * @template T
 *
 * @extends Collection<T>
 */
abstract class EntityWithIdCollection extends Collection
{
    public function allIndexedById(): array
    {
        /**
         * @var EntityWithId[] $entities
         */
        $entities = $this->all();
        $indexedEntities = [];

        foreach ($entities as $entity) {
            $indexedEntities[$entity->getId()->value()] = $entity;
        }

        return $indexedEntities;
    }

    /**
     * @throws CollectionException
     */
    public function allIds(): IdCollection
    {
        /**
         * @var EntityWithId[] $entities
         */
        $entities = $this->all();
        $ids = [];

        foreach ($entities as $entity) {
            $ids[] = $entity->getId();
        }

        return new IdCollection($ids);
    }
}
