<?php

declare(strict_types=1);

namespace App\V2\Domain\LTI;

use App\V2\Domain\Shared\Exception\InfrastructureException;

interface LtiKeyProvider
{
    /**
     * @throws InfrastructureException
     */
    public function generateKeys(): void;

    public function getAlgorithm(): string;

    public function getPublicKeyFile(): string;

    public function getPrivateKeyFile(): string;

    public function sign(string $data, string &$signature): bool;

    public function verify(string $data, string $signature): bool;
}
