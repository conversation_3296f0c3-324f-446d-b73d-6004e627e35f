<?php

declare(strict_types=1);

namespace App\V2\Domain\LTI;

use App\V2\Domain\Shared\Entity\EntityWithId;
use App\V2\Domain\Shared\Url\Url;
use App\V2\Domain\Shared\Uuid\Uuid;

class LtiPlatform extends EntityWithId
{
    public function __construct(
        Uuid $id,// LTI: identifier
        private readonly Uuid $registrationId,
        private readonly string $name,
        private readonly string $audience,
        private readonly Url $oidcAuthenticationUrl,
        private readonly Url $oauth2AccessTokenUrl,
        private readonly Url $jwksUrl,
    ) {
        parent::__construct($id);
    }

    public function getRegistrationId(): Uuid
    {
        return $this->registrationId;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getAudience(): string
    {
        return $this->audience;
    }

    public function getOidcAuthenticationUrl(): Url
    {
        return $this->oidcAuthenticationUrl;
    }

    public function getOauth2AccessTokenUrl(): Url
    {
        return $this->oauth2AccessTokenUrl;
    }

    public function getJwksUrl(): Url
    {
        return $this->jwksUrl;
    }

    public function __toString(): string
    {
        return json_encode([
            'id' => $this->id->value(),
            'registration_id' => $this->registrationId->value(),
            'name' => $this->name,
            'audience' => $this->audience,
        ]);
    }
}
