<?php

declare(strict_types=1);

namespace App\V2\Domain\LTI\Exceptions;

use App\V2\Domain\Shared\Exception\InfrastructureException;

class LtiException extends InfrastructureException
{
    public const int CODE = 108116105; // LTI in ASCII

    public static function clientIdMustBeUnique(): self
    {
        return new self(
            message: 'Client ID must be unique',
            code: self::CODE,
        );
    }

    public static function deploymentIdMustBeUniqueInRegistrationContext(): self
    {
        return new self(
            message: 'Deployment ID must be unique in registration context',
            code: self::CODE,
        );
    }

    public static function noPublicKeyFound(): self
    {
        return new self(
            message: 'No public key found',
            code: self::CODE,
        );
    }

    public static function noPrivateKeyFound(): self
    {
        return new self(
            message: 'No private key found',
            code: self::CODE,
        );
    }
}
