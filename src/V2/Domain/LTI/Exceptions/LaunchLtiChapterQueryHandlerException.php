<?php

declare(strict_types=1);

namespace App\V2\Domain\LTI\Exceptions;

class LaunchLtiChapterQueryHandlerException extends \Exception
{
    public const int CODE = 108116105; // LTI in ASCII

    public static function fromPrevious(\Throwable $previous): self
    {
        return new self($previous->getMessage(), $previous->getCode(), $previous);
    }

    public static function chapterNotFound(): self
    {
        return new self('Chapter not found');
    }

    public static function courseNotFound(): self
    {
        return new self('Course not found');
    }

    public static function ltiChapterNoRelationToConfiguration(): self
    {
        return new self('Chapter is of type LTI but no configuration is defined');
    }

    public static function noRegistrationForLtiChapter(): self
    {
        return new self('No LTI registration found for the chapter');
    }

    public static function failedToSign(): self
    {
        return new self(
            message: 'Failed to sign',
            code: self::CODE,
        );
    }
}
