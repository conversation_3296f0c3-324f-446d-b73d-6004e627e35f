<?php

declare(strict_types=1);

namespace App\V2\Domain\Announcement\Manager;

use App\V2\Domain\Shared\Criteria\Criteria;

/**
 * @extends Criteria<AnnouncementManagerCriteria>
 */
class AnnouncementManagerCriteria extends Criteria
{
    private ?int $userId = null;
    private ?int $announcementId = null;

    public function isEmpty(): bool
    {
        return null === $this->userId && null === $this->announcementId;
    }

    public function filterByUserId(int $userId): self
    {
        $this->userId = $userId;

        return $this;
    }

    public function filterByAnnouncementId(int $announcementId): self
    {
        $this->announcementId = $announcementId;

        return $this;
    }

    public function getUserId(): ?int
    {
        return $this->userId;
    }

    public function getAnnouncementId(): ?int
    {
        return $this->announcementId;
    }
}
