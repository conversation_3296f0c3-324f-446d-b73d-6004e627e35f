<?php

declare(strict_types=1);

namespace App\V2\Domain\Announcement\Manager;

use App\V2\Domain\Shared\Exception\InfrastructureException;

class AnnouncementManagerRepositoryException extends InfrastructureException
{
    public static function duplicateAnnouncementManager(AnnouncementManager $announcementManager): self
    {
        return new self(\sprintf(
            'Announcement manager with user id %d and announcement id %d already exists',
            $announcementManager->getUserId()->value(),
            $announcementManager->getAnnouncementId()->value()
        ));
    }
}
