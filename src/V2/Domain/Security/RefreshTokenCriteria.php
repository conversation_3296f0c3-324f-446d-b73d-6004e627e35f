<?php

declare(strict_types=1);

namespace App\V2\Domain\Security;

use App\V2\Domain\Shared\Criteria\Criteria;

/**
 * @extends Criteria<RefreshTokenCriteria>
 */
class RefreshTokenCriteria extends Criteria
{
    private ?string $token = null;

    public function isEmpty(): bool
    {
        return null === $this->token;
    }

    public function filterByToken(string $token): self
    {
        $this->token = $token;

        return $this;
    }

    public function getToken(): ?string
    {
        return $this->token;
    }
}
