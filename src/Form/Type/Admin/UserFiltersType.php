<?php

namespace App\Form\Type\Admin;

use App\Entity\Center;
use App\Entity\Filter;
use App\Entity\FilterCategory;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use App\Repository\FilterRepository;
use App\Repository\FilterCategoryRepository;
use App\Service\SettingsService;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;


class UserFiltersType extends AbstractType
{

    private SettingsService $settings;
    protected EntityManagerInterface $em;


    public function __construct (SettingsService $settings, EntityManagerInterface $em)
    {
        $this->settings = $settings;
        $this->em        = $em;
    }

    /**
     * @param FormBuilderInterface $builder
     * @param array $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {

        $filterRepository = $this->em->getRepository(Filter::class);

        $filterCategoryRepository = $this->em->getRepository(FilterCategory::class);
        $filter_categories = array();
        $filter_categories = $filterCategoryRepository->getCategories();
        foreach ($filter_categories as $category) {
            $filters = array();
            foreach ($filterRepository->getFilterCategory($category->getId()) as $filter)
            {
                $filters[$filter->getName()] = $filter->getId();
            }

            $builder->add($category->getName(), ChoiceType::class, [
                'choices' => $filters,
                'attr' => [
                    'data-ea-widget' => 'ea-autocomplete',
                ],
            ]);

        }

    }

    /**
     * @param OptionsResolver $resolver
     */
    public function configureOptions (OptionsResolver $resolver)
    {

        $resolver->setDefaults([
            'data_class' => 'App\Entity\UserFilter',
        ]);
    }
    
}