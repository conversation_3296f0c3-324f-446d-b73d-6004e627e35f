<?php

declare(strict_types=1);

namespace App\Campus\Games;

use App\Entity\Chapter;
use App\Enum\Games as EnumGameFormula;

class WordSearch extends Game
{
    public function getQuestions($userCourseChapter): array
    {
        $soupQuestions = $userCourseChapter->getChapter()->getOrdenarMenormayors();
        $questions = $this->getFormattedQuestions($soupQuestions);

        return [
            'questions' => $questions,
        ];
    }

    private function getFormattedQuestions($soupQuestions): array
    {
        $questions = [];
        foreach ($soupQuestions as $question) {
            $wordsText = $question->getWordsArray();
            $questions[] = [
                'id' => $question->getId(),
                'time' => $question->getTime(),
                'question' => $question->getTitle(),
                'words' => explode(',', $wordsText),
            ];
        }

        return $questions;
    }

    public function check($userCourseChapter, $answers): bool
    {
        // * Las comprobaciones se hacen en el front
        return true;
    }

    public function calculateGamePoints($data, $args = null)
    {
        if (!$args instanceof Chapter) {
            return null;
        }

        if (
            !isset($data['answers']) || !$data['answers'] > 0
            || !isset($data['timeTotal'])
            || !isset($data['words'])
        ) {
            return 0;
        }

        $chapterType = $args->getType();
        $answers = $data['answers'];
        $nAnswers = \count($answers);
        $maxTime = $data['timeTotal'];
        $nWords = $data['words'];
        $rightAnswers = 0;
        $failsAnswers = 0;
        $time = 0;
        $words = $data['words'];
        $nAttempts = \array_key_exists('attempts', $data) && \is_array($data['attempts'])
            ? \count($data['attempts']) : 1;

        if($nAnswers > 0){
            foreach ($answers as $answer) {
                foreach ($answer['attempts'] as $answerAttempt) {
                    if(isset($answerAttempt['correct'])){
                        if ($answerAttempt['correct']) {
                            ++$rightAnswers;
                        }
                        if (!$answerAttempt['correct']) {
                            ++$failsAnswers;
                        }
                    }
                    $time = $answerAttempt['time'];
                }
            }
        }

        $completionPercentage = $rightAnswers > 0 ? ($rightAnswers / $nWords) : 0;
        if ($completionPercentage < $chapterType->getPercentageCompleted()) {
            return 0;
        }

        $remainingTime = ($maxTime - $time) > 0 ? ($maxTime - $time) : 1;
        $basePercentage = EnumGameFormula::BASE_HALF
            + (
                EnumGameFormula::BASE_QUARTER * pow(EnumGameFormula::WORD_SEARCH_PENALTY, ($failsAnswers * 2) / $words)
                + (EnumGameFormula::BASE_QUARTER * ($remainingTime / $maxTime))
            );
        $adjustedPercentage = pow($basePercentage, EnumGameFormula::E_L_COEF);
        $attemptsCorrectionPercentage = $adjustedPercentage * pow(EnumGameFormula::ATTEMPTS_COEF, $nAttempts - 1);

        return $attemptsCorrectionPercentage;
    }
}
