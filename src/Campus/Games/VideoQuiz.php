<?php

declare(strict_types=1);

namespace App\Campus\Games;

use App\Entity\AnswersVideoQuiz;
use App\Entity\Chapter;
use App\Entity\Videoquiz as VideoQuizEntity;
use App\Enum\Games as EnumGameFormula;
use App\Enum\Games as GamesEnum;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;

class VideoQuiz extends Game
{
    protected EntityManagerInterface $em;
    protected SettingsService $settings;

    public function __construct(EntityManagerInterface $em, SettingsService $settings)
    {
        $this->em = $em;
        $this->settings = $settings;
        parent::__construct($em);
    }

    public function getQuestions($userCourseChapter): array
    {
        $repository = $this->em->getRepository(VideoQuizEntity::class);
        $videoQuiz = $repository->findOneBy([
            'chapter' => $userCourseChapter->getChapter()->getId(),
        ]);

        $quizQuestions = $videoQuiz->getVideopreguntas();
        $questions = $this->getFormattedQuestions($quizQuestions);

        return [
            'questions' => $questions,
            'videoDuration' => $videoQuiz->getVideoDuration(),
            'videoUrl' => $videoQuiz->getUrl(),
        ];
    }

    private function getFormattedQuestions($quizQuestions)
    {
        $path = $this->settings->get(GamesEnum::VIDEO_QUIZ_UPLOADS_PATH_KEY) . '/';
        $questions = [];
        foreach ($quizQuestions as $question) {
            $quizAnswers = $question->getAnswersVideoQuizzes();
            $answers = $this->getFormattedAnswers($quizAnswers);

            $questions[] = [
                'id' => $question->getId(),
                'question' => $question->getTexto(),
                'imageUrl' => $path . $question->getImage(),
                'random' => true,
                'time' => GamesEnum::VIDEO_QUIZ_QUESTION_TIME,
                'triggerTime' => $question->getCurrenttime(),
                'answers' => $answers,
            ];
        }
        usort($questions, function ($a, $b) {
            return $a['triggerTime'] <=> $b['triggerTime'];
        });

        return $questions;
    }

    private function getFormattedAnswers($quizAnswers): array
    {
        $answers = [];
        foreach ($quizAnswers as $answer) {
            $answers[] = [
                'id' => $answer->getId(),
                'answer' => $answer->getAnswer(),
            ];
        }

        return $answers;
    }

    public function check($userCourseChapter, $answers): array
    {
        return [
            'correct' => $this->isCorrect($answers),
        ];
    }

    private function isCorrect($answer)
    {
        if (!isset($answer) || !isset($answer->id)) {
            return false;
        }

        $answerId = $answer->id;
        $repository = $this->em->getRepository(AnswersVideoQuiz::class);
        $answer = $repository->find($answerId);

        return $answer->isIsCorrect();
    }

    public function calculateGamePoints($data, $args = null)
    {
        if (!$args instanceof Chapter) {
            return null;
        }

        if (!isset($data['answers']) || !$data['answers'] > 0
            || !isset($data['totalVideo']) || !isset($data['videoQuestions'])) {
            return 0;
        }

        $answers = $data['answers'];
        $nAnswers = \count($answers);
        $timeSpentVideoWatch = 0;
        $videoTime = $data['totalVideo'];
        $videoQuestions = $data['videoQuestions'];
        $nAttempts = \array_key_exists('attempts', $data) ? \count($data['attempts']) : 1;

        if($nAnswers > 0){
            foreach ($answers as $answer) {
                if ($answer['time']) {
                    $timeSpentVideoWatch += $answer['time'];
                }
            }
        }

        $averageTime = $videoTime / $timeSpentVideoWatch;
        $basePercentage = pow($averageTime, 1 / $videoQuestions);
        $adjustedPercentage = pow($basePercentage, EnumGameFormula::E_L_COEF);
        $attemptsCorrectionPercentage = $adjustedPercentage * pow(EnumGameFormula::ATTEMPTS_COEF, $nAttempts - 1);

        return $attemptsCorrectionPercentage;
    }
}
