<?php

namespace App\Campus\Controller\General;

use App\Entity\Message;
use App\Entity\MessageAttachment;
use App\Repository\AnnouncementRepository;
use App\Repository\CourseRepository;
use App\Repository\MessageRepository;
use App\Repository\UserRepository;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\Annotations as Rest;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Swagger\Annotations as SWG;
use Vich\UploaderBundle\Handler\DownloadHandler;
use Symfony\Contracts\Translation\TranslatorInterface;


/**
 * Class ApiController
 * @package App\Controller
 *
 * @Route("/api")
 */
class ApiMessageController extends ApiBaseController
{

    private $em;


    /**
     * ApiController constructor.
     *
     * @param $logger
     */
    public function __construct(LoggerInterface $logger, UserRepository $userRepository, CourseRepository $courseRepository, AnnouncementRepository $announcementRepository, EntityManagerInterface $em,  TranslatorInterface $translator, SettingsService $settingsService)
    {

        parent::__construct($logger, $userRepository, $courseRepository, $announcementRepository, $translator, $settingsService);
        $this->em = $em;
    }

    /**
     * @Rest\Get("/messages/recipients", name="api_messages_recipients_get")
     *
     * @return Response
     */
    public function getRecipients()
    {
        try {
            $code  = Response::HTTP_OK;
            $error = false;

            $data = [
                'recipients' => $this->userRepository->findAll(),
            ];
        } catch (\Exception $e) {
            $code    = Response::HTTP_INTERNAL_SERVER_ERROR;
            $error   = true;
            $message = "An error has occurred trying to start chapter: {$e->getMessage()}";
        }

        $response = [
            'status' => $code,
            'error'  => $error,
            'data'   => $code == Response::HTTP_OK ? $data : $message,
        ];

        return $this->sendResponse($response, array('groups' => array('messages')));
    }


    /**
     * @Rest\Get("/messages", name="api_messages_get")
     *
     * @return Response
     */
    public function getMessages()
    {
        try {
            $code  = Response::HTTP_OK;
            $error = false;

            $user = $this->userRepository->findOneBy(array('email' => $this->getUser()->getUsername()));

            $data = [
                'sent'     => $this->em->getRepository(Message::class)->findBy([
                    'sender' => $user,
                ]),
                'received' => $this->em->getRepository(Message::class)->findBy([
                    'recipient' => $user,
                ]),
            ];
        } catch (\Exception $e) {
            $code    = Response::HTTP_INTERNAL_SERVER_ERROR;
            $error   = true;
            $message = "An error has occurred trying to start chapter: {$e->getMessage()}";
        }

        $response = [
            'status' => $code,
            'error'  => $error,
            'data'   => $code == Response::HTTP_OK ? $data : $message,
        ];

        return $this->sendResponse($response, ['groups' => ['list', 'messages']]);
    }


    /**
     * @Rest\Get("/message/{id}", name="api_message_get")
     *
     * @param Message $message
     * @return Response
     */
    public function getMessage(Message $message)
    {
        $code  = Response::HTTP_OK;
        $error = false;

        if (!$message) {
            $code    = Response::HTTP_NOT_FOUND;
            $error   = true;
            $message = 'Message not found';
        } else if (!$message->isUserInvolved($this->getUser())) {
            $code    = Response::HTTP_UNAUTHORIZED;
            $error   = true;
            $message = 'Access not allowed';
        } elseif ($message->getRecipient() === $this->getUser()) {
            $message->markAsOpen();
            $this->em->persist($message);
            $this->em->flush();
        }

        $response = [
            'status' => $code,
            'error'  => $error,
            'data'   => $message,
        ];

        return $this->sendResponse($response, ['groups' => ['detail', 'messages']]);
    }


    /**
     * @Rest\Post("/messages/sent", name="api_messages_sent")
     *
     * @return Response
     */
    public function sentMessage(Request $request)
    {
        $status = Response::HTTP_BAD_REQUEST;
        $error  = true;

        $email     = $request->get('form-email');
        $subject   = $request->get('form-subject');
        $body      = $request->get('form-body');
        $replyId   = $request->get('form-reply');

        $recipient = $this->userRepository->findOneBy(['email' => $email]);

        if ($email and $subject and $body) {
            $message = new Message();
            $message
                ->setSender($this->getUser())
                ->setRecipient($recipient)
                ->setSentAt(new \DateTime())
                ->setSubject($subject)
                ->setBody($body);

            if ($replyId) {
                $result = $this->em->getRepository(Message::class)->find($replyId);
                $message->setReplyTo($result);
            }
            $this->em->persist($message);

            $countFile = $request->get('count-file');

            if ($countFile > 0) {
                for ($i = 0; $i < $countFile; $i++) {
                    $file = $request->files->get('file' . $i);

                    $messageAttachment = new MessageAttachment();
                    $messageAttachment->setMessage($message)
                        ->setFilename('')
                        ->setFilenameFile($file)
                        ->setName($file->getClientOriginalName());

                    $this->em->persist($messageAttachment);
                }
            }

            $this->em->flush();

            $status = Response::HTTP_OK;
            $error  = false;
        }

        $response = [
            'status' => $status,
            'error'  => $error,
        ];

        return $this->sendResponse($response, ['groups' => ['detail', 'user_area']]);
    }


    /**
     * @Rest\Get("/message-attachment/{id}", name="api_messages_attachment_download")
     *
     * @param MessageAttachment $messageAttachment
     * @param DownloadHandler $downloadHandler
     * @return JsonResponse|Response
     */
    public function downloadAttachment(MessageAttachment $messageAttachment, DownloadHandler $downloadHandler)
    {
        $status = Response::HTTP_NOT_FOUND;
        $error  = true;
        $data   = 'Attachment not allowed';

        if ($messageAttachment) {
            $message = $messageAttachment->getMessage();

            if ($message->getSender() == $this->getUser() or $message->getRecipient() == $this->getUser()) {
                return $downloadHandler->downloadObject($messageAttachment, 'filenameFile', null, $messageAttachment->getName());
            }
        }


        $response = [
            'status' => $status,
            'error'  => $error,
            'message'   => $data,
        ];

        return $this->sendResponse($response);
    }
}
