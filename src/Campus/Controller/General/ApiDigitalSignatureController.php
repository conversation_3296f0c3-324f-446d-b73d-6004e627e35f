<?php


namespace App\Campus\Controller\General;


use App\Entity\Course;
use App\Repository\AnnouncementRepository;
use App\Repository\CourseRepository;
use App\Repository\UserRepository;
use App\Service\Api\UserDigitalSignatureService;
use App\Service\SettingsService;
use FOS\RestBundle\Controller\Annotations as Rest;
use Psr\Log\LoggerInterface;
use Swagger\Annotations as SWG;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;
use Symfony\Component\HttpFoundation\Response;


/**
 * Class ApiController
 * @package App\Controller
 *
 * @Route("/api")
 */
class ApiDigitalSignatureController extends ApiBaseController
{

    private $userDigitalSignatureService;


    /**
     * ApiController constructor.
     *
     * @param $logger
     */
    public function __construct(
        LoggerInterface $logger,
        UserRepository $userRepository,
        CourseRepository $courseRepository,
        AnnouncementRepository $announcementRepository,
        TranslatorInterface $translator,
        SettingsService $settingsService,
        UserDigitalSignatureService $userDigitalSignatureService
    ) {
        parent::__construct($logger, $userRepository, $courseRepository, $announcementRepository, $translator, $settingsService);

        $this->userDigitalSignatureService = $userDigitalSignatureService;
    }

    private function handleException(\Exception $e): Response
    {
        $errorMessage = sprintf('Error on line %d of %s: %s', $e->getLine(), $e->getFile(), $e->getMessage());
        return $this->sendResponse(['error' => true, 'status' => 500, 'message' => $errorMessage]);
    }

    /**
     * @Rest\Post("/digital-signature/announcement-user", name="api_digital_signature_announcement_user")
     */
    public function saveDigitalSignatureAnnouncementUser(Request $request)
    {
        try {            
            $response = $this->userDigitalSignatureService->saveUserDigitalSignature($request);
            return $this->sendResponse($response);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }


    }
}
