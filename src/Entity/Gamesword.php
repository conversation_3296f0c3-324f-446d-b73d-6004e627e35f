<?php

namespace App\Entity;

use App\Repository\GameswordRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass=GameswordRepository::class)
 */
class Gamesword
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups({"roulette","gamesword"})
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"roulette","gamesword"})
     */
    private $question;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"roulette","gamesword"})
     */
    private $word;

    /**
     * @ORM\Column(type="integer") 
     * @Groups({"roulette","gamesword"})
     */
    private $time;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"roulette","gamesword"})
     */
    private $gametype;

    /**
     * @ORM\ManyToOne(targetEntity=chapter::class, inversedBy="gameswords")
     */
    private $chapter;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getQuestion(): ?string
    {
        return $this->question;
    }

    public function setQuestion(string $question): self
    {
        $this->question = $question;

        return $this;
    }

    public function getWord(): ?string
    {
        return $this->word;
    }

    public function setWord(string $word): self
    {
        $this->word = $word;

        return $this;
    }

    public function getTime(): ?int
    {
        return $this->time;
    }

    public function setTime(int $time): self
    {
        $this->time = $time;

        return $this;
    }

    public function getChapter(): ?chapter
    {
        return $this->chapter;
    }

    public function setChapter(?chapter $chapter): self
    {
        $this->chapter = $chapter;

        return $this;
    }

    public function getGametype(): ?string
    {
        return $this->gametype;
    }

    public function setGametype(string $gametype): self
    {
        $this->gametype = $gametype;

        return $this;
    }
}
