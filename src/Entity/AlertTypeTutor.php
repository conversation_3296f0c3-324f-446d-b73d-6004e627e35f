<?php

namespace App\Entity;

use App\Repository\AlertTypeTutorRepository;
use App\Behavior\Blamable;
use App\Behavior\Timestampable;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use Knp\DoctrineBehaviors\Contract\Entity\TranslatableInterface;
use Knp\DoctrineBehaviors\Model\Translatable\TranslatableTrait;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\HasLifecycleCallbacks()
 * @Gedmo\SoftDeleteable(fieldName="deletedAt", timeAware=false, hardDelete=false)
 * @ORM\Entity(repositoryClass=AlertTypeTutorRepository::class)
 */
class AlertTypeTutor implements TranslatableInterface
{
    use TranslatableTrait;

    use Blamable, Timestampable;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups({"list"})
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"list"})
     */
    private $name;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Groups({"list"})
     */
    private $description;

    /**
     * @ORM\Column(type="boolean")
     */
    private $active;

    /**
     * @ORM\OneToMany(targetEntity=AnnouncementAlertTutor::class, mappedBy="alertTypeTutor")
     */
    private $announcementAlertTutors;

    /**
     * @ORM\OneToMany(targetEntity=TypeCourseAlerts::class, mappedBy="alertTypeTutor", orphanRemoval=true, cascade={"persist", "remove"})
     */
    private $typeCourseAlerts;

    public function __construct()
    {
        $this->createdAt = new DateTime();
        $this->updatedAt = new DateTime();
        $this->active = 1;
        $this->announcementAlertTutors = new ArrayCollection();
        $this->typeCourseAlerts = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id)
    {
        $this->id = $id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function isActive(): ?bool
    {
        return $this->active;
    }

    public function setActive(bool $active): self
    {
        $this->active = $active;

        return $this;
    }

    /**
     * @return Collection<int, AnnouncementAlertTutor>
     */
    public function getAnnouncementAlertTutors(): Collection
    {
        return $this->announcementAlertTutors;
    }

    public function addAnnouncementAlertTutor(AnnouncementAlertTutor $announcementAlertTutor): self
    {
        if (!$this->announcementAlertTutors->contains($announcementAlertTutor)) {
            $this->announcementAlertTutors[] = $announcementAlertTutor;
            $announcementAlertTutor->setAlertTypeTutor($this);
        }

        return $this;
    }

    public function removeAnnouncementAlertTutor(AnnouncementAlertTutor $announcementAlertTutor): self
    {
        if ($this->announcementAlertTutors->removeElement($announcementAlertTutor)) {
            // set the owning side to null (unless already changed)
            if ($announcementAlertTutor->getAlertTypeTutor() === $this) {
                $announcementAlertTutor->setAlertTypeTutor(null);
            }
        }

        return $this;
    }

    public function __toString()
    {
        return $this->getId() . '-alert-type-tutor';
    }

    /**
     * @return Collection<int, TypeCourseAlerts>
     */
    public function getTypeCourseAlerts(): Collection
    {
        return $this->typeCourseAlerts;
    }

    public function setTypeCourseAlerts($typeCourseAlerts): self
    {
        $old = $this->getTypeCourseAlerts();
        foreach ($old as $o)
        {
            if (!in_array($o, $typeCourseAlerts)) $this->removeTypeCourseAlert($o);
        }
        foreach ($typeCourseAlerts as $t) $this->addTypeCourseAlert($t);
        return $this;
    }

    public function addTypeCourseAlert(TypeCourseAlerts $typeCourseAlert): self
    {
        if (!$this->typeCourseAlerts->contains($typeCourseAlert)) {
            $this->typeCourseAlerts[] = $typeCourseAlert;
            $typeCourseAlert->setAlertTypeTutor($this);
        }

        return $this;
    }

    public function removeTypeCourseAlert(TypeCourseAlerts $typeCourseAlert): self
    {
        if ($this->typeCourseAlerts->removeElement($typeCourseAlert)) {
            // set the owning side to null (unless already changed)
            if ($typeCourseAlert->getAlertTypeTutor() === $this) {
                $typeCourseAlert->setAlertTypeTutor(null);
            }
        }

        return $this;
    }
}
