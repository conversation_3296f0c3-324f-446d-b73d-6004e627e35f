<?php

namespace App\Entity;

use App\Repository\HelpCategoryRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Knp\DoctrineBehaviors\Contract\Entity\TranslatableInterface;
use Knp\DoctrineBehaviors\Model\Translatable\TranslatableTrait;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass=HelpCategoryRepository::class)
 */
class HelpCategory implements TranslatableInterface
{
    use TranslatableTrait;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups({"help"})
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"help"})
     */
    private $name;

    /**
     * @ORM\OneToMany(targetEntity=HelpText::class, mappedBy="category")
     * @Groups({"help"})
     */
    private $helpTexts;

    /**
     * @ORM\Column(type="integer")
     */
    private $sort;

    public function __construct()
    {
        $this->helpTexts = new ArrayCollection();
    }

    public function __toString()
    {
        return $this->name;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    /**
     * @return Collection|HelpText[]
     */
    public function getHelpTexts(): Collection
    {
        return $this->helpTexts;
    }

    public function addHelpText(HelpText $helpText): self
    {
        if (!$this->helpTexts->contains($helpText)) {
            $this->helpTexts[] = $helpText;
            $helpText->setCategory($this);
        }

        return $this;
    }

    public function removeHelpText(HelpText $helpText): self
    {
        if ($this->helpTexts->removeElement($helpText)) {
            // set the owning side to null (unless already changed)
            if ($helpText->getCategory() === $this) {
                $helpText->setCategory(null);
            }
        }

        return $this;
    }

    public function getSort(): ?int
    {
        return $this->sort;
    }

    public function setSort(int $sort): self
    {
        $this->sort = $sort;

        return $this;
    }
}
