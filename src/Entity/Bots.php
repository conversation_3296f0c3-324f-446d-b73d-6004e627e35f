<?php

namespace App\Entity;

use App\Repository\BotsRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass=BotsRepository::class)
 */
class Bots
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups({"challenge_duel_details"})
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"challenge_user_statistics","challenge_duel_details"})
     */
    private $name;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"challenge_user_statistics","challenge_duel_details"})
     */
    private $last_name;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"challenge_user_statistics","challenge_duel_details"})
     */
    private $route;

    /**
     * @ORM\OneToMany(targetEntity=ChallengeDuel::class, mappedBy="assigned_bot")
     */
    private $challengeDuels;

    public function __construct()
    {
        $this->challengeDuels = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getLastName(): ?string
    {
        return $this->last_name;
    }

    public function setLastName(string $last_name): self
    {
        $this->last_name = $last_name;

        return $this;
    }

    public function getRoute(): ?string
    {
        return 'assets/bot_images/' . $this->route;
    }

    public function setRoute(string $route): self
    {
        $this->route = $route;

        return $this;
    }

    /**
     * @return Collection|ChallengeDuel[]
     */
    public function getChallengeDuels(): Collection
    {
        return $this->challengeDuels;
    }

    public function addChallengeDuel(ChallengeDuel $challengeDuel): self
    {
        if (!$this->challengeDuels->contains($challengeDuel)) {
            $this->challengeDuels[] = $challengeDuel;
            $challengeDuel->setAssignedBot($this);
        }

        return $this;
    }

    public function removeChallengeDuel(ChallengeDuel $challengeDuel): self
    {
        if ($this->challengeDuels->removeElement($challengeDuel)) {
            // set the owning side to null (unless already changed)
            if ($challengeDuel->getAssignedBot() === $this) {
                $challengeDuel->setAssignedBot(null);
            }
        }

        return $this;
    }
}
