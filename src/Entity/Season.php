<?php

declare(strict_types=1);

namespace App\Entity;

use App\Repository\SeasonRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass=SeasonRepository::class)
 */
class Season
{
    public const string TYPE_SEQUENTIAL = 'sequential';
    public const string TYPE_FREE = 'free';
    public const string TYPE_EXAM = 'exam';

    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"detail"})
     */
    private ?int $id;

    /**
     * @ORM\Column(type="string", length=255)
     *
     * @Groups({"detail"})
     */
    private ?string $name;

    /**
     * @ORM\ManyToOne(targetEntity=Course::class, inversedBy="seasons")
     *
     * @ORM\JoinColumn(nullable=false)
     */
    private ?Course $course;

    /**
     * @ORM\OneToMany(targetEntity=Chapter::class, mappedBy="season")
     *
     * @Groups({"detail"})
     *
     * @ORM\OrderBy({"position" = "ASC"})
     */
    private $chapters;

    /**
     * @ORM\Column(type="integer")
     */
    private ?int $sort;

    /**
     * @ORM\Column(type="string", length=20, options={"default":"sequential"})
     *
     * @Groups({"detail"})
     */
    private string $type = self::TYPE_SEQUENTIAL;

    public function __construct()
    {
        $this->chapters = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getCourse(): ?Course
    {
        return $this->course;
    }

    public function setCourse(?Course $course): self
    {
        $this->course = $course;

        return $this;
    }

    /**
     * @return Collection|Chapter[]
     */
    public function getChapters(): Collection
    {
        return $this->chapters;
    }

    public function addChapter(Chapter $chapter): self
    {
        if (!$this->chapters->contains($chapter)) {
            $this->chapters[] = $chapter;
            $chapter->setSeason($this);
        }

        return $this;
    }

    public function removeChapter(Chapter $chapter): self
    {
        if ($this->chapters->removeElement($chapter)) {
            // set the owning side to null (unless already changed)
            if ($chapter->getSeason() === $this) {
                $chapter->setSeason(null);
            }
        }

        return $this;
    }

    public function __toString()
    {
        return $this->getName();
    }

    public function getSort(): ?int
    {
        return $this->sort;
    }

    public function setSort(int $sort): self
    {
        $this->sort = $sort;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }
}
