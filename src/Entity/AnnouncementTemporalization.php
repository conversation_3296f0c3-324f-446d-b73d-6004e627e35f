<?php

namespace App\Entity;

use App\Repository\AnnouncementTemporalizationRepository;
use App\Behavior\Blamable;
use App\Behavior\Timestampable;
use App\Utils\TimeZoneConverter\UtcTimezoneInterface;
use App\Utils\TimeZoneConverter\UtcTimezoneTrait;
use DateTime;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=AnnouncementTemporalizationRepository::class)
 */
class AnnouncementTemporalization implements UtcTimezoneInterface
{
    use UtcTimezoneTrait;
    use Blamable;
    use Timestampable;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="datetime_immutable")
     */
    private $startedAt;

    /**
     * @ORM\Column(type="datetime_immutable")
     */
    private $finishedAt;

    /**
     * @ORM\ManyToOne(targetEntity=Chapter::class, inversedBy="temporalizations")
     */
    private $chapter;

    /**
     * @ORM\ManyToOne(targetEntity=Announcement::class, inversedBy="temporalizations")
     */
    private $announcement;

    /**
     * Time in seconds
     * @ORM\Column(type="integer")
     */
    private ?int $minimumTime = 0;

    public function __construct()
    {
        $this->createdAt = new DateTime();
        $this->updatedAt = new DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getStartedAt(): ?\DateTimeImmutable
    {
        return $this->startedAt;
    }

    public function setStartedAt(\DateTimeImmutable $startedAt): self
    {
        $this->startedAt = $startedAt;

        return $this;
    }

    public function getFinishedAt(): ?\DateTimeImmutable
    {
        return $this->finishedAt;
    }

    public function setFinishedAt(\DateTimeImmutable $finishedAt): self
    {
        $this->finishedAt = $finishedAt;

        return $this;
    }

    public function getChapter(): ?Chapter
    {
        return $this->chapter;
    }

    public function setChapter(?Chapter $chapter): self
    {
        $this->chapter = $chapter;

        return $this;
    }

    public function getAnnouncement(): ?Announcement
    {
        return $this->announcement;
    }

    public function setAnnouncement(?Announcement $announcement): self
    {
        $this->announcement = $announcement;

        return $this;
    }

    public function getMinimumTime(): ?int
    {
        return $this->minimumTime;
    }

    public function setMinimumTime(int $minimumTime): self
    {
        $this->minimumTime = $minimumTime;

        return $this;
    }

    public function __toString()
    {
        return 'temporalization'.$this->getId();
    }

    public function __clone()
    {
        $this->id = null;

    }
} 
