<?php

namespace App\Entity;

use App\Repository\LibraryFileRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass=LibraryFileRepository::class)
 */
class LibraryFile
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups("library")
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=100)
     * @Groups("library")
     */
    private ?string $name;

    /**
     * @ORM\Column(type="string", length=20)
     * @Groups("library")
     */
    private ?string $type;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Groups("library")
     */
    private ?string $url;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Groups("library")
     */
    private ?string $filename;

    /**
     * @ORM\OneToOne(targetEntity=Library::class, inversedBy="libraryFile")
     * @ORM\JoinColumn(nullable=false)
     */
    private $library;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getUrl(): ?string
    {
        return $this->url;
    }

    public function setUrl(string $url): self
    {
        $this->url = $url;

        return $this;
    }

    public function getFilename(): ?string
    {
        return $this->filename;
    }

    public function setFilename(?string $filename): self
    {
        $this->filename = $filename;

        return $this;
    }

    public function getLibrary(): ?Library
    {
        return $this->library;
    }

    public function setLibrary(Library $library): self
    {
        $this->library = $library;

        return $this;
    }
}
