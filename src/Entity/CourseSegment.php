<?php

namespace App\Entity;

use App\Repository\CourseSegmentRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Knp\DoctrineBehaviors\Contract\Entity\TranslatableInterface;
use Knp\DoctrineBehaviors\Model\Translatable\TranslatableTrait;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass=CourseSegmentRepository::class)
 */
class CourseSegment  implements TranslatableInterface
{
    use AtAndBy;
    use TranslatableTrait;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups({"list","courseFilters", "update-course"})
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"list","courseFilters", "update-course"})
     */
    private $name;

    /**
     * @ORM\ManyToMany(targetEntity=Course::class, inversedBy="courseSegments")
     */
    private $Segment;

    /**
     * @ORM\ManyToOne(targetEntity=CourseSegmentCategory::class, inversedBy="segments")
     */
    private $courseSegmentCategory;

    public function __construct()
    {
        $this->Segment = new ArrayCollection();
    }

    public function __toString()
    {
        return $this->getCourseSegmentCategory() . ': ' . $this->getName();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    /**
     * @return Collection|Course[]
     */
    public function getSegment(): Collection
    {
        return $this->Segment;
    }

    public function addSegment(Course $segment): self
    {
        if (!$this->Segment->contains($segment)) {
            $this->Segment[] = $segment;
        }

        return $this;
    }

    public function removeSegment(Course $segment): self
    {
        $this->Segment->removeElement($segment);

        return $this;
    }

    public function getCourseSegmentCategory(): ?CourseSegmentCategory
    {
        return $this->courseSegmentCategory;
    }

    public function setCourseSegmentCategory(?CourseSegmentCategory $courseSegmentCategory): self
    {
        $this->courseSegmentCategory = $courseSegmentCategory;

        return $this;
    }
}
