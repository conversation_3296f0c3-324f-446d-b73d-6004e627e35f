<?php

namespace App\Entity;

use App\Repository\CourseSegmentCategoryRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Knp\DoctrineBehaviors\Contract\Entity\TranslatableInterface;
use Knp\DoctrineBehaviors\Model\Translatable\TranslatableTrait;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass=CourseSegmentCategoryRepository::class)
 */
class CourseSegmentCategory  implements TranslatableInterface
{
    use TranslatableTrait;
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups({"list","courseFilters"})
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"list","courseFilters"})
     */
    private $name;

    /**
     * @ORM\OneToMany(targetEntity=CourseSegment::class, mappedBy="courseSegmentCategory")
     * @ORM\OrderBy({"name" = "ASC"})
     * @Groups({"list","courseFilters"})
     */
    private $segments;

    public function __construct()
    {
        $this->segments = new ArrayCollection();
    }

    public function __toString()
    {
        return $this->name;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    /**
     * @return Collection|CourseSegment[]
     */
    public function getSegments(): Collection
    {
        return $this->segments;
    }

    public function addSegment(CourseSegment $segment): self
    {
        if (!$this->segments->contains($segment)) {
            $this->segments[] = $segment;
            $segment->setCourseSegmentCategory($this);
        }

        return $this;
    }

    public function removeSegment(CourseSegment $segment): self
    {
        if ($this->segments->removeElement($segment)) {
            // set the owning side to null (unless already changed)
            if ($segment->getCourseSegmentCategory() === $this) {
                $segment->setCourseSegmentCategory(null);
            }
        }

        return $this;
    }
}
