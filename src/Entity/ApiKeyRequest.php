<?php

namespace App\Entity;

use App\Repository\ApiKeyRequestRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=ApiKeyRequestRepository::class)
 */
class ApiKeyRequest
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=ApiKeyUser::class, inversedBy="requests")
     * @ORM\JoinColumn(nullable=false)
     */
    private $apiKeyUser;

    /**
     * @ORM\Column(type="datetime_immutable")
     */
    private $createdAt;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $ip;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $endpoint;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getApiKeyUser(): ?ApiKeyUser
    {
        return $this->apiKeyUser;
    }

    public function setApiKeyUser(?ApiKeyUser $apiKeyUser): self
    {
        $this->apiKeyUser = $apiKeyUser;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeImmutable $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getIp(): ?string
    {
        return $this->ip;
    }

    public function setIp(string $ip): self
    {
        $this->ip = $ip;

        return $this;
    }

    public function getEndpoint(): ?string
    {
        return $this->endpoint;
    }

    public function setEndpoint(string $endpoint): self
    {
        $this->endpoint = $endpoint;

        return $this;
    }
}
