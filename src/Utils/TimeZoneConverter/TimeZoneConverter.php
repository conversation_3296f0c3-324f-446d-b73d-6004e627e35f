<?php

namespace App\Utils\TimeZoneConverter;

class TimeZoneConverter
{
    /**
     * @param array $data
     * @return array
     */
    public static function checkTimezone(array $data): array
    {
        try
        {
            $total = count($data);
            if ($total === 0) return $data;
            $firstKey = array_key_first($data);
            $isMulti = is_array($data[$firstKey]);
            $utcTz = new \DateTimeZone('UTC');
            if (!$isMulti)
            {
                return self::getCorrectDateTime($data, $utcTz);
            }

            foreach ($data as &$value)
            {
                $value = self::getCorrectDateTime($value, $utcTz);
            }

            return $data;
        }
        catch (\Exception $e)
        {
            throw new TimeZoneConverterException($e->getMessage(), 500, $e);
        }
    }

    /**
     * @param $value
     * @param \DateTimeZone $utcTz
     * @return mixed
     * @throws \Exception
     */
    public static function getCorrectDateTime($value, \DateTimeZone $utcTz)
    {
        $hasTimezone = array_key_exists('timezone', $value) && !empty($value['timezone']);
        $timezone = $hasTimezone ? $value['timezone'] : 'Europe/Madrid';
        $tz = new \DateTimeZone($timezone);
        foreach ($value as $key => $internalValue) {
            if ($internalValue instanceof \DateTime || $internalValue instanceof \DateTimeImmutable) {
                $temp = new \DateTimeImmutable($internalValue->format('Y-m-d H:i:s'), $hasTimezone ? $utcTz : $tz);
                $value[$key] = $temp->setTimezone($tz);
            }
        }
        return $value;
    }
}
