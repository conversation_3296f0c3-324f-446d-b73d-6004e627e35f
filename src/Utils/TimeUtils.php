<?php

namespace App\Utils;

class TimeUtils
{
    public static function formatTime($time)
    {
        $hours = floor($time / 3600);
        $minutes = floor(($time - ($hours * 3600)) / 60);
        $seconds = $time - ($hours * 3600) - ($minutes * 60);

        return $hours . ':' . str_pad($minutes, 2, 0, STR_PAD_LEFT) . ":" . str_pad($seconds, 2, 0, STR_PAD_LEFT);
    }

    public static function addSecondsToTime($timeString, $secondsToAdd)
    {
        $timeParts = explode(':', $timeString);
        $hours = (int)$timeParts[0];
        $minutes = (int)$timeParts[1];
        $seconds = (int)$timeParts[2];

        $totalSeconds = $hours * 3600 + $minutes * 60 + $seconds + $secondsToAdd;

        $newHours = floor($totalSeconds / 3600);
        $newMinutes = floor(($totalSeconds - ($newHours * 3600)) / 60);
        $newSeconds = $totalSeconds - ($newHours * 3600) - ($newMinutes * 60);

        $newTime = sprintf('%02d:%02d:%02d', $newHours, $newMinutes, $newSeconds);

        return $timeString . ' - ' . $newTime;
    }  


    public static function timeUserInTheCourse($time):String
    {
        $horas = floor($time / 3600);
        $minutos = floor(($time - ($horas * 3600)) / 60);
        $segundos = $time - ($horas * 3600) - ($minutos * 60);

        return $horas . ':' . $minutos . ":" . $segundos;
    }
}
