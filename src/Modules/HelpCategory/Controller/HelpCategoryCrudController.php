<?php

declare(strict_types=1);

namespace App\Modules\HelpCategory\Controller;

use App\Entity\HelpCategory;
use App\Modules\Common\Controller\BaseVueController;
use App\Modules\HelpCategory\Services\HelpCategoryCrudService;
use App\Repository\HelpCategoryRepository;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Config\KeyValueStore;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use FOS\RestBundle\Controller\Annotations as Rest;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTManager;
use Psr\Log\LoggerInterface;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * @Route("/admin/")
 */
class HelpCategoryCrudController extends BaseVueController
{
    private AdminContextProvider $context;
    private JWTManager $JWTManager;
    private HelpCategoryRepository $helpCategoryRepository;
    private HelpCategoryCrudService $helpCategoryCrudService;

    public function __construct(
        AdminContextProvider $context,
        JWTManager $JWTManager,
        SettingsService $settings,
        EntityManagerInterface $em,
        LoggerInterface $logger,
        RequestStack $requestStack,
        TranslatorInterface $translator,
        HelpCategoryRepository $helpCategoryRepository,
        HelpCategoryCrudService $helpCategoryCrudService
    ) {
        parent::__construct($settings, $em, $logger, $requestStack, $translator);

        $this->context = $context;
        $this->JWTManager = $JWTManager;
        $this->helpCategoryRepository = $helpCategoryRepository;
        $this->helpCategoryCrudService = $helpCategoryCrudService;
    }

    public static function getEntityFqcn(): string
    {
        return HelpCategory::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud->overrideTemplate('crud/index', 'super_admin/helpCategory/app.html.twig');
    }

    public function configureResponseParameters(KeyValueStore $responseParameters): KeyValueStore
    {
        if (Crud::PAGE_INDEX === $responseParameters->get('pageName')) {
            $this->configureAppResponseParameters(
                $responseParameters,
                $this->settings,
                $this->context,
                $this->JWTManager
            );
        }

        return $responseParameters;
    }

    /**
     * @Rest\Get("help-categories/all")
     *
     * @return Response
     */
    public function getHelpCategories(Request $request)
    {
        return $this->executeSafe(function () {
            $request = $this->requestStack->getCurrentRequest();
            $content = json_decode($request->getContent(), true);
            $query = $content['query'] ?? null;
            $helpCategories = $this->helpCategoryRepository->getHelpCategories($query);

            return [
                'data' => $helpCategories,
            ];
        });
    }

    /**
     * @IsGranted("ROLE_ADMIN")
     *
     * @Rest\Get("help-categories/{id}")
     *
     * @return Response
     */
    public function getCategory(int $id, HelpCategoryRepository $helpCategoryRepository)
    {
        return $this->executeSafe(function () use ($helpCategoryRepository, $id) {
            $helpCategory = $helpCategoryRepository->find($id);

            if (!$helpCategory) {
                return $this->sendResponse([
                    'status' => Response::HTTP_NOT_FOUND,
                    'error' => true,
                    'data' => 'NOT_FOUND',
                ]);
            }

            return $this->helpCategoryCrudService->formatHelpCategoryStructure($helpCategory);
        });
    }

    /**
     * @IsGranted("ROLE_ADMIN")
     *
     * @Rest\Post("help-categories/create")
     */
    public function createHelpCategory(Request $request)
    {
        return $this->executeSafe(function () {
            $request = $this->requestStack->getCurrentRequest();
            $content = json_decode($request->getContent(), true);
            $helpCategory = new HelpCategory();
            $helpCategory->setName($content['name']);
            $helpCategory->setSort($content['sort'] ?? 0);
            $this->helpCategoryCrudService->setTranslations($helpCategory, $content['translations'] ?? []);
            $this->em->persist($helpCategory);
            $this->em->flush();

            return $this->helpCategoryCrudService->formatHelpCategoryStructure($helpCategory);
        }, [], [], Response::HTTP_CREATED);
    }

    /**
     * @IsGranted("ROLE_ADMIN")
     *
     * @Rest\Put("help-categories/update")
     *
     * @return Response
     */
    public function updateHelpCategory(HelpCategoryRepository $helpCategoryRepository)
    {
        return $this->executeSafe(function () use ($helpCategoryRepository) {
            $request = $this->requestStack->getCurrentRequest();
            $content = json_decode($request->getContent(), true);
            $id = $content['id'] ?? -1;
            $helpCategory = $helpCategoryRepository->find($id);

            if (!$helpCategory) {
                return $this->sendResponse([
                    'status' => Response::HTTP_ACCEPTED,
                    'error' => true,
                    'data' => 'NOT_FOUND',
                ]);
            }

            $helpCategory->setName($content['name']);
            $this->helpCategoryCrudService->setTranslations($helpCategory, $content['translations'] ?? []);
            $this->em->persist($helpCategory);
            $this->em->flush();

            return $this->helpCategoryCrudService->formatHelpCategoryStructure($helpCategory);
        });
    }

    /**
     * @IsGranted("ROLE_ADMIN")
     *
     * @Rest\Delete("help-categories/{id}")
     *
     * @return Response
     */
    public function deleteHelpCategory(HelpCategory $helpCategory)
    {
        if (!$helpCategory->getHelpTexts()->isEmpty()) {
            $locale = $this->getUser()?->getLocale() ?? $this->settings->get('app.adminDefaultLanguage');

            return $this->sendResponse([
                'status' => Response::HTTP_UNPROCESSABLE_ENTITY,
                'error' => true,
                'data' => $this->translator->trans(
                    id: 'message_api.help_category.delete_error',
                    parameters: ['%categoryName%' => $helpCategory->getName()],
                    domain: 'message_api',
                    locale: $locale
                ),
            ]);
        }

        $this->em->remove($helpCategory);
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_NO_CONTENT,
            'error' => false,
            'data' => null,
        ]);
    }

    /**
     * @IsGranted("ROLE_ADMIN")
     *
     * @Rest\Post("help-categories/update-order")
     *
     * @return Response
     */
    public function updateHelpCategoryOrder()
    {
        return $this->executeSafe(function () {
            $request = $this->requestStack->getCurrentRequest();
            $categoryOrder = json_decode($request->getContent(), true);

            $this->helpCategoryRepository->setHelpCategoryOrder($categoryOrder);
        });
    }
}
