<?php

declare(strict_types=1);

namespace App\Modules\Survey\Repository;

use App\Entity\Announcement;
use App\Entity\AnnouncementUser;
use App\Entity\Nps;
use App\Entity\NpsQuestion;
use App\Entity\User;
use App\Entity\UserCourse;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method Nps|null find($id, $lockMode = null, $lockVersion = null)
 * @method Nps|null findOneBy(array $criteria, array $orderBy = null)
 * @method Nps[]    findAll()
 * @method Nps[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class NpsModuleRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Nps::class);
    }

    public function createNps(?UserCourse $userCourse = null, NpsQuestion $question, array $params): Nps
    {
        $user = $params['user'];
        $answer = $params['answer'];
        $isPost = isset($params['isPost']) ? $params['isPost'] : false;

        $nps = new Nps();
        $nps->setCourse($userCourse);
        $nps->setQuestion($question);
        $nps->setUser($user);
        $nps->setCreatedBy($user);
        $nps->setUpdatedBy($user);
        $nps->setToPost($isPost);
        $nps->setMain($nps->getQuestion()->getMain());
        $nps->setType($answer['type'] ?? null);
        $this->determineAnswerValue($nps, $answer);

        if (isset($params['idAnnouncement'])) {
            $this->addAssessmentCall($nps, $user, $params['idAnnouncement']);
        }

        $this->getEntityManager()->persist($nps);

        return $nps;
    }

    private function determineAnswerValue(Nps $nps, array $answer): void
    {
        if (!isset($answer['value'])) {
            throw new \InvalidArgumentException('Missing required field value.');
        }
        switch ($answer['type']) {
            case NpsQuestion::TYPE_NPS:
                $nps->setValue($this->setValueNps($answer));

                return;

            case NpsQuestion::TYPE_SWITCH:
                $nps->setValue($this->setValueSwitch($answer));

                return;

            case NpsQuestion::TYPE_CHECKBOX:
                $nps->setValue($this->setValueCheckbox($answer));

                return;

            case NpsQuestion::TYPE_RADIO:
                $nps->setValue($this->setValueRadio($answer));

                return;

            default:
                $nps->setValue($answer['value']);
        }
    }

    private function setValueSwitch(array $answer): string
    {
        return 'on' == $answer['value'] ? '1' : '0';
    }

    private function setValueNps(array $answer): string
    {
        return (string) ($answer['value'] * 2);
    }

    private function setValueCheckbox(array $answer): string
    {
        return implode(',', $answer['value']);
    }

    private function setValueRadio(array $answer): string
    {
        return (string) $answer['value'];
    }

    private function addAssessmentCall(Nps $nps, User $user, int $announcementId): void
    {
        if (!$announcementId) {
            return;
        }

        $announcement = $this->getEntityManager()->getRepository(Announcement::class)->find($announcementId);

        if (!$announcement) {
            return;
        }

        $this->updateAnnouncementUser($user, $announcement);
        $nps->setAnnouncement($announcement);
    }

    private function updateAnnouncementUser(User $user, Announcement $announcement): void
    {
        $announcementUser = $this->getEntityManager()->getRepository(AnnouncementUser::class)
            ->findOneBy(['user' => $user, 'announcement' => $announcement]);

        if ($announcementUser) {
            $announcementUser->setValuedCourseAt(new \DateTimeImmutable());
            $this->getEntityManager()->persist($announcementUser);
        }
    }
}
