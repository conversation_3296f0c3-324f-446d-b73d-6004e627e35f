<?php

namespace App\Modules\CourseCategory\Controller;

use App\Entity\CourseCategory;
use App\Entity\Course;

use App\Repository\CourseCategoryRepository;

use App\Modules\Common\Controller\BaseVueController;
use App\Modules\CourseCategory\Services\CourseCategoryCrudService;

use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Contracts\Translation\TranslatorInterface;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Config\KeyValueStore;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;

use FOS\RestBundle\Controller\Annotations as Rest;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTManager;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;



/**
 * @Route("/admin/")
 */
class CourseCategoryCrudController extends BaseVueController
{
    private AdminContextProvider $context;
    private JWTManager $JWTManager;
    private CourseCategoryCrudService $courseCategoryCrudService;
    private CourseCategoryRepository $courseCategoryRepository;

    public function __construct(
        AdminContextProvider $context,
        JWTManager $JWTManager,
        SettingsService $settings,
        CourseCategoryCrudService $courseCategoryCrudService,
        EntityManagerInterface $em,
        LoggerInterface $logger,
        RequestStack $requestStack,
        TranslatorInterface $translator,
        CourseCategoryRepository $courseCategoryRepository

    ) {

        parent::__construct($settings, $em, $logger, $requestStack, $translator);

        $this->context = $context;
        $this->JWTManager = $JWTManager;
        $this->courseCategoryRepository = $courseCategoryRepository;
        $this->courseCategoryCrudService = $courseCategoryCrudService;
    }

    public static function getEntityFqcn(): string
    {
        return CourseCategory::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud->overrideTemplate('crud/index', 'super_admin/category/app.html.twig');
    }

    public function configureResponseParameters(KeyValueStore $responseParameters): KeyValueStore
    {
        if (Crud::PAGE_INDEX ===  $responseParameters->get('pageName')) {
            $this->configureAppResponseParameters(
                $responseParameters,
                $this->settings,
                $this->context,
                $this->JWTManager,
                [],
            );
        }
        return $responseParameters;
    }

    /**
     * @Rest\Get("course-categories/all")
     * @param Request $request
     * @return Response
     */
    public function getAllCourseCategories(Request $request)
    {
        return $this->executeSafe(function () {
            $request = $this->requestStack->getCurrentRequest();
            $content = json_decode($request->getContent(), true);
            $query = $content['query'] ?? null;
            $courseCategories = $this->courseCategoryRepository->getCourseCategoryByQuery($query);

            return [
                'data' => $this->courseCategoryCrudService->getCourseCategoryByLocale($courseCategories),
            ];
        });
    }

    /**
     * @IsGranted("ROLE_ADMIN")
     * @Rest\Get("course-categories/{id}")
     * @param int $id
     * @param CourseCategoryRepository $courseCategoryRepository
     * @return Response
     */
    public function getCategory(int $id, CourseCategoryRepository $courseCategoryRepository)
    {
        return $this->executeSafe(function () use ($courseCategoryRepository, $id) {
            $courseCategory = $courseCategoryRepository->find($id);

            if (!$courseCategory) return $this->sendResponse([
                'status' => Response::HTTP_NOT_FOUND,
                'error' => true,
                'data' => 'NOT_FOUND'
            ]);

            return $this->courseCategoryCrudService->formatCourseCategoryStructure($courseCategory);
        });
    }

    /**
     * @IsGranted("ROLE_ADMIN")
     * @Rest\Get("course-categories/{id}/courses-to-order")
     * @param CourseCategory $courseCategory
     * @return Response
     */
    public function getAllCourses(CourseCategory $courseCategory)
    {
        return $this->executeSafe(function () use ($courseCategory) {
            return $this->courseCategoryCrudService->getCoursesToOrder($courseCategory);
        });
    }

    /**
     * @IsGranted("ROLE_ADMIN")
     * @Rest\Post("course-categories/create")
     * @param Request $request
     */
    public function createCourseCategory(Request $request)
    {
        return $this->executeSafe(function () {
            $request = $this->requestStack->getCurrentRequest();
            $content = json_decode($request->getContent(), true);
            $courseCategory = new CourseCategory(); 

            if (($result = $this->courseCategoryCrudService->setCourseCategoryData($courseCategory, $content)) instanceof Response) return $result;

            return $this->courseCategoryCrudService->formatCourseCategoryStructure($courseCategory);
        }, [], [], Response::HTTP_CREATED);
    }


    /**
     * @IsGranted("ROLE_ADMIN")
     * @Rest\Put("course-categories/update")
     * @param Request $request
     * @param CourseCategoryRepository $courseCategoryRepository
     * @return Response
     */
    public function updateCourseSection(CourseCategoryRepository $courseCategoryRepository)
    {
        return $this->executeSafe(function () use ($courseCategoryRepository) {
            $request = $this->requestStack->getCurrentRequest();
            $content = json_decode($request->getContent(), true);
            $id = $content['id'] ?? -1;
            $courseCategory = $courseCategoryRepository->find($id);

            if (!$courseCategory) return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'NOT_FOUND'
            ]);

            if (($result = $this->courseCategoryCrudService->setCourseCategoryData($courseCategory, $content)) instanceof Response) return $result;

            return $this->courseCategoryCrudService->formatCourseCategoryStructure($courseCategory);
        }, [], [], Response::HTTP_OK);
    }

    /**
     * @IsGranted("ROLE_ADMIN")
     * @Rest\Delete("course-categories/{id}/delete")
     * @param CourseCategory $courseCategory
     * @return Response
     */
    public function deleteCourseCategory(CourseCategory $courseCategory)
    {
        if ($this->hasAssociatedCourses($courseCategory)) {
            return $this->categoryDeletionConflictResponse($courseCategory);
        }

        return $this->deleteCategory($courseCategory);
    }

    private function hasAssociatedCourses(CourseCategory $courseCategory): bool
    {
        $courses = $this->em->getRepository(Course::class)->findOneBy(['category' => $courseCategory]);
        return $courses !== null;
    }

    private function categoryDeletionConflictResponse(CourseCategory $courseCategory)
    {
        return $this->executeSafe(function () use ($courseCategory) {
            return [
                'message' => "No se puede eliminar la categoría {$courseCategory->getName()}, tiene cursos vinculados"
            ];
        }, [], [], Response::HTTP_CONFLICT);
    }

    private function deleteCategory(CourseCategory $courseCategory)
    {
        return $this->executeSafe(function () use ($courseCategory) {
            $this->em->remove($courseCategory);
            $this->em->flush();

            return null;
        }, [], [], Response::HTTP_NO_CONTENT);
    }


    /**
     * @IsGranted("ROLE_ADMIN")
     * @Rest\Post("course-categories/update-order")
     * @param Request $request
     * @return Response
     */
    public function updateCourseCategoryOrder(Request $request)
    {
        return $this->executeSafe(function (): void {
            $request = $this->requestStack->getCurrentRequest();
            $categoryorder = json_decode($request->getContent(), true);

            $this->courseCategoryCrudService->setCourseCategoryOrder($categoryorder);
        }, [], [], Response::HTTP_OK);
    }

    /**
     * @IsGranted("ROLE_ADMIN")
     * @Rest\Post("course-categories/update-active/{id}")
     * @param CourseCategory $courseCategory
     * @return Response
     */
    public function updateCourseCategoryActive(CourseCategory $courseCategory)
    {
        return $this->executeSafe(function () use ($courseCategory): void {
            $courseCategory->setActive(!$courseCategory->isActive());
            $this->em->persist($courseCategory);
            $this->em->flush();
        }, [], [], Response::HTTP_OK);
    }
}
