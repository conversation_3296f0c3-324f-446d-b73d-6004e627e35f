<?php

namespace App\Modules\Announcement\Controller;

use App\Admin\Traits\SerializerTrait;
use App\Entity\Announcement;
use App\Entity\User;
use App\Service\Diploma\DiplomaService;
use FOS\RestBundle\Controller\Annotations as Rest;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * @Route("/admin/")
 */
class DiplomaController extends AbstractController
{
    use SerializerTrait;

    private $diplomaService;

    public function __construct(
        DiplomaService $diplomaService
    ) {
        $this->diplomaService = $diplomaService;
    }


    /**
     * @Route("download-diploma-user/{user}/announcement/{announcement}", name="download-diploma-user", methods={"GET"})
     */
    public function downloadDiplomaUser(User $user, Announcement $announcement)
    {
        try {
            $output = $this->diplomaService->generateUserRequestedDiploma($announcement, $user);

            $response = [
                'status' => 200,
                'error'  => false,
                'data'   => $output['diploma'],
                'nombre' => $output['nombre']
            ];
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' =>  'Ha ocurrido un error al descargar el pdf' . ' {' . $e->getMessage() . '}'
            ];
        }

        return  $this->sendResponse($response);
    }
}
