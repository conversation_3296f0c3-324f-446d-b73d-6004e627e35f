<?php

declare(strict_types=1);

namespace App\Controller\Roleplay;

use Doctrine\Common\Annotations\AnnotationReader;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\Encoder\JsonEncoder;
use Symfony\Component\Serializer\Exception\ExceptionInterface;
use Symfony\Component\Serializer\Mapping\Factory\ClassMetadataFactory;
use Symfony\Component\Serializer\Mapping\Loader\AnnotationLoader;
use Symfony\Component\Serializer\NameConverter\MetadataAwareNameConverter;
use Symfony\Component\Serializer\Normalizer\DateTimeNormalizer;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;
use Symfony\Component\Serializer\Serializer;

class BaseController extends AbstractController
{
    protected EntityManagerInterface $em;
    protected RequestStack $requestStack;
    protected ?Request $request;

    public function __construct(EntityManagerInterface $entityManager, RequestStack $requestStack)
    {
        $this->em = $entityManager;
        $this->requestStack = $requestStack;
        $this->request = $requestStack->getCurrentRequest();
    }

    protected function sendResponse($response, $context = []): Response
    {
        return new Response(
            $this->serialize($response, $context),
            $response['status'],
            ['content-type' => 'application/json']
        );
    }

    public function serialize($response, $context = []): string
    {
        if (empty($context)) {
            $normalizer = new ObjectNormalizer();
        } else {
            $classMetadataFactory = new ClassMetadataFactory(new AnnotationLoader(new AnnotationReader()));
            $metadataAwareNameConverter = new MetadataAwareNameConverter($classMetadataFactory);
            $normalizer = new ObjectNormalizer($classMetadataFactory, $metadataAwareNameConverter);
        }

        $serializer = new Serializer(
            [new DateTimeNormalizer(['datetime_format' => 'Y-m-d H:i:s']), $normalizer],
            [new JsonEncoder()]
        );

        return $serializer->serialize($response, 'json', $context);
    }

    /**
     * @throws ExceptionInterface
     */
    public function normalize($object, $context = [])
    {
        if (empty($context)) {
            $normalizer = new ObjectNormalizer();
        } else {
            $classMetadataFactory = new ClassMetadataFactory(new AnnotationLoader(new AnnotationReader()));
            $metadataAwareNameConverter = new MetadataAwareNameConverter($classMetadataFactory);
            $normalizer = new ObjectNormalizer($classMetadataFactory, $metadataAwareNameConverter);
        }
        $serializer = new Serializer(
            [new DateTimeNormalizer(['datetime_format' => 'Y-m-d H:i:s']), $normalizer],
            [new JsonEncoder()]
        );

        return $serializer->normalize($object, null, $context);
    }
}
