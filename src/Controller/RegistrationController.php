<?php

namespace App\Controller;

use App\Entity\User;
use App\Entity\RecoveryCode;
use App\Form\RegistrationFormType;
use App\Admin\Traits\SerializerTrait;
use App\Entity\AnnouncementUser;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Mailer\MailerInterface;
use App\Repository\UserRepository;
use App\Service\SettingsService;
use App\Utils\EncryptionUtils;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\Annotations as Rest;

class RegistrationController extends AbstractController
{
    private $mailer;
    private $userRepository;
    private $em;
    private $settings;

    use SerializerTrait;

    public function __construct(MailerInterface $mailer, UserRepository $userRepository, EntityManagerInterface $em, SettingsService $settings)
    {
        $this->mailer = $mailer;
        $this->userRepository = $userRepository;
        $this->em = $em;
        $this->settings = $settings;
    }
    /**
     * @Route("/register", name="app_register")
     */
    public function register(Request $request, UserPasswordHasherInterface $passwordEncoder): Response
    {
        $user = new User();
        $form = $this->createForm(RegistrationFormType::class, $user);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // encode the plain password
            $user->setPassword(
                $form->get('password')->getData()
            );

            $entityManager = $this->getDoctrine()->getManager();
            $entityManager->persist($user);
            $entityManager->flush();
            // do anything else you need here, like send an email

            return $this->redirectToRoute('welcome');
        }

        return $this->render('registration/register.html.twig', [
            'registrationForm' => $form->createView(),
        ]);
    }


    /**
     * @Rest\Get("/activeAccount/{id}/{hash}", name="active-account")
     */
    public function activeAccount(User $user, $hash)
    {

        $user = $this->em->getRepository(User::class)->find($user);
        //Verificar el estado del hash
        $recoveryCode = $this->em->getRepository(RecoveryCode::class)->findOneBy([
            'user' => $user,
            'codeActivation' => $hash
        ]);

        $user->setIsActive(1);
        $recoveryCode->setState(1);
        $this->em->persist($user, $recoveryCode);
        $this->em->flush();

        return $this->render(
            'registration/active_account.html.twig',
            [
                'user' => $user,
                'hash' => $hash,
                'recoveryCode' => $recoveryCode
            ]
        );
    }

    /**
     * @Route("/changeStateAccount", name="change_state_account",methods={"POST"})
     */
    public function changeStateAccount(Request $request)
    {
        return $this->redirect('/campus/');
    }


    /**
     * @Rest\Get("register-assistance-announcement/{idUserHash}/{confirmation}", name="register-assistance-announcement")
     */
    public function registerAssistanceToAnnouncement($idUserHash, $confirmation)
    {
        $encriptionKey = $this->settings->get('app.fundae.encryption_key');
        $encriptionUtil = new EncryptionUtils($encriptionKey);

        $idUserAnnouncement = $encriptionUtil->decrypt($idUserHash);
        $announcementUser = $this->em->getRepository(AnnouncementUser::class)->find($idUserAnnouncement);

        $result = $this->saveConfirmationAssistance($announcementUser, $confirmation);

        return $this->render('announcement/register-assistance.html.twig', [
            'idUserHash' => $idUserHash,
            'announcementUser' => $announcementUser,
            'confirmation' => $result
        ]);
    }

    private function saveConfirmationAssistance(AnnouncementUser $announcementUser, $confirmation)
    {
        $now = new \DateTimeImmutable();
        $finishAnnouncement = $announcementUser->getAnnouncement()->getFinishAt();

        $isConfirmation = ($confirmation == 'yes');

        if ($now <= $finishAnnouncement) {
            $announcementUser->setIsConfirmationAssistance($isConfirmation);
            $announcementUser->setDateConfirmationAssistance(new \DateTimeImmutable());
            $this->em->persist($announcementUser);
            $this->em->flush();

            return $confirmation;
        }

        return 'outoftime';
    }
}
