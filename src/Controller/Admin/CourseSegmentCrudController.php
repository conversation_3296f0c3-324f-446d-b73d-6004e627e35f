<?php

namespace App\Controller\Admin;

use App\Entity\CourseSegment;
use App\Admin\Traits\SerializerTrait;
use App\Admin\Field\TranslationField;
use App\Entity\CourseSegmentCategory;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Field\IdField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Config\KeyValueStore;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Translation\TranslatorInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Psr\Log\LoggerInterface;

class CourseSegmentCrudController extends AbstractCrudController
{

    use SerializerTrait;
    private $em;
    private $requestStack;
    private $logger;
    protected $translator;
    private $context;
    private $settings;


    public function __construct(EntityManagerInterface $em, RequestStack $requestStack, LoggerInterface $logger, AdminContextProvider $context, TranslatorInterface $translator, SettingsService $settings)
    {
        $this->em =             $em;
        $this->requestStack =   $requestStack;
        $this->logger =         $logger;
        $this->context = $context;
        $this->translator = $translator;
        $this->settings = $settings;
    }

    public static function getEntityFqcn(): string
    {
        return CourseSegment::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular($this->translator->trans('course_segmente.label_in_singular', [], 'messages', $this->getUser()->getLocale()))
            ->setEntityLabelInPlural($this->translator->trans('course_segmente.label_in_plural', [], 'messages', $this->getUser()->getLocale()))
            ->addFormTheme('@FOSCKEditor/Form/ckeditor_widget.html.twig')
            ->setSearchFields(['id', 'title', 'description', 'image'])
            ->addFormTheme('@A2lixTranslationForm/bootstrap_4_layout.html.twig');

    }

    public function configureFields(string $pageName): iterable
    {

        $id   = IdField::new('id', '#');
        $name = TextField::new('name', $this->translator->trans('filter.configureFields.name', [], 'messages', $this->getUser()->getLocale()));

        $translations = TranslationField::new('translations', $this->translator->trans('help_category.configureFields.translations', [], 'messages', $this->getUser()->getLocale()), [
            'name' => [
                'label'      => $this->translator->trans('filter.configureFields.name', [], 'messages', $this->getUser()->getLocale()),
            ],
        ]);


        if (Crud::PAGE_INDEX === $pageName) {
            return [$id, $name];
        } elseif (Crud::PAGE_DETAIL === $pageName) {
            return [$id, $name];
        } elseif (Crud::PAGE_NEW === $pageName) {
            return [$name];
        } elseif (Crud::PAGE_EDIT === $pageName && $this->settings->get('app.multilingual') == true) {
            return [$name, $translations];
        } elseif (Crud::PAGE_EDIT === $pageName) {
            return [$name];
        }
    }

    public function configureResponseParameters(KeyValueStore $responseParameters): KeyValueStore
    {

        if ($this->requestStack->getCurrentRequest()->get('courseSegmentCategoryId')) {
            $responseParameters->set('courseSegmentCategory', $this->em->getRepository(CourseSegmentCategory::class)->find($this->requestStack->getCurrentRequest()->get('courseSegmentCategoryId')));
        }

        if (Crud::PAGE_DETAIL === $responseParameters->get('pageName')) {

            $courseSegment = $this->em->getRepository(CourseSegment::class);
            $entity            = $this->context->getContext()->getEntity();
            $courseSegment           = $courseSegment->find($entity->getPrimaryKeyValue());

            $courseSegmentCategory = $this->em->getRepository(CourseSegmentCategory::class);
            $courseSegmentCategory = $courseSegmentCategory->find([
                'id' => $courseSegment->getId()
            ]);

            $courseSegmentAll = $courseSegment->findBy([
                'courseSegmentCategory' => $courseSegmentCategory
            ]);


            $adminUrlGenerator = $this->get(AdminUrlGenerator::class);
            $referrer = $adminUrlGenerator
                ->unsetAll()
                ->setController(CourseSegmentCrudController::class)
                ->setAction('detail')
                ->setEntityId($courseSegmentCategory->getId())
                ->generateUrl();

            $responseParameters->set('courseSegmentCategory', $courseSegmentCategory);
            $responseParameters->set('courseSegment', $courseSegmentAll);
            $responseParameters->set('referrer', $referrer);
        }


        return $responseParameters;
    }

    public function createEntity(string $entityFqc)
    {
        $courseSegment = new CourseSegment();

        $courseSegmentRepository = $this->em->getRepository(CourseSegmentCategory::class);
        $courseSegmentCategory           = $courseSegmentRepository->find($this->requestStack->getCurrentRequest()->get('courseSegmentCategoryId'));

        $courseSegment->setCourseSegmentCategory($courseSegmentCategory);

        return $courseSegment;
    }
}
