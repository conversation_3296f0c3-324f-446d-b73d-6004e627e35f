<?php

namespace App\Controller\Admin;

use App\Admin\Traits\SerializerTrait;
use App\Admin\Traits\DeleteFilePathTrait;
use App\Entity\Categorize;
use App\Entity\CategorizeAnswers;
use App\Entity\CategorizeOptions;
use App\Entity\Chapter;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Contracts\Translation\TranslatorInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class CategorizeOptionsCrudController extends AbstractCrudController
{
	use DeleteFilePathTrait, SerializerTrait;

	private   $em;
	private   $requestStack;
	private   $logger;
	private   $context;
	protected $translator;
	protected $adminUrlGenerator;

	public function __construct(EntityManagerInterface $em, RequestStack $requestStack, LoggerInterface $logger, AdminContextProvider $context, TranslatorInterface $translator, AdminUrlGenerator $adminUrlGenerator)
	{
		$this->em = $em;
		$this->requestStack = $requestStack;
		$this->logger = $logger;
		$this->context = $context;
		$this->translator = $translator;
		$this->adminUrlGenerator = $adminUrlGenerator;
	}

	public static function getEntityFqcn(): string
	{
		return CategorizeOptions::class;
	}

	/**
	 * @Route("/admin/categorize-options/new", name="admin_categorize_options_new", methods={"POST"})
	 */
	public function newCategorize(Request $request)
	{
		try {
			$chapterId = $request->get('chapter');
			$image = $request->files->get('image');
			$name = $request->get('name');
			$chapter = $this->em->getRepository(Chapter::class)->find($chapterId);


			if ($name != '') {
				$categorizeOptionsSearch = $this->em->getRepository(CategorizeOptions::class)->findOneBy(
					['chapter' => $chapterId, 'name' => $name],

				);

				if (!$categorizeOptionsSearch) {
					$entity = new CategorizeOptions();
					$entity->setChapter($chapter);
					$entity->setName($name);

					if ($image != null) {
						$entity->setImage('');
						$entity->setImageFile($image);
					}
					$this->em->persist($entity);
					$this->em->flush();
				}
			}

			$categorizeOptions = $this->em->getRepository(CategorizeOptions::class)->findBy(['chapter' => $chapterId]);

			$response = [
				'status' => Response::HTTP_OK,
				'error'  => false,
				'data'   => $categorizeOptions,
			];
		} catch (\Exception $e) {
			$response = [
				'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
				'error'  => true,
				'data'   => 'Error to delete question: {' . $e->getMessage() . '}'
			];
		}

		return $this->sendResponse($response, array('groups' => array('categorizeOptions', 'detail')));
	}

	/**
	 * @Route("/admin/categorize-options/{id}", name="admin_categorize_options", methods={"GET"})
	 */
	public function fetchCategorizeOptions(Chapter $chapter)
	{
		try {
			$categorizeOptions = $this->em->getRepository(CategorizeOptions::class)->findBy(['chapter' => $chapter->getId()]);

			$response = [
				'status' => Response::HTTP_OK,
				'error'  => false,
				'data'   => $categorizeOptions,
			];
		} catch (\Exception $e) {
			$response = [
				'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
				'error'  => true,
				'data'   => 'Error to get categorize options: {' . $e->getMessage() . '}'
			];
		}

		return $this->sendResponse($response, array('groups' => array('categorizeOptions', 'detail')));
	}

	/**
	 * @Route("/admin/categorize-options/edit", name="admin_categorize_options_edit", methods={"POST"})
	 */
	public function editCategorizeOptions(Request  $request)
	{
		try {
			$id = $request->get('id');
			$name = $request->get('name');
			$image = $request->files->get('image');
			$chapterId = $request->get('chapter');

			$categorizeOption = $this->em->getRepository(CategorizeOptions::class)->find($id);
			$categorizeOption->setName($name);

			if ($image != null) {
				if (!is_null($categorizeOption->getImage())) $this->deleteFile($this->getParameter('app.gamecategorize_options_uploads_path'), $categorizeOption->getImage());
				$categorizeOption->setImage('');
				$categorizeOption->setImageFile($image);
			}
			$this->em->persist($categorizeOption);
			$this->em->flush();

			$categorizeOptions = $this->em->getRepository(CategorizeOptions::class)->findBy(['chapter' => $chapterId]);

			$response = [
				'status' => Response::HTTP_OK,
				'error'  => false,
				'data'   => $categorizeOptions,
			];
		} catch (\Exception $e) {
			$response = [
				'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
				'error'  => true,
				'data'   => 'Error to get categorize options: {' . $e->getMessage() . '}'
			];
		}

		return $this->sendResponse($response, array('groups' => array('categorizeOptions', 'detail')));
	}

	/**
	 * @Route("/admin/categorize-options/delete", name="admin_categorize_options_delete", methods={"POST"})
	 */
	public function deleteCategorizeOption(Request $request)
	{
		try {
			$id = $request->get('id');
			$chapterId = $request->get('chapter');
			$error = false;

			$message = "Registro eliminado correctamente";

			$categorizeOption = $this->em->getRepository(CategorizeOptions::class)->find($id);
			$categorizeOptions = $this->em->getRepository(CategorizeOptions::class)->findBy(['chapter' => $chapterId]);


			if (count($categorizeOption->getCategorizeAnswers()) == 0) {
				$this->em->remove($categorizeOption);
				$this->deleteCategorizeAnswers($categorizeOption);
				$this->em->flush();
			} else {
				$error = true;
				$message = "La opción no se puede eliminar porque está siendo utilizada en una pregunta.";

				$response = [
					'status' => 409,
					'error'  => $error,
					'message' => 'La opción no se puede eliminar porque está siendo utilizada en una pregunta.',
					'data'   => [],
				];

				return $this->sendResponse($response, array('groups' => array('categorizeOptions', 'detail')));
			}



			$response = [
				'status' => Response::HTTP_OK,
				'error'  => false,
				'data'   => $categorizeOptions,
				'message' => $message
			];
		} catch (\Exception $e) {
			$response = [
				'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
				'error'  => $error,
				'message' => "Ocurrio error del servidor",
				'data'   => 'Error to get categorize options: {' . $e->getMessage() . '}'
			];
		}

		return  $this->sendResponse($response, array('groups' => array('categorizeOptions', 'detail')));
	}

	private function deleteCategorizeAnswers(CategorizeOptions $categorize)
	{
		$categorizeAnswers = $this->em->getRepository(CategorizeAnswers::class)->findBy(['options' => $categorize->getId()]);

		foreach ($categorizeAnswers as $categorizeAnswer) {
			$this->em->remove($categorizeAnswer);
		}
	}


	/**
	 * @Route("/admin/categorize/new", name="admin_categorize_new", methods={"POST"})
	 */
	public function newCategorizeChapter(Request $request)
	{
		try {
			$idChapter = $request->get('idChapter');
			$question = $request->get('question');
			$image = $request->files->get('image');
			$answers = json_decode($request->get('answers'), true);
			$time = $request->get('time');

			$chapter = $this->em->getRepository(Chapter::class)->find($idChapter);

			$categorize = new Categorize();
			$categorize->setChapter($chapter);
			$categorize->setQuestion($question);
			$categorize->setTime($time);

			if ($image != null) {
				$categorize->setImage('');
				$categorize->setImageFile($image);
			}

			$this->em->persist($categorize);

			foreach ($answers as $answer) {
				$categorizeOption = $this->em->getRepository(CategorizeOptions::class)->find($answer['id']);
				$categorizeAnswer = new CategorizeAnswers();
				$categorizeAnswer->setCategorize($categorize);
				$categorizeAnswer->setOptions($categorizeOption);
				$categorizeAnswer->setCorrect($answer['selected']);
				$this->em->persist($categorizeAnswer);
			}

			$this->em->flush();

			$categorize = $this->em->getRepository(Categorize::class)->findBy(['chapter' => $idChapter]);

			$response = [
				'status' => Response::HTTP_OK,
				'error'  => false,
				'data'   => $categorize,
			];
		} catch (\Exception $e) {
			$response = [
				'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
				'error'  => true,
				'data'   => 'Error to get categorize: {' . $e->getMessage() . '}'
			];
		}

		return $this->sendResponse($response, array('groups' => array('categorize', 'categorizeOptions', 'detail')));
	}

	/**
	 * @Route("/admin/categorize/{id}", name="admin_categorize_get", methods={"GET"})
	 */
	public function fetchCategorizes(Chapter $chapter)
	{
		try {
			$categorize = $this->em->getRepository(Categorize::class)->findBy(['chapter' => $chapter->getId()]);

			$response = [
				'status' => Response::HTTP_OK,
				'error'  => false,
				'data'   => $categorize,
				'route' => $this->urlChapter($chapter->getId())
			];
		} catch (\Exception $e) {
			$response = [
				'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
				'error'  => true,
				'data'   => 'Error to get categorize: {' . $e->getMessage() . '}'
			];
		}

		return $this->sendResponse($response, array('groups' => array('categorize', 'categorizeOptions', 'detail')));
	}

	private function urlChapter($chapterId)
	{
		return $this->adminUrlGenerator
			->unsetAll()
			->setController(ChapterCrudController::class)
			->setAction('edit')
			->setEntityId($chapterId)
			->generateUrl();
	}

	/**
	 * @Route("/admin/categorize/edit", name="admin_categorize_edit", methods={"POST"})
	 */
	public function editCategorize(Request  $request)
	{
		try {
			$id = $request->get('id');
			$question = $request->get('question');
			$image = $request->files->get('image');
			$answers = json_decode($request->get('answers'), true);
			$answersDelete = json_decode($request->get('answersDeleted'), true);
			$time = $request->get('time');

			$categorize = $this->em->getRepository(Categorize::class)->find($id);

			$categorize->setQuestion($question);
			$categorize->setTime($time);

			if ($image != null) {
				if (!is_null($categorize->getImage())) $this->deleteFile($this->getParameter('app.gameCategorize_uploads_path'), $categorize->getImage());
				$categorize->setImage('');
				$categorize->setImageFile($image);
			}

			$this->em->persist($categorize);

			foreach ($answers as $answer) {
				$idAnswer = json_decode($answer['id'], true);
				$categorizeAnswer = $this->em->getRepository(CategorizeAnswers::class)->find(['id' => $idAnswer]);
				$categorizeAnswer->setCorrect($answer['correct']);
				$this->em->persist($categorizeAnswer);
			}

			foreach ($answersDelete as $answer) {
				$idAnswer = json_decode($answer['id'], true);
				$categorizeAnswer = $this->em->getRepository(CategorizeAnswers::class)->find(['id' => $idAnswer]);
				$this->em->remove($categorizeAnswer);
			}


			$this->em->flush();

			$categorize = $this->em->getRepository(Categorize::class)->findBy(['chapter' => $categorize->getChapter()->getId()]);

			$response = [
				'status' => Response::HTTP_OK,
				'error'  => false,
				'data'   => $categorize,
			];
		} catch (\Exception $e) {
			$response = [
				'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
				'error'  => true,
				'data'   => 'Error to get categorize: {' . $e->getMessage() . '}'
			];
		}

		return $this->sendResponse($response, array('groups' => array('categorize', 'categorizeOptions', 'detail')));
	}

	/**
	 * @Route("/admin/categorize/delete", name="admin_categorize_delete", methods={"POST"})
	 */
	public function deleteCategorize(Request $request)
	{
		try {
			$id = $request->get('id');
			$categorize = $this->em->getRepository(Categorize::class)->find($id);
			$chapterId = $categorize->getChapter()->getId();
			$this->em->remove($categorize);

			$categorize = $this->em->getRepository(Categorize::class)->findBy(['chapter' => $chapterId]);

			$categorizeAnswers = $this->em->getRepository(CategorizeAnswers::class)->findBy(['categorize' => $id]);

			foreach ($categorizeAnswers as $categorizeAnswer) {
				$this->em->remove($categorizeAnswer);
			}
			$this->em->flush();


			$response = [
				'status' => Response::HTTP_OK,
				'error'  => false,
				'data'   => $categorize,
			];
		} catch (\Exception $e) {
			$response = [
				'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
				'error'  => true,
				'data'   => 'Error to get categorize: {' . $e->getMessage() . '}'
			];
		}

		return $this->sendResponse($response, array('groups' => array('categorize', 'categorizeOptions', 'detail')));
	}
}
