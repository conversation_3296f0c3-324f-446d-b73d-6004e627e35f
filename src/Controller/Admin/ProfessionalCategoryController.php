<?php

namespace App\Controller\Admin;

use App\Admin\Traits\SerializerTrait;
use App\Entity\Course;
use App\Entity\ProfessionalCategory;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class ProfessionalCategoryController extends AbstractController
{
    use SerializerTrait;

    private $em;
    private $requestStack;
    private $logger;


    public function __construct (EntityManagerInterface $em, RequestStack $requestStack, LoggerInterface $logger)
    {
        $this->em           = $em;
        $this->requestStack = $requestStack;
        $this->logger       = $logger;
    }


    /**
     * @Route ("/admin/professional-categories/list", name="admin_professional_categories_list")
     * @param Request $request
     * @return Response
     */
    public function getProfessionalCategories(Request $request)
    {
        $professionalCategories = $this->em->getRepository(ProfessionalCategory::class)->findAll();

        $response = [
            'status' => Response::HTTP_OK,
            'error'  => false,
            'data'   => ['professionalCategories' => $professionalCategories],
        ];
        return $this->sendResponse($response, ['groups' => 'admin_area']);
    }


    /**
     * @Route ("/admin/professional-categories/course/{id}", name="admin_professional_categories_course")
     * @param Course $course
     * @return Response
     */
    public function getCourseProfessionalCategories(Course $course)
    {
        $response = [
            'status' => Response::HTTP_OK,
            'error'  => false,
            'data'   => ['professionalCategories' => $course->getCategories()],
        ];
        return $this->sendResponse($response, ['groups' => 'admin_area']);
    }
}
