<?php

namespace App\Controller\Admin;

use App\Admin\Traits\SerializerTrait;
use App\Entity\Setting;
use App\Repository\SettingGroupRepository;
use App\Repository\SettingRepository;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTManager;
use Psr\Log\LoggerInterface;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;


/**
 * @IsGranted("ROLE_SUPER_ADMIN")
 */
class SettingsController extends AbstractController
{
    use SerializerTrait;

    private SettingsService $settings;
    private EntityManagerInterface $em;
    private AdminContextProvider $context;
    private LoggerInterface $logger;
    private JWTManager $jwt;
    protected TranslatorInterface $translator;
    protected RequestStack $request;
    protected AdminUrlGenerator $adminUrlGenerator;


    public function __construct(
        SettingsService  $settings,
        EntityManagerInterface $em,
        AdminContextProvider   $context,
        RequestStack           $request,
        AdminUrlGenerator      $adminUrlGenerator
    ) {
        $this->settings            = $settings;
        $this->em                = $em;
        $this->context           = $context;
        $this->request           = $request;
        $this->adminUrlGenerator = $adminUrlGenerator;
    }


    /**
     * @Route("/admin/settings", name="admin_settings")
     * @param SettingRepository $settingRepository
     * @return Response
     */
    public function index(SettingRepository $settingRepository): Response
    {
        $settings = $settingRepository->findAllByCode();

        return $this->render('admin/settings/general/index.html.twig', [
            'settings' => $settings,
        ]);
    }


    /**
     * @Route("/admin/settings/list", name="admin_settings_list")
     * @param SettingGroupRepository $settingGroupRepository
     * @return Response
     */
    public function getList(SettingGroupRepository $settingGroupRepository): Response
    {
        $groups = $settingGroupRepository->findBy([], ['sort' => 'ASC']);
        $timezonesSetting = $this->em->getRepository(Setting::class)->findOneBy(['code' => 'app.timezones']);
        $selectedTimezones = [];
        if ($timezonesSetting) {
            $value = $timezonesSetting->getValue();
            if (!empty($value)) $selectedTimezones = json_decode($value, true);
        }

        $response = [
            'status' => Response::HTTP_OK,
            'error'  => false,
            'data'   => [
                'groups' => $groups,
                'timezones' => \DateTimeZone::listIdentifiers(),
                'selectedTimezones' => $selectedTimezones
            ],
        ];

        return $this->sendResponse($response, ['groups' => ['setting:list']]);
    }


    /**
     * @Route("/admin/settings/save", name="admin_settings_save")
     * @param SettingRepository $settingRepository
     * @param Request $request
     * @return Response
     */
    public function save(SettingRepository $settingRepository, SettingGroupRepository $settingGroupRepository, Request $request): Response
    {
        $data = json_decode($request->getContent(), true);

        $settings = $data['settings'];
        $timezones = $data['timezones'] ?? [];

        if (!empty($settings)) {
            foreach ($settings as $item) {
                $setting = $settingRepository->findOneBy(['code' => $item['code']]);

                if ($setting) {
                    $setting->setValue($item['value']);
                }
            }
        }

        $timezonesCode = $settingRepository->findOneBy([
            'code' => 'app.timezones'
        ]);
        if (!$timezonesCode) {
            $timezonesCode = new Setting();
            $timezonesCode->setCode('app.timezones')
                ->setType('json')
                ->setName('Zonas Horarias')
                ->setSettingGroup($settingGroupRepository->find(1))
                ->setSort(10)
            ;
        }
        $timezonesCode->setValue(json_encode($timezones));
        $this->em->persist($timezonesCode);
        $this->em->flush();

        $response = [
            'status' => Response::HTTP_OK,
            'error'  => false,
            'data'   => [],
        ];

        $this->em->flush();

        return $this->sendResponse($response);
    }


    /**
     * @Route("/admin/settings/info", name="admin_phpinfo")
     * @return Response
     */
    public function phpinfo(): Response
    {

        if ($this->container->has('profiler')) {
            $this->container->get('profiler')->disable();
        }

        ob_start();
        phpinfo();
        $phpinfo = ob_get_contents();
        ob_get_clean();

        $phpinfo = str_replace('background-color: #fff;', '', $phpinfo);

        return $this->render('admin/settings/phpinfo.html.twig', [
            'phpinfo' => $phpinfo,
        ]);
    }
}
