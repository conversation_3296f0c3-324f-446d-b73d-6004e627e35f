<?php

namespace App\Controller\Admin;

use App\Admin\Field\FosCkeditorField;
use App\Admin\Field\TranslationField;
use App\Entity\SectionDefaultFront;
use App\Entity\Setting;
use App\Service\SettingsService;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\Field;
use EasyCorp\Bundle\EasyAdminBundle\Field\IntegerField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use Symfony\Contracts\Translation\TranslatorInterface;

class SectionDefaultFrontCrudController extends AbstractCrudController
{
    private TranslatorInterface $translator;
    private SettingsService $settings;


    public function __construct(TranslatorInterface $translator, SettingsService $settings)
    {
        $this->translator = $translator;
        $this->settings = $settings;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular('Sections by default front')
            ->setEntityLabelInPlural('Sections by default front')
            ->addFormTheme('@FOSCKEditor/Form/ckeditor_widget.html.twig')
            ->addFormTheme('@A2lixTranslationForm/bootstrap_4_layout.html.twig');
    }

    public static function getEntityFqcn(): string
    {
        return SectionDefaultFront::class;
    }


    public function configureFields(string $pageName): iterable
    {
        $id = IntegerField::new('id', 'ID');

        $name = TextField::new('name', $this->translator->trans('course.configureFields.name', [], 'messages', $this->getUser()->getLocale()));

        $description = FosCkeditorField::new('description', $this->translator->trans('course.configureFields.description', [], 'messages', $this->getUser()->getLocale()))->setFormTypeOptions([
            'config_name' => 'basic',
        ]);

        $translations = TranslationField::new('translations', $this->translator->trans('question_nps.configureFields.translations', [], 'messages', $this->getUser()->getLocale()), [
            'name' => [
                'label'      => $this->translator->trans('question_nps.configureFields.name_question', [], 'messages', $this->getUser()->getLocale()),
            ],
            'description' => [                
                'label'      => $this->translator->trans('course.configureFields.description', [], 'messages', $this->getUser()->getLocale()),
                
            ],
        ])
            ->setFormTypeOptions([
                'locales' => $this->settings->get('app.multilingual') ? $this->settings->get('app.languages') : ['en'],
            ]);

        $active = BooleanField::new('isActive', 'Activo');

        $open_visible = BooleanField::new('isOpenCourse', 'Agregar cursos del campus abierto');

        $idSection = IntegerField::new('idSection', 'Id Section');

        if (Crud::PAGE_INDEX === $pageName) {
            return [$id, $name, $active];
        }
        if (Crud::PAGE_EDIT === $pageName) {
            return [$name, $description, $translations, $active, $open_visible];
        }
    }


    public function configureActions(Actions $actions): Actions
    {
        return $actions
            ->remove(Crud::PAGE_INDEX, Action::DELETE)
            ->remove(Crud::PAGE_INDEX, Action::NEW);
    }
}
