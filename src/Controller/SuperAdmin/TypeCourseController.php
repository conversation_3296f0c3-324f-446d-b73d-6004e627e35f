<?php

declare(strict_types=1);

namespace App\Controller\SuperAdmin;

use App\Admin\Traits\SerializerTrait;
use App\Entity\AnnouncementConfigurationTypeTranslation;
use App\Entity\TypeCourseAnnouncementStepConfiguration;
use App\Entity\TypeCourseAnnouncementStepCreation;
use App\Entity\TypeCourseAnnouncementStepCreationTranslation;
use App\Enum\AnnouncementConfigurationType as AnnouncementConfigurationTypeEnum;
use App\Repository\TypeCourseAnnouncementStepCreationRepository;
use App\Service\Catalog\CatalogService;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\Annotations as Rest;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * @Route("/admin")
 */
class TypeCourseController extends AbstractController
{
    use SerializerTrait;

    private EntityManagerInterface $em;
    private CatalogService $catalogService;

    public function __construct(EntityManagerInterface $em, CatalogService $catalogService)
    {
        $this->em = $em;
        $this->catalogService = $catalogService;
    }

    private function handleException(\Exception $e): Response
    {
        $errorMessage = \sprintf('Error on line %d of %s: %s', $e->getLine(), $e->getFile(), $e->getMessage());

        return $this->sendResponse(['error' => true, 'status' => 500, 'message' => $errorMessage]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     *
     * @Rest\Get("/TypeCourse-AnnouncementStepCreation/all")
     */
    public function getAllTypeCourseAnnouncementStepCreation(): Response
    {
        $data = [];
        $typeCourseAnnouncementStepCreation = $this->em->getRepository(TypeCourseAnnouncementStepCreation::class)->findAll();
        foreach ($typeCourseAnnouncementStepCreation as $type) {
            $translations = [];
            /** @var TypeCourseAnnouncementStepCreationTranslation $translation */
            foreach ($type->getTranslations() as $translation) {
                $name = $translation->getName();
                $description = $translation->getDescription();
                $translations[] = [
                    'locale' => $translation->getLocale(),
                    'name' => $name,
                    'description' => $description,
                ];
            }
            // ************ falta el translate*/
            $userLocale = $this->getUser()->getLocale();
            $annStepConfig = [];
            $steps = $type->getTypeCourseAnnouncementStepConfigurations();
            foreach ($steps as $step) {
                $nameTranslation = '';
                $idAnnConfig = $step->getAnnouncementConfigurationType()->getId();
                /** @var AnnouncementConfigurationTypeTranslation $translation */
                $annTypeConfigTranslations = $step->getAnnouncementConfigurationType()->getTranslations();
                foreach ($annTypeConfigTranslations as $translation) {
                    if ($translation->getLocale() == $userLocale) {
                        $name = $translation->getName();
                    } else {
                        $name = $step->getAnnouncementConfigurationType()->getName();
                    }
                }
                $annStepConfig[] = [
                    'id' => $step->getId(),
                    'name' => $name,
                    'active' => $step->isActive(),
                ];
            }
            // ********** */

            $typeCourseName = $type->getTypeCourse()->getName() . ' ' . substr($type->getTypeCourse()->getDenomination(), 0, 3);
            $data[] = [
                'id' => $type->getId(),
                'name' => $type->getName(),
                'description' => $type->getDescription(),
                'position' => $type->getPosition(),
                'isRequired' => $type->isIsRequired(),
                'active' => $type->isActive(),
                'typeCourseName' => $typeCourseName,
                'denomination' => $type->getTypeCourse()->getDenomination(),
                'translations' => $translations,

                'StepsConfigurations' => $annStepConfig,
            ];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data,
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     *
     * @Rest\Post("/TypeCourse-AnnouncementStepCreation/{id}/active")
     */
    public function changeTypeCourseAnnouncementStepCreationActiveStatus(Request $request, TypeCourseAnnouncementStepCreation $typeCourseAnnouncementStepCreation): Response
    {
        $content = json_decode($request->getContent(), true);

        $active = isset($content['active']) ? (bool) $content['active'] : false;

        if (!$this->canChangeActiveStatus($typeCourseAnnouncementStepCreation)) {
            return $this->sendResponse([
                'status' => Response::HTTP_BAD_REQUEST,
                'error' => true,
                'message' => 'You must first activate the corresponding configuration at the platform level.',
            ]);
        }

        $typeCourseAnnouncementStepCreation->setActive($active);
        $this->em->persist($typeCourseAnnouncementStepCreation);
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
        ]);
    }

    private function canChangeActiveStatus(TypeCourseAnnouncementStepCreation $typeCourseAnnouncementStepCreation): bool
    {
        $typeCourseAnnouncementStepConfigurations = $typeCourseAnnouncementStepCreation->getTypeCourseAnnouncementStepConfigurations();

        foreach ($typeCourseAnnouncementStepConfigurations as $stepConfiguration) {
            $announcementConfigurationType = $stepConfiguration->getAnnouncementConfigurationType();

            if ($announcementConfigurationType && AnnouncementConfigurationTypeEnum::ANNOUNCEMENT_CERTIFICATE_CODE === $announcementConfigurationType->getCode()) {
                $configurationClientAnnouncement = $announcementConfigurationType->getConfigurationClientAnnouncement();
                if ($configurationClientAnnouncement && !$configurationClientAnnouncement->isActive()) {
                    return false;
                }
            }
        }

        $announcementStepCreation = $typeCourseAnnouncementStepCreation->getAnnouncementStepCreation();

        return (bool) $announcementStepCreation;
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     *
     * @Rest\Post("/TypeCourse-AnnouncementStepCreation/{id}/activeConfiguration")
     */
    public function changeTypeCourseAnnouncementStepConfigurationActiveStatus(
        Request $request,
        TypeCourseAnnouncementStepConfiguration $typeCourseAnnouncementStepConfiguration
    ): Response {
        $content = json_decode($request->getContent(), true);
        $active = $content['active'] ?? false;
        $typeCourseAnnouncementStepConfiguration->setActive($active);
        $this->em->persist($typeCourseAnnouncementStepConfiguration);
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     *
     * @Rest\Post("/TypeCourse-AnnouncementStepCreation/update")
     */
    public function updateTypeCourseAnnouncementStepCreation(
        Request $request,
        TypeCourseAnnouncementStepCreationRepository $typeCourseAnnouncementStepCreationRepository
    ): Response {
        $content = json_decode($request->getContent(), true);
        $id = $content['id'] ?? -1;
        $typeCourseAnnouncementStepCreation = $typeCourseAnnouncementStepCreationRepository->find($id);
        if (!$typeCourseAnnouncementStepCreation) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'NOT_FOUND',
            ]);
        }

        if (($result = $this->saveTypeCourseAnnouncementStepCreation($request, $typeCourseAnnouncementStepCreation)) instanceof Response) {
            return $result;
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
        ]);
    }

    public function saveTypeCourseAnnouncementStepCreation(
        Request $request,
        TypeCourseAnnouncementStepCreation $typeCourseAnnouncementStepCreation
    ) {
        $content = json_decode($request->getContent(), true);
        $name = $content['name'] ?? null;
        $description = $content['description'] ?? null;
        $active = $content['active'] ?? false;
        $isRequired = $content['isRequired'] ?? false;

        $errors = [];
        if (empty($name)) {
            $errors[] = 'Name required';
        }
        if (\count($errors)) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => $errors,
            ]);
        }
        $typeCourseAnnouncementStepCreation->setName($name)
            ->setDescription($description)
            ->setIsRequired($isRequired)
            ->setActive($active);

        $translations = $content['translations'];
        foreach ($translations as $data) {
            $translation = $this->em->getRepository(TypeCourseAnnouncementStepCreationTranslation::class)->findOneBy([
                'translatable' => $typeCourseAnnouncementStepCreation,
                'locale' => $data['locale'],
            ]);
            $name = $data['name'] ?? null;
            $description = $data['description'] ?? null;
            if (empty($name) && empty($description)) {
                if ($translation) {
                    $this->em->remove($translation);
                }
                continue;
            }
            if (!$translation) {
                $translation = new TypeCourseAnnouncementStepCreationTranslation();
                $translation->setTranslatable($typeCourseAnnouncementStepCreation);
                $translation->setLocale($data['locale']);
            }
            $translation->setName($name)
                ->setDescription($description);
            $this->em->persist($translation);
        }

        $StepsConfigurations = $content['StepsConfigurations'];
        foreach ($StepsConfigurations as $data) {
            $StepsConfiguration = $this->em->getRepository(TypeCourseAnnouncementStepConfiguration::class)->find($data['id']);
            $StepsConfiguration->setActive($data['active']);
            $this->em->persist($StepsConfiguration);
        }

        $this->em->persist($typeCourseAnnouncementStepCreation);
        $this->em->flush();

        return true;
    }
}
