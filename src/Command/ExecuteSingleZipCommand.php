<?php

declare(strict_types=1);

namespace App\Command;

use App\Entity\ZipFileTask;
use App\Service\TaskCron\ZipFileTaskExecutorService;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

/**
 * Command to execute a specific ZipFileTask by its ID.
 * This command is used internally by GenerateZipCommand to execute tasks in separate processes.
 */
class ExecuteSingleZipCommand extends Command
{
    private const MAX_TIMEOUT = 7200;

    protected static $defaultName = 'zip:execute-single';
    protected static $defaultDescription = 'Execute a single ZipFileTask by ID';

    private EntityManagerInterface $em;
    private ZipFileTaskExecutorService $zipFileTaskExecutorService;
    private LoggerInterface $logger;

    public function __construct(
        EntityManagerInterface $em,
        ZipFileTaskExecutorService $zipFileTaskExecutorService,
        LoggerInterface $logger
    ) {
        $this->em = $em;
        $this->zipFileTaskExecutorService = $zipFileTaskExecutorService;
        $this->logger = $logger;
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument('taskId', InputArgument::REQUIRED, 'The ID of the ZipFileTask to execute');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        set_time_limit(self::MAX_TIMEOUT);
        $io = new SymfonyStyle($input, $output);
        $taskId = $input->getArgument('taskId');

        $task = $this->em->getRepository(ZipFileTask::class)->find($taskId);

        if (!$task) {
            $io->error(\sprintf('ZipFileTask with ID %s not found', $taskId));

            return Command::FAILURE;
        }

        try {
            $io->text(\sprintf('Executing ZipFileTask with ID %s', $taskId));

            // Execute the task
            $this->zipFileTaskExecutorService->execute($task);

            $io->success(\sprintf('ZipFileTask with ID %s executed successfully', $taskId));

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $io->error(\sprintf('Error executing ZipFileTask with ID %s: %s', $taskId, $e->getMessage()));

            return Command::FAILURE;
        }
    }
}
