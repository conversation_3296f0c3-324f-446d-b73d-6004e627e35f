<?php

declare(strict_types=1);

namespace App\Command\Announcement;

use App\Service\Annoucement\CronJob\AnnouncementNotificationCronService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

class AnnoucementAlertCommand extends Command
{
    public function __construct(
        private readonly AnnouncementNotificationCronService $announcementNotificationCronService
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->setName('announcement:state:execute')
            ->setDescription('Notifica a los usuarios cada media hora sobres las alertas de las convocatorias');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        try {
            $this->announcementNotificationCronService->executeAlertAnnouncement();
            $io->success('Las notificaciones de la convocatoria han sido revisadas');

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $io->error($e->getMessage());

            return Command::FAILURE;
        }
    }
}
