<?php

declare(strict_types=1);

namespace App\Command\Announcement;

use App\Service\Annoucement\CronJob\AnnouncementCronService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

class AnnouncementCommand extends Command
{
    public function __construct(
        private readonly AnnouncementCronService $announcementCronService
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->setName('announcement:aproved-criteria')
            ->setDescription('Revisar el estado de las convocatorias');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        try {
            $this->announcementCronService->execute();

            $io->success('Las convocatorias han sido revisadas');

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $io->error($e->getMessage());

            return Command::FAILURE;
        }
    }
}
