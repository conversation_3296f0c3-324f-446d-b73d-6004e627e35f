<?php

declare(strict_types=1);

namespace App\Command;

use App\Service\Nps\NpsService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

class InitializeTableCourseStatCommand extends Command
{
    public function __construct(
        private readonly NpsService $npsService
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->setName('initialize:course-stat')
            ->setDescription('Initialize the course_stat table');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        try {
            $message = $this->npsService->initilizeAndNewRegisterTableCourseStat();
            $io->success($message);

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $io->error($e->getMessage());

            return Command::FAILURE;
        }
    }
}
