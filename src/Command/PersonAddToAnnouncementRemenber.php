<?php

declare(strict_types=1);

namespace App\Command;

use App\Behavior\Notifications;
use App\Repository\EmailNotificationRepository;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Yaml\Yaml;
use Symfony\Contracts\Translation\TranslatorInterface;

class PersonAddToAnnouncementRemenber extends Command
{
    use Notifications;

    private mixed $appConf;

    public function __construct(
        private readonly EmailNotificationRepository $emailNotificationRepository,
        private readonly TranslatorInterface $translator,
        private readonly MailerInterface $mailer,
        private readonly LoggerInterface $logger
    ) {
        $this->appConf = Yaml::parse(
            file_get_contents(realpath(__DIR__ . '/../..') . '/config/services/easylearning.yaml')
        )['parameters'];

        parent::__construct();
    }

    protected function configure(): void
    {
        $this->setName('notification:PersonAddToAnnouncementRemenber')
            ->setHelp('Send email notifications - 72h before')
            ->setDescription('Send itinerary: notifications - 72h before')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->logger->info('notification:PersonAddToAnnouncementRemenber');

        foreach ($this->appConf['app.languages'] as $language) {
            $this->sendByLang($language);
        }

        return 1;
    }

    protected function sendByLang($lang): int
    {
        $this->logger->info('PersonAddToAnnouncementRemenber:' . $lang);
        // choose items to send
        $data = $this->emailNotificationRepository->findAnnouncementUserRemenber($lang);

        $this->logger->info(print_r($data));

        if (\count($data) > 0) {
            foreach ($data as $item) {
                $this->sendEmailByGroups(
                    [
                        'email' => [$item['email']],
                    ],
                    \sprintf(
                        $this->translator->trans(
                            'notification.announcementRemember.subject',
                            [],
                            'emailNotification',
                            $lang
                        ),
                        $item['firstName']
                    ),
                    \sprintf(
                        $this->translator->trans(
                            'notification.announcementRemember.title',
                            [],
                            'emailNotification',
                            $lang
                        ),
                        $item['firstName']
                    ),
                    \sprintf(
                        $this->translator->trans(
                            'notification.announcementRemember.body',
                            [],
                            'emailNotification',
                            $lang
                        ),
                        $this->appConf['app.fromName'],
                        $item['name'],
                        $this->appConf['app.fromName']
                    ),
                    '',
                    \sprintf(
                        $this->translator->trans(
                            'notification.announcementRemember.button',
                            [],
                            'emailNotification',
                            $lang
                        ),
                        $item['firstName']
                    ),
                    '',
                    $this->translator->trans(
                        'notification.announcementRemember.footer',
                        [],
                        'emailNotification',
                        $lang
                    ),
                );
            }
        // update users and notifications
        // $this->emailNotificationRepository->markSentItems($data);
        } else {
            print_r('Sin data para enviar ' . $lang);
        }

        return 1;
    }
}
