<?php

declare(strict_types=1);

namespace App\Command;

use App\Entity\User;
use App\Entity\UserManage;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class UserManagerInitCommand extends Command
{
    public function __construct(
        private readonly EntityManagerInterface $em,
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this->setName('user:managers:init')
            ->setDescription('Create manage row on manager users');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $userRepository = $this->em->getRepository(User::class);

        $managers = $userRepository->findManagers();

        foreach ($managers as $manager) {
            if (!$manager->getManage()) {
                $userManage = new UserManage();
                $userManage->setUser($manager);
                $this->em->persist($userManage);

                $output->writeln('Updated: ' . $manager->getFullName());
            }
        }

        $this->em->flush();

        return Command::SUCCESS;
    }
}
