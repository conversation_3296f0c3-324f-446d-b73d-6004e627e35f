<?php

namespace App\Resources\DataFixtureBase\Catalog;

class CatalogData
{
    const DEFAULT_DATA = [
        [
            'id'=> 1,
            'name'=> 'catalog.1.name',
            'relation'=> 'PLATFORM',
            'description'=> 'catalog.1.description',
            'component'=> 'ChapterType',
            'route'=> 'ChapterTypes',
            'service'=> 'ChapterTypeServices',
        ],
        [
            'id'=> 2,
            'name'=> 'catalog.2.name',
            'relation'=> 'PLATFORM',
            'description'=> 'catalog.2.description',
            'component'=> 'CourseType',
            'route'=> 'TypeCourse',
            'service' => 'TypeCourseServices'
        ],
        [
            'id'=> 3,
            'name'=> 'catalog.3.name',
            'relation'=> 'ANNOUNCEMENT',
            'description'=> 'catalog.3.description',
            'component'=> 'AprovalCriteria',
            'route'=> 'AnnouncementCriteria',
            'service'=> 'AnnouncementCriteriaServices',
        ],
        [
            'id'=> 4,
            'name'=> 'catalog.4.name',
            'relation'=> 'ANNOUNCEMENT',
            'description'=> 'catalog.4.description',
            'component'=> 'TutorAlerts',
            'route'=> 'AlertTypeTutor',
            'service'=> 'AlertTypeTutorServices'
        ],
        [
            'id'=> 5,
            'name'=> 'catalog.5.name',
            'relation'=> 'PLATFORM',
            'description'=> 'catalog.5.description',
            'component'=> 'DiplomasType',
            'route'=> 'DiplomasType',
            'service'=> 'TypeDiplomaServices'
        ],
        [
            'id'=> 6,
            'name'=> 'catalog.6.name',
            'relation'=> 'ANNOUNCEMENT',
            'description'=> 'catalog.6.description',
            'component' => 'ConfigurationClientAnnouncement',
            'route'=> 'ConfigurationClientAnnouncement',
            'service' => 'ConfigurationClientAnnouncementServices'
        ],
        [
            'id'=> 7,
            'name'=> 'catalog.7.name',
            'relation'=> 'ANNOUNCEMENT',
            'description'=> 'catalog.7.description',
            'component'=> 'MoneyType',
            'route'=> 'TypeMoney',
            'service' => 'TypeMoneyServices'
        ],
        [
            'id'=> 8,
            'name'=> 'catalog.8.name',
            'relation'=> 'PLATFORM',
            'description'=> 'catalog.8.description',
            'component'=> 'GroupSetting',
            'route'=> 'SettingGroup',
            'service'=> null,
        ],
        [
            'id'=> 9,
            'name'=> 'catalog.9.name',
            'relation'=> 'PLATFORM',
            'description'=> 'catalog.9.description',
            'component'=> 'Setting',
            'route'=> 'Setting',
            'service'=> 'SettingServices',
        ],
        [
            'id'=> 10,
            'name'=> 'catalog.10.name',
            'relation'=> 'FUNDAE',
            'description'=> 'catalog.10.description',
            'component'=> 'Company',
            'route'=> 'Company',
            'service' => null
        ],
        [
            'id'=> 11,
            'name'=> 'catalog.11.name',
            'relation'=> 'FUNDAE',
            'description'=> 'catalog.11.description',
            'component'=> 'ProfessionalCategory',
            'route'=> 'ProfessionalCategory',
            'service' => null
        ],
        [
            'id'=> 12,
            'name'=> 'catalog.12.name',
            'relation'=> "FUNDAE",
            'description'=> 'catalog.12.description',
            'component'=> 'UserWorkCenter',
            'route'=> 'UserWorkCenter',
            'service'=> null
        ],
        [
            'id'=> 13,
            'name'=> 'catalog.13.name',
            'relation'=> 'FUNDAE',
            'description'=> 'catalog.13.description',
            'component'=> 'UserWorkDepartment',
            'route'=> 'UserWorkDepartment',
            'service'=> null
        ],
        [
            'id'=> 14,
            'name'=> 'catalog.14.name',
            'relation'=> 'FUNDAE',
            'description'=> 'catalog.14.description',
            'component'=> 'UserStudyLevel',
            'route'=> 'UserStudyLevel',
            'service'=> null
        ],
        [
            'id'=> 15,
            'name'=> 'catalog.15.name',
            'relation'=> 'ANNOUNCEMENT',
            'description'=> 'catalog.15.description',
            'component'=> 'TypeCourseAnnouncementStepCreation',
            'route'=> 'TypeCourseAnnouncementStepCreation',
            'service'=> 'TypeCourseAnnouncementStepCreationServices'
        ],
        [
            'id'=> 16,
            'name'=> 'catalog.16.name',
            'relation'=> 'ANNOUNCEMENT',
            'description'=> 'catalog.16.description',
            'component'=> 'ClassroomvirtualType',
            'route'=> 'ClassroomvirtualType',
            'service'=> 'ClassroomvirtualTypeServices'
        ],
        [
            'id'=> 17,
            'name'=> 'catalog.17.name',
            'relation'=> 'PLATFORM',
            'description'=> 'catalog.17.description',
            'component'=> 'TypeIdentification',
            'route'=> 'TypeIdentification',
            'service' => 'TypeIdentificationServices'
        ],

        [
            'id'=> 18,
            'name'=> 'catalog.18.name',
            'relation'=> 'ANNOUNCEMENT',
            'description'=> 'catalog.18.description',
            'component'=> 'AnnouncementModality',
            'route'=> 'AnnouncementModality',
            'service' => null
        ],
        [
            'id'=> 19,
            'name'=> 'catalog.19.name',
            'relation'=> 'PLATFORM',
            'description'=> 'catalog.19.description',
            'component'=> 'TranslationsAdmin',
            'route'=> 'TranslationsAdmin',
            'service'=> null,
        ],
        [
            'id'=> 20,
            'name'=> 'catalog.20.name',
            'relation'=> 'ANNOUNCEMENT',
            'description'=> 'catalog.20.description',
            'component'=> 'ExtraData',
            'route'=> 'ExtraData',
            'service' => 'ExtraDataServices'

        ],
        [
            'id'=> 21,
            'name'=> 'catalog.21.name',
            'relation'=> 'PLATFORM',
            'description'=> 'catalog.21.description',
            'component'=> 'UserExtraFields',
            'route'=> 'UserExtraFields',
            'service'=> null,
        ]
    ];

}