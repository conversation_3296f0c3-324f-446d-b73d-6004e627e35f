<?php

namespace App\Resources\Traits\Catalog;

use App\Entity\AnnouncementStepCreation;
use App\Resources\DataFixtureBase\Announcement\AnnouncementStepCreationData;

trait AnnouncementStepCreationTrait
{

    protected function getDefaultData(): array
    {
        $parameters = self::PARAMETERS_BASE;
        $parameters['fieldsToTranslate'] = ['description'];
        $parameters['setFields'] = array_merge($parameters['setFields'], ['position' => 'setPosition', 'extra' => 'setExtra']);

        return [
            'data' => AnnouncementStepCreationData::DEFAULT_DATA,
            'parameters' => $parameters,
            'baseEntity' => AnnouncementStepCreation::class
        ];
    }
}