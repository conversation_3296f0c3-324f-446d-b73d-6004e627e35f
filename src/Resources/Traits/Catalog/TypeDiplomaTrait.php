<?php

declare(strict_types=1);

namespace App\Resources\Traits\Catalog;

use App\Entity\TypeDiploma;
use App\Resources\DataFixtureBase\Announcement\TypeDiplomaData;

trait TypeDiplomaTrait
{
    protected function getDefaultData(): array
    {
        $parameters = self::PARAMETERS_BASE;
        $parameters['setFields'] = array_merge(
            $parameters['setFields'],
            ['extra' => 'setExtra', 'is_main' => 'setIsMain', 'apply_to' => 'setApplyTo']
        );

        return [
            'data' => TypeDiplomaData::DEFAULT_DATA,
            'parameters' => $parameters,
            'baseEntity' => TypeDiploma::class,
            'translationEntity' => null,
        ];
    }
}
