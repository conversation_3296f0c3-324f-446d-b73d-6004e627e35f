<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\Fillgaps;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Fillgaps>
 *
 * @method Fillgaps|null find($id, $lockMode = null, $lockVersion = null)
 * @method Fillgaps|null findOneBy(array $criteria, array $orderBy = null)
 * @method Fillgaps[]    findAll()
 * @method Fillgaps[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class FillgapsRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Fillgaps::class);
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function add(Fillgaps $entity, bool $flush = true): void
    {
        $this->_em->persist($entity);
        if ($flush) {
            $this->_em->flush();
        }
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function remove(Fillgaps $entity, bool $flush = true): void
    {
        $this->_em->remove($entity);
        if ($flush) {
            $this->_em->flush();
        }
    }
}
