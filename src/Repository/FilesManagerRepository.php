<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\FilesManager;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<FilesManager>
 *
 * @method FilesManager|null find($id, $lockMode = null, $lockVersion = null)
 * @method FilesManager|null findOneBy(array $criteria, array $orderBy = null)
 * @method FilesManager[]    findAll()
 * @method FilesManager[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class FilesManagerRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, FilesManager::class);
    }

    public function add(FilesManager $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(FilesManager $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
