<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\Chapter;
use App\Entity\Content;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method Content|null find($id, $lockMode = null, $lockVersion = null)
 * @method Content|null findOneBy(array $criteria, array $orderBy = null)
 * @method Content[]    findAll()
 * @method Content[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ContentRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Content::class);
    }

    public function getContentsSimplified(Chapter $chapter): array
    {
        return $this->createQueryBuilder('c')
            ->select('c.id', 'c.title', 'c.content', 'c.position')
            ->where('c.chapter =:chapter')
            ->setParameter('chapter', $chapter)
            ->orderBy('c.position', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * @return Content[]
     */
    public function getContentsOrderedByPosition(Chapter $chapter): array
    {
        return $this->_em->getRepository(Content::class)->createQueryBuilder('c')
            ->select('c')
            ->where('c.chapter = :chapter')
            ->setParameter('chapter', $chapter)
            ->orderBy('c.position', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * @param Content[] $contents
     *
     * @return void
     */
    public function updateContentsPosition(array $contents)
    {
        for ($i = 0; $i < \count($contents); ++$i) {
            $contents[$i]->setPosition($i + 1);
            $this->_em->persist($contents[$i]);
        }
        $this->_em->flush();
    }

    /**
     * @param Content[] $contents
     */
    public function getContentIndex(array $contents, int $contentId): int
    {
        for ($i = 0; $i < \count($contents); ++$i) {
            if ($contents[$i]->getId() === $contentId) {
                return $i;
            }
        }

        return -1;
    }
}
