<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\ChatMessage;
use App\Entity\ChatMessageLike;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ChatMessageLike>
 *
 * @method ChatMessageLike|null find($id, $lockMode = null, $lockVersion = null)
 * @method ChatMessageLike|null findOneBy(array $criteria, array $orderBy = null)
 * @method ChatMessageLike[]    findAll()
 * @method ChatMessageLike[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ChatMessageLikeRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ChatMessageLike::class);
    }

    public function add(ChatMessageLike $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(ChatMessageLike $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function toggleLike(ChatMessage $message, User $user)
    {
        $like = $this->findOneBy([
            'chatMessage' => $message,
            'user' => $user
        ]);

        if (!$like) {
            $like = new ChatMessageLike();
            $like->setChatMessage($message)
                ->setUser($user);
        }
        $like->setActive(!$like->isActive());

        $this->_em->persist($like);
        $this->_em->flush();
    }
}
