<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\CourseSegment;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method CourseSegment|null find($id, $lockMode = null, $lockVersion = null)
 * @method CourseSegment|null findOneBy(array $criteria, array $orderBy = null)
 * @method CourseSegment[]    findAll()
 * @method CourseSegment[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CourseSegmentRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CourseSegment::class);
    }

    public function getList()
    {
        $segments = $this->findAll();
        $list = [];
        foreach ($segments as $segment) {
            $list[$segment->getId()] = $segment->getCourseSegmentCategory()->getName() . ': ' . $segment->getName();
        }
        asort($list);

        return $list;
    }

    public function getSegmentsByArray(array $segments_ids)
    {
        $qb = $this->createQueryBuilder('s');
        $qb->andWhere($qb->expr()->in('s.id', $segments_ids));

        return $qb->getQuery()->getResult();
    }
}
