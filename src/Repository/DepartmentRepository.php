<?php

declare(strict_types=1);

namespace App\Repository;

use App\Admin\Traits\StatsQueryFiltersTrait;
use App\Entity\Course;
use App\Entity\Department;
use App\Entity\UserCourse;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method Department|null find($id, $lockMode = null, $lockVersion = null)
 * @method Department|null findOneBy(array $criteria, array $orderBy = null)
 * @method Department[]    findAll()
 * @method Department[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class DepartmentRepository extends ServiceEntityRepository
{
    use StatsQueryFiltersTrait;

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Department::class);
    }

    public function getList()
    {
        $departaments = $this->findBy([], ['code' => 'ASC', 'name' => 'ASC']);
        $list = [];
        foreach ($departaments as $departament) {
            $list[$departament->getId()] = $departament->getFullName();
        }

        return $list;
    }

    public function findList($departaments = [])
    {
        $query = $this->createQueryBuilder('c');

        if (!empty($departaments)) {
            $query->andWhere($query->expr()->in('c.id', $departaments));
        }

        return $query
            ->orderBy('c.name', 'ASC')
            ->getQuery()
            ->getResult()
        ;
    }

    public function top10DeparmentMoreUse($conditions)
    {
        $query = $this->createQueryBuilder('d')
           ->select('count(d.id) as count, d.name')
           ->leftJoin(
               Course::class,
               'c',
               \Doctrine\ORM\Query\Expr\Join::WITH,
               'c.code = d.code'
           )
           ->leftJoin(
               UserCourse::class,
               'uc',
               \Doctrine\ORM\Query\Expr\Join::WITH,
               'c.id = uc.course'
           )
           ->groupBy('d.name')
           ->orderBy('count(d.id)', 'DESC')
        ;

        if (!empty($conditions['dateFrom'])) {
            $query->andWhere('c.createdAt >= :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
        }
        if (!empty($conditions['dateTo'])) {
            $query->andWhere('c.createdAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);
        }

        $this->setProfessionalCategoriesQueryFilters($query, $conditions);

        return $query
            ->setMaxResults(10)
            ->getQuery()
            ->getArrayResult()
        ;
    }

    public function courseByDepartment()
    {
        $query = $this->createQueryBuilder('d')
           ->select('count(d.id) as count, d.name')
           ->innerJoin(
               Course::class,
               'c',
               \Doctrine\ORM\Query\Expr\Join::WITH,
               'c.code = d.code'
           )
           ->andWhere('c.deletedAt is null')
           ->groupBy('d.name')
        ;

        $query->addOrderBy('d.name', 'DESC');

        return $query
            ->getQuery()
            ->getArrayResult()
        ;
    }
}
