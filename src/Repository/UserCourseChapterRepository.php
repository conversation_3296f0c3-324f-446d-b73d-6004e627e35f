<?php

declare(strict_types=1);

namespace App\Repository;

use App\Admin\Traits\StatsQueryFiltersTrait;
use App\Admin\Traits\UserCourseTrait;
use App\Entity\Chapter;
use App\Entity\Course;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Entity\UserCourseChapter;
use App\Entity\UserLogin;
use App\Enum\CourseStatsEnum;
use App\Service\Course\DT0\ChapterQueryParams;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method UserCourseChapter|null find($id, $lockMode = null, $lockVersion = null)
 * @method UserCourseChapter|null findOneBy(array $criteria, array $orderBy = null)
 * @method UserCourseChapter[]    findAll()
 * @method UserCourseChapter[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class UserCourseChapterRepository extends ServiceEntityRepository
{
    use StatsQueryFiltersTrait;
    use UserCourseTrait;

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, UserCourseChapter::class);
    }

    public function countByUser(User $user, $finished = null)
    {
        $query = $this->createQueryBuilder('ucc')
            ->select('count(ucc.id)')
            ->leftJoin('ucc.userCourse', 'uc')
            ->andWhere('uc.user = :user')
            ->setParameter('user', $user);

        if (!\is_null($finished)) {
            $query->andWhere('ucc.finishedAt IS ' . ($finished ? 'NOT' : '') . ' NULL');
        }

        return $query->getQuery()
            ->getSingleScalarResult();
    }

    public function countByUserAndCourse(User $user, Course $course, $finished = null)
    {
        $query = $this->createQueryBuilder('ucc')
            ->select('count(ucc.id)')
            ->leftJoin('ucc.userCourse', 'uc')
            ->innerJoin('ucc.chapter', 'c')
            ->andWhere('uc.user = :user')
            ->andWhere('uc.course = :course')
            ->setParameters([
                'user' => $user,
                'course' => $course,
            ]);

        if (!\is_null($finished)) {
            $query->andWhere('ucc.finishedAt IS ' . ($finished ? 'NOT' : '') . ' NULL');
        }

        return $query->getQuery()
            ->getSingleScalarResult();
    }

    public function getTimeSpentByTypeAndUser(User $user)
    {
        return $this->createQueryBuilder('ucc')
            ->select('SUM(ucc.timeSpent) as time, ct.name')
            ->leftJoin('ucc.userCourse', 'uc')
            ->leftJoin('ucc.chapter', 'c')
            ->leftJoin('c.type', 'ct')
            ->andWhere('uc.user = :user')
            ->setParameter('user', $user)
            ->orderBy('SUM(ucc.timeSpent)', 'DESC')
            ->groupBy('ct.name')
            ->getQuery()
            ->getResult();
    }

    public function getTimeSpentByType(array $conditions = [])
    {
        $query = $this->createQueryBuilder('ucc')
            ->select('SUM(ucc.timeSpent) as time, ct.name')
            ->leftJoin('ucc.userCourse', 'uc')
            ->join('ucc.chapter', 'c')
            ->leftJoin('c.type', 'ct')
            ->groupBy('ct.name');

        if (isset($conditions['country']) || isset($conditions['center']) || !empty($conditions['category']) || !empty($conditions['departament']) || !empty($conditions['gender']) || !empty($conditions['division']) || !empty($conditions['filters'])) {
            $query->leftJoin('uc.user', 'u')
                ->leftJoin('u.extra', 'ue');

            $this->setQueryFilters($query, $conditions);
        }

        if (!empty($conditions['dateFrom'])) {
            $query->andWhere('ucc.finishedAt >= :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
        }
        if (!empty($conditions['dateTo'])) {
            $query->andWhere('ucc.finishedAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);
        }

        return $query->getQuery()->getResult();
    }

    public function getTimeSpentByCourse(Course $course)
    {
        $query = $this->createQueryBuilder('ucc')
            ->select('SUM(ucc.timeSpent) as time')
            ->leftJoin('ucc.userCourse', 'uc')
            ->leftJoin('uc.course', 'c')
            ->andWhere('uc.course = :course')
            ->orWhere('c.translation = :course')
            ->setParameter('course', $course);

        return $query->getQuery()->getSingleScalarResult();
    }

    public function getAverageTimeSpentByCourse(Course $course)
    {
        $conn = $this->_em->getConnection();

        $sql = 'SELECT SUM( c.users ) as countUsers, SUM( c.time ), ( SUM( c.time )) / SUM( c.users ) AS averageTime, ( SUM( c.time ) / 60 ) / SUM( c.users ) AS minutos
                FROM (
                 SELECT c.id,  c.code, c.name, COUNT( DISTINCT ( ch.id ) ) AS chapters, COUNT( DISTINCT ( uc.user_id ) ) AS users, SUM( ucc.time_spent ) AS time, 	( SUM( ucc.time_spent ) ) / COUNT( DISTINCT ( uc.user_id ) ) AS MEDIUM
                    FROM course c
                        JOIN chapter ch ON c.id = ch.course_id AND ch.deleted_at IS NULL
                        LEFT JOIN user_course_chapter ucc ON ch.id = ucc.chapter_id
                        JOIN user_course uc ON ucc.user_course_id = uc.id
                        JOIN user u ON uc.user_id = u.id
                    WHERE
                        c.active = 1
                        AND c.deleted_at IS NULL
                        AND u.roles NOT LIKE \'%ADMIN%\'
                        AND u.roles NOT LIKE \'%MANAGER%\'
                        AND uc.finished_at IS NOT NULL
                        AND ( c.id = :course OR c.translation_id = :course )
                    GROUP BY
                    c.id
                ) c;';

        $stmt = $conn->prepare($sql);

        return $stmt->executeQuery(['course' => $course->getId()])->fetchAssociative();
    }

    public function getTotalFinishedChapters(array $conditions = [])
    {
        $query = $this->createQueryBuilder('ucc')
            ->select(['count(ucc.id)'])
            ->andWhere('ucc.finishedAt IS NOT NULL');

        $this->setCourseQueryFilters($query, $conditions);

        return $query->getQuery()->getSingleScalarResult();
    }

    public function getDailyFinishedChapters(array $conditions = [])
    {
        $query = $this->createQueryBuilder('ucc')
            ->select(['count(ucc.id) as count', "DATE_FORMAT(ucc.finishedAt, '%Y-%m-%d') as date"])
            ->andWhere('ucc.finishedAt IS NOT NULL')
            ->groupBy('date');

        if (!empty($conditions['course'])) {
            $subquery = $this->_em->createQueryBuilder()
                ->select('course.id')
                ->from('App:course', 'course')
                ->orWhere('course.id = :course')
                ->orWhere('course.translation = :course')
                ->getDQL();

            $query->leftJoin('ucc.chapter', 'c')
                ->andWhere(
                    $query->expr()->in(
                        'c.course',
                        $subquery
                    )
                )
                ->setParameter(':course', $conditions['course']);
        }

        $this->setCourseQueryFilters($query, $conditions);

        return $query->getQuery()
            ->getResult();
    }

    public function countByType(array $conditions = [])
    {
        $query = $this->createQueryBuilder('ucc')
            ->select([
                'count(ucc.id) as count',
                'ct.name',
                'ct.id',
            ])
            ->innerJoin('ucc.chapter', 'c')
            ->innerJoin('c.type', 'ct')
            ->andWhere('ucc.finishedAt IS NOT NULL')
            ->groupBy('ct.id')
            ->orderBy('count', 'DESC');

        $this->setCourseQueryFilters($query, $conditions);

        return $query->getQuery()->getResult();
    }

    public function findExport($starDate, $endDate)
    {
        $query = $this->createQueryBuilder('ucc');
        $query->select([
            'u.id AS userId',
            'CONCAT(u.firstName, \' \', u.lastName) as userName',
            'c.id AS courseId',
            'c.name AS courseName',
            'chapter.id AS chapterId',
            'chapter.title AS chapterTitle',
            'ucc.startedAt',
            'ucc.finishedAt',
            'ucc.timeSpent',
        ])
            ->leftJoin('ucc.chapter', 'chapter')
            ->leftJoin('ucc.userCourse', 'uc')
            ->leftJoin('uc.course', 'c')
            ->leftJoin('uc.user', 'u');

        if ($starDate) {
            $starDateTime = \DateTime::createFromFormat('Y-m-d H:i', $starDate . ' 00:00');
            $query->andWhere($query->expr()->orX(
                'ucc.startedAt >= :start',
                'ucc.finishedAt >= :start'
            ))
                ->setParameter('start', $starDateTime);
        }

        if ($endDate) {
            $endDateTime = \DateTime::createFromFormat('Y-m-d H:i', $endDate . ' 23:59');
            $query->andWhere($query->expr()->orX(
                'ucc.startedAt <= :end',
                'ucc.finishedAt <= :end'
            ))
                ->setParameter('end', $endDateTime);
        }

        return $query->getQuery()->getResult();
    }

    private function setCourseQueryFilters($query, $conditions)
    {
        if (!empty($conditions)) {
            if (isset($conditions['active']) || isset($conditions['country']) || isset($conditions['center']) || !empty($conditions['category']) || !empty($conditions['departament']) || !empty($conditions['gender']) || !empty($conditions['division'])) {
                $query->leftJoin('ucc.userCourse', 'uc')
                    ->leftJoin('uc.user', 'u')
                    ->leftJoin('u.extra', 'ue');

                $this->setQueryFilters($query, $conditions);
            }

            if (!empty($conditions['dateFrom'])) {
                $query->andWhere('ucc.finishedAt > :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
            }
            if (!empty($conditions['dateTo'])) {
                $query->andWhere('ucc.finishedAt < :dateTo')->setParameter('dateTo', $conditions['dateTo']);
            }
        }
    }

    public function getUsersActivesAndInactives(string $order, array $conditions = [])
    {
        $query = $this->createQueryBuilder('ucc')
            ->select(['sum(ucc.timeSpent) as total', 'u.id', 'u.firstName', 'u.lastName'])
            ->leftJoin('ucc.userCourse', 'uc')
            ->leftJoin('uc.user', 'u')
            ->groupBy('uc.user')
            ->orderBy('total', $order)
            ->setMaxResults(10)
            ->andWhere('u.roles = :role')
            ->setParameter('role', '["ROLE_USER"]');

        if (!empty($conditions['dateFrom'])) {
            $query->andWhere('uc.finishedAt >= :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
        }

        if (!empty($conditions['dateTo'])) {
            $query->andWhere('uc.finishedAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);
        }

        $this->setSearchFilters($query, $conditions);

        return $query->getQuery()->getResult();
    }

    public function getConditionUsers($query, $conditions)
    {
        if (isset($conditions['active']) || isset($conditions['country']) || isset($conditions['center']) || !empty($conditions['category']) || !empty($conditions['departament']) || !empty($conditions['gender']) || !empty($conditions['division']) || !empty($conditions['filters'])) {
            $query->leftJoin('u.extra', 'ue');
        }

        $this->setQueryFilters($query, $conditions);
    }

    public function getTimeByMaxDate(\DateTime $dateTime)
    {
        $query = $this->createQueryBuilder('ucc')
            ->select('sum(ucc.timeSpent)/3600')
            ->where('ucc.startedAt < :date')
            ->setParameter('date', $dateTime->format('Y-m-d'));

        return $query->getQuery()
            ->getSingleScalarResult();
    }

    public function getStatTimeCourseStatedFinished(array $conditions = [], $startOrFinsh, $startHour = null, $endHour = null, $dayWeek = null)
    {
        $query = $this->createQueryBuilder('ucc')
            ->leftJoin('ucc.userCourse', 'uc');

        if ('start' == $startOrFinsh) {
            $query->andWhere('uc.finishedAt is null');

            $query
                ->select('count(uc.startedAt) as count')
                ->andWhere('date_of_week(uc.startedAt) >= :dateWeek')
                ->setParameter('dateWeek', $dayWeek)
                ->andWhere('hour(uc.startedAt)  >= :startHour and hour(uc.startedAt) < :endHour')
                ->setParameter('startHour', $startHour)
                ->setParameter('endHour', $endHour);
        } elseif ('finish' == $startOrFinsh) {
            $query->andWhere('uc.finishedAt is not null');

            $query
                ->select('count(uc.finishedAt) as count')
                ->andWhere('date_of_week(uc.finishedAt) >= :dateWeek')
                ->setParameter('dateWeek', $dayWeek)
                ->andWhere('hour(uc.finishedAt)  >= :startHour and hour(uc.finishedAt) < :endHour')
                ->setParameter('startHour', $startHour)
                ->setParameter('endHour', $endHour);
        } elseif ('login' == $startOrFinsh) {
            $query
                ->select('count(ul.createdAt) as count')
                ->innerJoin(
                    UserLogin::class,
                    'ul',
                    \Doctrine\ORM\Query\Expr\Join::WITH,
                    'uc.user = ul.user'
                )
                ->andWhere('date_of_week(ul.createdAt) >= :dateWeek')
                ->setParameter('dateWeek', $dayWeek)
                ->andWhere('hour(ul.createdAt)  >= :startHour and hour(ul.createdAt) < :endHour')
                ->setParameter('startHour', $startHour)
                ->setParameter('endHour', $endHour);
        }

        if (isset($conditions['active']) || isset($conditions['country']) || isset($conditions['center']) || !empty($conditions['category']) || !empty($conditions['departament']) || !empty($conditions['gender']) || !empty($conditions['division']) || !empty($conditions['filters'])) {
            $query->leftJoin('uc.user', 'u')
                ->leftJoin('u.extra', 'ue');

            $this->setQueryFilters($query, $conditions);
        }

        if (!empty($conditions['dateFrom'])) {
            $query->andWhere('ucc.finishedAt >= :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
        }
        if (!empty($conditions['dateTo'])) {
            $query->andWhere('ucc.finishedAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);
        }

        return $query->getQuery()->getResult();
    }

    public function getTotalUserFinishedChapters(ChapterQueryParams $queryParams)
    {
        $query = $this->createQueryBuilder('ucc')
            ->select('COUNT(ucc.id) as finished, SUM(ucc.timeSpent) as totalTimeSpent')
            ->where('ucc.chapter = :chapter')
            ->andWhere('ucc.finishedAt IS NOT NULL')
            ->setParameter('chapter', $queryParams->chapter)
            ->setMaxResults(1);

        if ($queryParams->courseFinishedOnTime) {
            $this->applyRangeDateConditions($query, CourseStatsEnum::FINISHED_IN_TIME, $queryParams->dateFrom, $queryParams->dateTo);
        }

        $query->join('ucc.userCourse', 'uc')
            ->join('uc.user', 'u')
            ->andWhere($query->expr()->in('u.id', $queryParams->users));

        $this->addFilterByAnnouncement($query, $queryParams->announcementId);

        $result = $query->getQuery()->getSingleResult();

        $total = $result['finished'];
        $totalTimeSpent = $result['totalTimeSpent'];

        return [
            'finished' => $total,
            'totalTimeSpent' => $totalTimeSpent,
        ];
    }

    public function chapterGetTotalUsersStarted(ChapterQueryParams $queryParams)
    {
        $query = $this->createQueryBuilder('ucc')
            ->select('COUNT (ucc.id) as total')
            ->andWhere('ucc.chapter = :chapter')
            ->andWhere('ucc.startedAt IS NOT NULL AND ucc.finishedAt IS NULL')
            ->setParameter('chapter', $queryParams->chapter)
            ->setMaxResults(1);

        if ($queryParams->courseStartedOnTime) {
            $this->applyRangeDateConditions($query, CourseStatsEnum::STARTED_IN_TIME, $queryParams->dateFrom, $queryParams->dateTo);
        }

        $query->join('ucc.userCourse', 'uc')
            ->join('uc.user', 'u')
            ->andWhere($query->expr()->in('u.id', $queryParams->users));

        $this->addFilterByAnnouncement($query, $queryParams->announcementId);

        return $query->getQuery()->getSingleScalarResult();
    }

    public function getTimeByChapter(ChapterQueryParams $queryParams)
    {
        $query = $this->createQueryBuilder('ucc')
            ->select('COUNT(ucc.id) as finished, SUM(ucc.timeSpent) as totalTimeSpent')
            ->leftJoin('ucc.userCourse', 'uc')
            ->where('ucc.chapter = :chapter');

        if ($queryParams->courseStartedOnTime) {
            $this->applyRangeDateConditions($query, CourseStatsEnum::STARTED_IN_TIME, $queryParams->dateFrom, $queryParams->dateTo);
        }

        if (!empty($queryParams->users)) {
            $query->join('uc.user', 'u')
                ->andWhere($query->expr()->in('u.id', $queryParams->users));
        }

        $this->addFilterByAnnouncement($query, $queryParams->announcementId);

        $query->setParameter('chapter', $queryParams->chapter)
            ->setMaxResults(1);

        $result = $query->getQuery()->getSingleResult();

        $totalTimeSpent = $result['totalTimeSpent'] ?? 0;

        return [
            'totalTime' => $totalTimeSpent,
        ];
    }

    private function applyRangeDateConditions(QueryBuilder $query, int $type = CourseStatsEnum::STARTED_IN_TIME, ?\DateTimeInterface $dateFrom = null, ?\DateTimeInterface $dateTo = null): void
    {
        $column = CourseStatsEnum::STARTED_IN_TIME === $type ? 'startedAt' : 'finishedAt';
        if ($dateFrom) {
            $query->andWhere('ucc.' . $column . ' >= :dateFrom')
                ->setParameter('dateFrom', $dateFrom);
        }

        if ($dateTo) {
            $query->andWhere('ucc.' . $column . ' <= :dateTo')
                ->setParameter('dateTo', $dateTo);
        }
    }

    public function chapterGetUserCourseChaptersQuery(ChapterQueryParams $queryParams): ?QueryBuilder
    {
        if ($queryParams->findUsers && empty($queryParams->users)) {
            return null;
        }

        $baseQb = $this->createQueryBuilder('ucc')
            ->andWhere('ucc.chapter = :chapter')
            ->setParameter('chapter', $queryParams->chapter)
            ->addOrderBy('ucc.id', 'ASC')
            ->addGroupBy('ucc.id');

        if (0 != $queryParams->announcementId) {
            $userCourseAnnouncementIds = [];
            $userCourse = $this->_em->getRepository(UserCourse::class)->findBy(['announcement' => $queryParams->announcementId]);
            foreach ($userCourse as $uc) {
                $userCourseAnnouncementIds[] = $uc->getId();
            }
            if (empty($userCourseAnnouncementIds)) {
                return null;
            }
            $baseQb->andWhere($baseQb->expr()->in('ucc.userCourse', $userCourseAnnouncementIds));
        }

        if ($queryParams->courseStartedOnTime) {
            $this->applyRangeDateConditions($baseQb, CourseStatsEnum::STARTED_IN_TIME, $queryParams->dateFrom, $queryParams->dateTo);
        }

        if ($queryParams->courseFinishedOnTime) {
            $this->applyRangeDateConditions($baseQb, CourseStatsEnum::FINISHED_IN_TIME, $queryParams->dateFrom, $queryParams->dateTo);
        }

        if (0 == $queryParams->announcementId) {
            $baseQb->join('ucc.userCourse', 'uc')
                ->join('uc.user', 'u')
                ->andWhere('uc.announcement is NULL');

            if (!empty($queryParams->users)) {
                $baseQb->andWhere($baseQb->expr()->in('u.id', $queryParams->users));
            }
        }

        return $baseQb;
    }

    public function getUserChapterPaginated(QueryBuilder $baseQb, $offset, $pageSize)
    {
        $qb = clone $baseQb;
        $qb->setMaxResults($pageSize)->setFirstResult($offset * $pageSize);

        return $qb->getQuery()->toIterable();
    }

    public function getUserCourseChapterDiffAdminSuper(Chapter $chapter, User $user)
    {
        return $this->createQueryBuilder('ucc')
            ->innerJoin('ucc.userCourse', 'uc')
            ->andWhere('ucc.chapter = :chapter')
            ->andWhere('uc.user != :user')
            ->setParameter('chapter', $chapter)
            ->setParameter('user', $user)
            ->getQuery()
            ->getResult()
        ;
    }

    public function courseGetTotalChapterStarted(Course $course, array $usersIds = [], $announcement = null)
    {
        $qb = $this->createQueryBuilder('ucc')
            ->select('COUNT(DISTINCT(ucc.chapter)) as total')
            ->join('ucc.userCourse', 'uc')
            ->where('uc.course = :course')
            ->andWhere('ucc.startedAt IS NOT NULL AND ucc.finishedAt IS NULL');

        $this->addFilterByAnnouncement($qb, $announcement);

        $qb->setParameter('course', $course)
            ->setMaxResults(1);

        if (!empty($usersIds)) {
            $qb->join('uc.user', 'u')
                ->andWhere($qb->expr()->in('u.id', $usersIds));
        }

        try {
            return (int) $qb
                ->getQuery()
                ->getSingleScalarResult();
        } catch (NoResultException|NonUniqueResultException $e) {
            return 0;
        }
    }

    public function courseGetTotalChapterFinished(Course $course, array $usersIds = [], $announcement = null)
    {
        $qb = $this->createQueryBuilder('ucc')
            ->select('COUNT(DISTINCT(ucc.chapter)) as total')
            ->join('ucc.userCourse', 'uc')
            ->where('uc.course = :course')
            ->andWhere('ucc.startedAt IS NOT NULL AND ucc.finishedAt IS NOT NULL');

        $this->addFilterByAnnouncement($qb, $announcement);

        $qb->setParameter('course', $course)
            ->setMaxResults(1);

        if (!empty($usersIds)) {
            $qb->join('uc.user', 'u')
                ->andWhere($qb->expr()->in('u.id', $usersIds));
        }

        try {
            return (int) $qb
                ->getQuery()
                ->getSingleScalarResult();
        } catch (NoResultException|NonUniqueResultException $e) {
            return 0;
        }
    }

    public function getUserChapters(UserCourse $userCourse)
    {
        return $this->createQueryBuilder('ucc')
            ->select('ucc.id', 'ucc.startedAt', 'ucc.finishedAt', 'ucc.data', 'ucc.timeSpent')
            ->addSelect('c.id as chapterId')
            ->join('ucc.chapter', 'c')
            ->where('ucc.userCourse = :userCourse')
            ->setParameter('userCourse', $userCourse)
            ->getQuery()
            ->getResult();
    }

    public function findUsersInChapterAndFinishedCourse(ChapterQueryParams $queryParams): bool
    {
        $qb = $this->_em->createQueryBuilder();
        $qb->select('COUNT(DISTINCT uc.user)')
            ->from(UserCourseChapter::class, 'ucc')
            ->innerJoin('ucc.userCourse', 'uc')
            ->where('ucc.chapter = :chapter')
            ->andWhere('uc.finishedAt IS NOT NULL')
            ->andWhere('uc.user IN (:userIds)')
            ->setParameter('chapter', $queryParams->chapter)
            ->setParameter('userIds', $queryParams->users);

        $validCount = $qb->getQuery()->getSingleScalarResult();

        return $validCount === \count($queryParams->users);
    }

    public function getUserCourseChapters(UserCourse $userCourse)
    {
        return $this->createQueryBuilder('ucc')
            ->join('ucc.chapter', 'c')
            ->where('ucc.userCourse = :userCourse')
            ->andWhere('c.deletedAt IS NULL')
            ->setParameter('userCourse', $userCourse)
            ->getQuery()
            ->getResult();
    }
}
