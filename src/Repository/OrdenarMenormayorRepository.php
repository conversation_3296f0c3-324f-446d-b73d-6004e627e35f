<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\OrdenarMenormayor;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<OrdenarMenormayor>
 *
 * @method OrdenarMenormayor|null find($id, $lockMode = null, $lockVersion = null)
 * @method OrdenarMenormayor|null findOneBy(array $criteria, array $orderBy = null)
 * @method OrdenarMenormayor[]    findAll()
 * @method OrdenarMenormayor[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class OrdenarMenormayorRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, OrdenarMenormayor::class);
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function add(OrdenarMenormayor $entity, bool $flush = true): void
    {
        $this->_em->persist($entity);
        if ($flush) {
            $this->_em->flush();
        }
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function remove(OrdenarMenormayor $entity, bool $flush = true): void
    {
        $this->_em->remove($entity);
        if ($flush) {
            $this->_em->flush();
        }
    }
}
