<?php

declare(strict_types=1);

namespace App\Service\Inspector;

use App\Entity\Announcement;
use App\Entity\AnnouncementInspectorAccess;
use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Request;

class InspectorAccessService
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    public function getInspectorAccess(AnnouncementInspectorAccess $announcementInspectorAccess): array
    {
        $announcement = $this->validateAnnouncement($announcementInspectorAccess);
        $user = $this->validateUser($announcementInspectorAccess->getEmail());

        return [
            'user' => $user->getId(),
            'course' => $announcement->getCourse()->getId(),
            'announcement' => $announcement->getId(),
        ];
    }

    private function validateAnnouncement(AnnouncementInspectorAccess $announcementInspectorAccess): Announcement
    {
        $announcement = $announcementInspectorAccess->getAnnouncement();
        if (!$announcement) {
            throw new \RuntimeException('Announcement not found');
        }

        return $announcement;
    }

    private function validateUser(string $email): User
    {
        $user = $this->entityManager->getRepository(User::class)->findOneBy(['email' => $email]);
        if (!$user) {
            throw new \RuntimeException('User not found');
        }

        if (AnnouncementInspectorAccess::EMAIL_INSPECTOR !== $email) {
            throw new \RuntimeException('User is not an inspector');
        }

        return $user;
    }

    public function generateFullUrl(Request $request, string $result): string
    {
        return str_replace($request->getRequestUri(), '', $request->getUri()) . $result;
    }
}
