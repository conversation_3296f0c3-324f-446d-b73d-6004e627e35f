<?php

namespace App\Service\Catalog;

use App\Entity\Catalog;
use App\Service\Catalog\ListCatalog\Announcement\AlertTypeTutorServices;
use App\Service\Catalog\ListCatalog\Announcement\AnnouncementConfigurationTypeServices;
use App\Service\Catalog\ListCatalog\Announcement\AnnouncementCriteriaServices;
use App\Service\Catalog\ListCatalog\Announcement\AnnouncementStepCreationServices;
use App\Service\Catalog\ListCatalog\Announcement\ClassroomVirtualTypeServices;
use App\Service\Catalog\ListCatalog\Announcement\ConfigurationClientAnnouncementServices;
use App\Service\Catalog\ListCatalog\Announcement\TypeCourseAnnouncementStepCreationServices;
use App\Service\Catalog\ListCatalog\Announcement\TypeDiplomaServices;
use App\Service\Catalog\ListCatalog\Announcement\TypeIdentificationServices;
use App\Service\Catalog\ListCatalog\Announcement\TypeMoneyServices;
use App\Service\Catalog\ListCatalog\Catalog\CatalogoServices;
use App\Service\Catalog\ListCatalog\Course\ChapterTypeServices;
use App\Service\Catalog\ListCatalog\Course\TypeCourseServices;
use App\Service\Catalog\ListCatalog\General\CronJobServices;
use App\Service\Catalog\ListCatalog\General\SectionDefaultFrontServices;
use App\Service\Catalog\ListCatalog\Settings\SettingServices;
use Doctrine\ORM\EntityManagerInterface;

class ExecuteCatalogService
{

    private TypeIdentificationServices $typeIdentificationServices;
    private CatalogoServices $catalogService;
    private AlertTypeTutorServices $alertTypeTutorServices;
    private AnnouncementCriteriaServices $announcementCriteriaServices;
    private ConfigurationClientAnnouncementServices $configurationClienteAnnouncementServices;
    private ChapterTypeServices $chapterTypeServices;
    private AnnouncementConfigurationTypeServices $announcementConfigurationTypeServices;
    private TypeCourseServices $courseServices;
    private TypeDiplomaServices $diplomaServices;
    private CronJobServices $cronJobServices;
    private ClassroomVirtualTypeServices $classroomVirtualTypeServices;
    private TypeMoneyServices $moneyServices;
    private AnnouncementStepCreationServices $announcementStepCreationServices;
    private TypeCourseAnnouncementStepCreationServices $typeCourseAnnouncementStepCreationServices;
    private SectionDefaultFrontServices $defaultFrontServices;
    private SettingServices $services;
    private EntityManagerInterface $em;


    public function __construct(
        TypeIdentificationServices $typeIdentificationServices,
        CatalogoServices $catalogService,
        AlertTypeTutorServices $alertTypeTutorServices,
        AnnouncementCriteriaServices $announcementCriteriaServices,
        ConfigurationClientAnnouncementServices $configurationClienteAnnouncementServices,
        ChapterTypeServices $chapterTypeServices,
        AnnouncementConfigurationTypeServices $announcementConfigurationTypeServices,
        TypeCourseServices $courseServices,
        TypeDiplomaServices $diplomaServices,
        CronJobServices $cronJobServices,
        ClassroomVirtualTypeServices $classroomVirtualTypeServices,
        TypeMoneyServices $moneyServices,
        AnnouncementStepCreationServices $announcementStepCreationServices,
        TypeCourseAnnouncementStepCreationServices $typeCourseAnnouncementStepCreationServices,
        SectionDefaultFrontServices $defaultFrontServices,
        SettingServices $services,
        EntityManagerInterface $em
    )
    {
        $this->typeIdentificationServices = $typeIdentificationServices;
        $this->catalogService = $catalogService;
        $this->alertTypeTutorServices = $alertTypeTutorServices;
        $this->announcementCriteriaServices = $announcementCriteriaServices;
        $this->chapterTypeServices = $chapterTypeServices;
        $this->announcementConfigurationTypeServices = $announcementConfigurationTypeServices;
        $this->courseServices = $courseServices;
        $this->diplomaServices = $diplomaServices;
        $this->cronJobServices = $cronJobServices;
        $this->classroomVirtualTypeServices = $classroomVirtualTypeServices;
        $this->moneyServices = $moneyServices;
        $this->announcementStepCreationServices = $announcementStepCreationServices;
        $this->typeCourseAnnouncementStepCreationServices = $typeCourseAnnouncementStepCreationServices;
        $this->defaultFrontServices = $defaultFrontServices;
        $this->services = $services;
        $this->configurationClienteAnnouncementServices = $configurationClienteAnnouncementServices;
        $this->em = $em;
     
    }

    private function getObjectData(): array
    {
        return [
            'TypeIdentificationServices' => $this->typeIdentificationServices,
            'CatalogService' => $this->catalogService,
            'AnnouncementCriteriaServices' => $this->announcementCriteriaServices,
            'ConfigurationClientAnnouncementServices' => $this->configurationClienteAnnouncementServices,
            'ChapterTypeServices' => $this->chapterTypeServices,
            'AnnouncementConfigurationTypeServices' => $this->announcementConfigurationTypeServices,
            'TypeCourseServices' => $this->courseServices,
            'TypeDiplomaServices' => $this->diplomaServices,
            'CronJobServices' => $this->cronJobServices,
            'ClassroomvirtualTypeServices' =>$this->classroomVirtualTypeServices,
            'TypeMoneyServices' => $this->moneyServices,
            'AnnouncementStepCreationServices' => $this->announcementStepCreationServices,
            'TypeCourseAnnouncementStepCreationServices' => $this->typeCourseAnnouncementStepCreationServices,
            'SectionDefaultFrontServices' => $this->defaultFrontServices,
            'SettingServices' => $this->services,
            'AlertTypeTutorServices' => $this->alertTypeTutorServices,
        ];
    }

    public function executeAllCatalog()
    {
        try{
            $messages = [];
            foreach ($this->getObjectData()  as $key => $catalog) {
                $catalog->load();
                $messages[] = "El servicio '$key' se executado correctament";
            }
            return $messages;
        }
        catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
       
    }

    public function executeSeviceCatalog($service){
        try{
            $this->getObjectData()[$service]->load();
        }
        catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function getCatalogs(){
        $catalogs = $this->em->getRepository(Catalog::class)->createQueryBuilder('cs')
            ->select('cs.id, cs.name, cs.service, cs.description')
            ->where('cs.service IS NOT NULL')
            ->getQuery()
            ->getResult();

        $data = [];

        foreach ($catalogs as $catalog) {
            $data[] = [
                'id' => $catalog['id'],
                'name' => $catalog['name'],
                'description' => $catalog['description'],
                'service' => $catalog['service'],
            ];
        }
        return $data;
    }
}
