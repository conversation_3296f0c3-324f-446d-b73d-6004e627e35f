<?php

declare(strict_types=1);

namespace App\Service\Course\Traits;

use App\Entity\Chapter;
use App\Entity\UserCourseChapter;
use App\Service\Course\DT0\ChapterQueryParams;

trait CourseReportTrait
{
    private $params = [];

    private array $dataChapters = [];
    private array $dataUsersChapters = [];

    private function getChapterProgressPercentages(Chapter $chapter): array
    {
        $course = $chapter->getCourse();
        $users = $this->getUsers($course);
        $userCount = \count($users);

        if (0 === $userCount) {
            return [
                'time' => 0,
                'inProgress' => 0,
                'finished' => 0,
            ];
        }

        $queryParamsChapter = $this->getChapterQueryParameters($chapter);

        $timeByChapter = $this->em->getRepository(UserCourseChapter::class)->getTimeByChapter($queryParamsChapter);
        $totalUserFinished = $this->em->getRepository(UserCourseChapter::class)->getTotalUserFinishedChapters($queryParamsChapter);
        $totalUserInProgress = $this->em->getRepository(UserCourseChapter::class)->chapterGetTotalUsersStarted($queryParamsChapter);

        $percentageFinished = $this->calculatePercentage(\intval($totalUserFinished['finished']) ?? 0, $userCount);
        $percentageProgress = $this->calculatePercentage(\intval($totalUserInProgress) ?? 0, $userCount);

        return [
            'time' => \intval($timeByChapter['totalTime'] ?? 0),
            'inProgress' => $percentageProgress,
            'finished' => $percentageFinished,
        ];
    }

    private function calculatePercentage(int $value, int $total): float
    {
        return $total > 0 ? round(($value / $total) * 100, 1, PHP_ROUND_HALF_UP) : 0;
    }

    protected function getChapterQueryParameters(Chapter $chapter): ChapterQueryParams
    {
        $course = $chapter->getCourse();
        $users = $this->getUsers($course);

        $courseParams = $this->getParamsCourse($course);

        $queryParamsChapter = ChapterQueryParams::create([
            'chapter' => $chapter,
            'findUsers' => false,
            'users' => $users,
            'dateFrom' => $courseParams['dateFrom'],
            'dateTo' => $courseParams['dateTo'],
            'courseStartedOnTime' => $courseParams['courseStartedIntime'],
            'announcementId' => $courseParams['announcementId'],
            'courseFinishedOnTime' => $courseParams['courseFinishedIntime'],
        ]);

        return $queryParamsChapter;
    }
}
