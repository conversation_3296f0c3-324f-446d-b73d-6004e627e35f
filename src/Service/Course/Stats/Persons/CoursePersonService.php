<?php

declare(strict_types=1);

namespace App\Service\Course\Stats\Persons;

use App\Entity\Announcement;
use App\Entity\Course;
use App\Entity\Itinerary;
use App\Entity\User;
use App\Service\Course\Common\UserCourseService;
use App\Service\Course\GlobalFilter;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;

class CoursePersonService extends BasePersonService
{
    private UserCourseService $userCourseService;

    public function __construct(
        RequestStack $requestStack,
        GlobalFilter $globalFilter,
        EntityManagerInterface $em,
        UserCourseService $userCourseService
    ) {
        parent::__construct($requestStack, $globalFilter, $em);
        $this->userCourseService = $userCourseService;
    }

    public function getPersons(?Course $course = null, ?Announcement $announcement = null, ?Itinerary $itinerary = null): array
    {
        $request = $this->requestStack->getCurrentRequest();
        $page = \intval($request->get('page', 1));
        $pageSize = \intval($request->get('page-size', 20));
        $content = json_decode($request->getContent(), true);
        $findUsers = false;
        $this->globalFilter->cleanFilters($content);
        $this->globalFilter->findUsersFilter($content, $findUsers);

        $usersIds = $this->userCourseService->getAllUsersIds($course, $content);

        if (empty($usersIds)) {
            return [
                'status' => Response::HTTP_ACCEPTED,
                'error' => false,
                'data' => [
                    'totalUsers' => 0,
                    'data' => [],
                ],
            ];
        }

        $pageUsersIds = \array_slice($usersIds, ($page - 1) * $pageSize, $pageSize);
        /** @var User[] $users */
        $users = $this->em->getRepository(User::class)->findBy(['id' => $pageUsersIds]);

        $data = [];
        foreach ($users as $user) {
            $data[] = $this->userCourseService->getUserData($course, $user, $announcement);
        }

        return [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'totalUsers' => \count($usersIds),
                'totalStarted' => $this->userCourseService->courseGetTotalUsersStarted($course, $findUsers, $usersIds),
                'totalFinished' => $this->userCourseService->courseGetTotalUsersFinished($course, $findUsers, $usersIds),
                'data' => $data,
            ],
        ];
    }
}
