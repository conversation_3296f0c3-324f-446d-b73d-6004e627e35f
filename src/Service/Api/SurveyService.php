<?php

namespace App\Service\Api;

use App\Entity\Announcement;
use App\Entity\NpsQuestion;
use App\Entity\SurveyAnnouncement;
use App\Entity\User;
use App\Repository\NpsQuestionRepository;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;

class SurveyService extends AbstractBaseService
{  
    private  $npsQuestionRepository;   

    public function __construct(
        EntityManagerInterface $em,     
        SettingsService $settings,
        NpsQuestionRepository $npsQuestionRepository
       
    ) {
        parent::__construct($em, $settings);  
        $this->npsQuestionRepository = $npsQuestionRepository;     
    }  

    public function getSurveyAnnouncement(Announcement $announcement, User $user){
        $surveyAnnouncement = $this->em->getRepository(SurveyAnnouncement::class)->findOneBy(['announcement' => $announcement]);

        if(!$surveyAnnouncement) {
           return  $this->getQuestionsMain($user);                
        } 
         
        return $this->getQuestionNpsAnouncement($user, $surveyAnnouncement);
        
    }
        
    private function getQuestionsMain(User $user){
        $locale = $this->getLocaleUser($user);  
        $questions = [];
        $questionsMain = $this->em->getRepository(NpsQuestion::class)->findBy(['main' => 1, 'source' => 1]);
        foreach ($questionsMain as $q) {
            $questionTranslate = $this->npsQuestionRepository->getNpsTranslation($q->getId(), $locale);
            $questions[] = [
                'id'       => $q->getId(),
                'question' => $questionTranslate ? $questionTranslate['question'] : $q->getQuestion(),
                'type'     => $q->getType(),
                'main'     => 1
            ];
        }
        return $questions;
    }

    private function getQuestionNpsAnouncement(User $user, SurveyAnnouncement $surveyAnnouncement){
        $locale = $this->getLocaleUser($user);  
        $questions = [];
        $npsQuestions = $this->npsQuestionRepository->findBy(['survey' => $surveyAnnouncement->getSurvey(), 'active' => true]);

        foreach ($npsQuestions as $npsQuestion) {
            $questionTranslate = $this->npsQuestionRepository->getNpsTranslation($npsQuestion->getId(), $locale);
            $questions[] = [
                'id' => $npsQuestion->getId(),
                'question' =>$questionTranslate ? $questionTranslate['question'] : $npsQuestion->getQuestion(),
                'type' => $npsQuestion->getType(),
                'main' => $npsQuestion->getMain(),
            ];
        }

        return $questions;
    }

    private function getLocaleUser(User $user){
        $languageDefault = $this->settings->get('app.defaultLanguage');       
        $locale = (isset($user) && $user->getLocaleCampus() != null) ? $user->getLocaleCampus() : $languageDefault;

        return $locale;
    }  
}
