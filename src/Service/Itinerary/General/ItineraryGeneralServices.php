<?php

declare(strict_types=1);

namespace App\Service\Itinerary\General;

use App\Entity\Course;
use App\Entity\Itinerary;
use App\Entity\User;
use App\Utils\SpreadsheetUtil;
use App\Utils\TimeUtils;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpKernel\KernelInterface;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\Translation\TranslatorInterface;

class ItineraryGeneralServices
{
    public const ZIP_PATH = 'xls';
    protected EntityManagerInterface $em;
    protected TranslatorInterface $trans;
    private Security $security;

    private string $baseDir;

    private $locale = 'es';
    private array $params = [];

    public function __construct(
        EntityManagerInterface $em,
        KernelInterface $kernel,
        TranslatorInterface $trans,
        Security $security
    ) {
        $this->em = $em;
        $this->baseDir = $kernel->getProjectDir() . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR . self::ZIP_PATH;
        if (!file_exists($this->baseDir)) {
            mkdir($this->baseDir);
        }
        $this->trans = $trans;
        $this->security = $security;
    }

    private function getUser()
    {
        return $this->security->getUser();
    }

    public function setLocale($locale)
    {
        $this->locale = $locale;
    }

    public function setParams(array $params)
    {
        $this->params = $params;
    }

    public function getParams(): array
    {
        return $this->params;
    }

    public function getItinerayInfoData($itinerary)
    {
        return $this->em->getRepository(Itinerary::class)->find($itinerary);
    }

    public function fetchCourseChapterIds(Course $course): array
    {
        return $course->getChapters()->map(function ($chapter) {
            return $chapter->getId();
        })->toArray();
    }

    public function getItineraryCourses(Itinerary $itinerary): array
    {
        $coursesItineray = $itinerary->getItineraryCourses();

        $courses = [];

        foreach ($coursesItineray as $courseItinerary) {
            $courses[] = $courseItinerary->getCourse();
        }

        return $courses;
    }

    public function createSpreadSheet(string $fileName, string $title = 'DEFAULT'): SpreadsheetUtil
    {
        return new SpreadsheetUtil(
            $this->getTransReport($fileName),
            $this->getTransReport($title)
        );
    }

    public function getTransReport($wordTranslate, $tag = 'reports'): string
    {
        return $this->trans->trans($wordTranslate, [], $tag, $this->locale);
    }

    public function getBaseDir(): string
    {
        $baseDir = $this->baseDir;
        if (isset($this->params['baseDir'])) {
            $baseDir = $this->params['baseDir'];
        }

        return $baseDir;
    }

    public function getItineraries($user)
    {
        $query = $this->em->getRepository(Itinerary::class)->createQueryBuilder('i')
            ->join('i.createdBy', 'u')
            ->leftJoin('i.itineraryCourses', 'ic');

        if ($user) {
            if (!$user instanceof User) {
                $user = $this->em->getRepository(User::class)->find($user);
            }

            if (!\in_array('ROLE_ADMIN', $user->getRoles())) {
                $query->leftJoin('i.itineraryManagers', 'im');
                $query->andWhere('i.createdBy = :user OR im.user = :user')
                    ->setParameter('user', $user);
            }
        }

        $itineraries = $query->select('i.id', 'i.name', 'i.active', 'i.sort')
            ->addSelect('u.id as user_id', 'u.firstName', 'u.lastName', 'u.email', 'i.createdAt')
            ->addSelect('count(ic) as totalCourses')
            ->groupBy('i.id')
            ->orderBy('i.sort, i.id', 'asc')
            ->getQuery()
            ->getResult();

        $dataItineraries = [];

        foreach ($itineraries as $itinerary) {
            $dataItineraries[] = $this->getDataItineraries($itinerary);
        }

        return $dataItineraries;
    }

    private function getDataItineraries($itinerary): array
    {
        $objItinerary = $this->getItinerayInfoData($itinerary['id']);
        $totalTime = $this->getItinerayTimeSpent($objItinerary);

        $dataProcess = $this->getDataItinerarysProcess($objItinerary);
        $totalUsers = $dataProcess['TotalPersonas'] ?? 0;
        $avrgTimerPerson = $totalTime > 0 ? $totalUsers > 0 ? round($totalTime / $totalUsers, 0) : '0' : '0';

        $createdAt = $itinerary['createdAt'];

        return [
            'id' => $itinerary['id'],
            'itinerary' => $itinerary['name'],
            'totalCourse' => $itinerary['totalCourses'],
            'totalUsers' => $totalUsers > 0 ? $totalUsers : '0',
            'notStarting' => $dataProcess['unstarted'] > 0 ? $dataProcess['unstarted'] : '0',
            'avrgNotStarting' => $dataProcess['avgTotalUnstarted'] . '%',
            'process' => $dataProcess['started'] > 0 ? $dataProcess['started'] : '0',
            'avrgProcess' => $dataProcess['avgTotalStarted'] . '%',
            'finalized' => $dataProcess['completed'] > 0 ? $dataProcess['completed'] : '0',
            'avrgFinalized' => $dataProcess['avgTotalComplete'] . '%',
            'totalTime' => $totalTime > 0 ? TimeUtils::formatTime($totalTime) : '-',
            'avrgTimerPerson' => $avrgTimerPerson > 0 ? TimeUtils::formatTime($avrgTimerPerson) : '-',
            'activo' => $itinerary['active'] ? 'Si' : 'No',
            'author' => $itinerary['email'],
            'created_at' => $createdAt->format('d-m-Y H:i:s'),
        ];
    }

    private function getItinerayTimeSpent($itinerary)
    {
        $dataItinerary = $this->em->getRepository(Itinerary::class)->getTimeSpentInCourses($itinerary);

        return $dataItinerary['total_time'] ?? 0;
    }

    private function storeItineraryStastSumary(Itinerary $itinerary): array
    {
        $itineraryRepository = $this->em->getRepository(Itinerary::class);

        $totalCourses = $itineraryRepository->getCoursesTotalFromItinerary($itinerary);
        $totalUsers = $itineraryRepository->getUsersTotalFromItinerary($itinerary);
        $totalUsersCompleted = 0;
        $totalUsersStarted = 0;
        $totalUsersTime = 0;

        $statsUsers = $itineraryRepository->getUsersTotalsFromItineraryByCompletionStatus($itinerary);
        foreach ($statsUsers as $statsUser) {
            if ($statsUser['completed'] == $totalCourses) {
                ++$totalUsersCompleted;
            } elseif ($statsUser['completed'] > 0 || $statsUser['started'] > 0) {
                ++$totalUsersStarted;
            }
            $totalUsersTime += $statsUser['total_time'];
        }

        $coursesStats = [];
        $coursesStats['totalCourses'] = $totalCourses;
        $usersStats = [];
        $usersStats['totalUsers'] = $totalUsers;
        $usersStats['totalUsersTime'] = $totalUsersTime;
        $usersStats['totalUsersCompleted'] = $totalUsersCompleted;
        $usersStats['totalUsersStarted'] = $totalUsersStarted;
        $usersStats['statsUsers'] = $statsUsers;
        $dataStats = [];
        $dataStats['coursesStats'] = $coursesStats;
        $dataStats['usersStats'] = $usersStats;

        return $dataStats;
    }

    private function getDataItinerarysProcess(Itinerary $itinerary): array
    {
        $itineraryRepository = $this->em->getRepository(Itinerary::class);

        $totalCompleted = 0;
        $totalStarted = 0;
        $totalUnstarted = 0;
        $totalPersonas = 0;
        $usersManual = 0;
        $usersItinerary = 0;

        $totalCourses = \count($itinerary->getItineraryCourses());

        $usersManual = $itineraryRepository->getUsersPaginated($itinerary, []);
        $usersItinerary = $itineraryRepository->getUsersByItineraryFilters($itinerary);

        foreach ([$usersManual, $usersItinerary] as $list) {
            if (!\is_array($list)) {
                continue;
            }

            foreach ($list as $user) {
                if (isset($users[$user['id']])) {
                    continue;
                }

                $users[$user['id']] = $user;
                if ($user['completed'] == $totalCourses) {
                    ++$totalCompleted;
                } elseif ($user['completed'] > 0 || $user['started'] > 0) {
                    ++$totalStarted;
                } else {
                    ++$totalUnstarted;
                }
            }
        }

        $totalPersonas = $totalCompleted + $totalStarted + $totalUnstarted;
        $avgTotalComplete = $this->getAvgTotal($totalPersonas, $totalCompleted);
        $avgTotalStarted = $this->getAvgTotal($totalPersonas, $totalStarted);
        $avgTotalUnstarted = $this->getAvgTotal($totalPersonas, $totalUnstarted);

        return [
            'completed' => 0 != $totalCompleted ? $totalCompleted : 0,
            'avgTotalComplete' => $avgTotalComplete,
            'started' => 0 != $totalStarted ? $totalStarted : 0,
            'avgTotalStarted' => $avgTotalStarted,
            'unstarted' => $totalUnstarted,
            'avgTotalUnstarted' => $avgTotalUnstarted,
            'TotalPersonas' => $totalPersonas,
        ];
    }

    private function getAvgTotal($totalPersonas, $valor)
    {
        return $totalPersonas > 0 ? $valor > 0 ? round(($valor / $totalPersonas) * 100, 2) : 0 : 0;
    }
}
