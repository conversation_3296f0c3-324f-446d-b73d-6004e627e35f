<?php

declare(strict_types=1);

namespace App\Service\Itinerary\General;

use App\Entity\Itinerary;
use App\Service\Course\Report\General\ContextStatsReports;
use App\Service\Course\Report\General\CourseStatsReport;
use App\Utils\SpreadsheetUtil;
use App\Utils\ToolsUtils;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use Psr\Log\LoggerInterface;

class ItineraryCourseReports extends BaseReport
{
    private ItineraryGeneralServices $itineraryGeneralServices;
    private CourseStatsReport $courseStatsReport;

    private LoggerInterface $logger;

    public function __construct(
        LoggerInterface $logger,
        ItineraryGeneralServices $itineraryGeneralServices,
        CourseStatsReport $courseStatsReport
    ) {
        $this->logger = $logger;
        $this->itineraryGeneralServices = $itineraryGeneralServices;
        $this->courseStatsReport = $courseStatsReport;
    }

    public function generate($itinerary, array $params = []): string
    {
        $this->logger->info('Itinerary_generate_reports_course [Started]');
        if (!$itinerary instanceof Itinerary) {
            $itinerary = $this->itineraryGeneralServices->getItinerayInfoData($itinerary);
        }

        if (null === $itinerary) {
            throw new \Exception('Itinerary not found');
        }

        $this->logger->info('Itinerary_generate_reports_course [Complete]');

        return $this->generateReportCourses($itinerary, $params);
    }

    private function generateReportCourses(Itinerary $itinerary, array $params = []): string
    {
        $courses = $this->itineraryGeneralServices->getItineraryCourses($itinerary);

        $strategy = new ContextStatsReports($this->courseStatsReport);
        $baseDir = $this->itineraryGeneralServices->getBaseDir();
        $baseDir .= DIRECTORY_SEPARATOR . $itinerary->getId() . '-' .
                    ToolsUtils::str_without_accents(trim($itinerary->getName()));

        $dataCourse = [];
        $parameters = $this->evalParameters($params);
        $parameters['baseDir'] = $baseDir;

        foreach ($courses as $course) {
            $this->logger->info('Itinerary_Course_generate_reports_course ' . $course->getId() . ' [Started]');
            if (0 == \count($parameters['chapters'])) {
                $parameters['chapters'] = $this->itineraryGeneralServices->fetchCourseChapterIds($course);
            }
            $dataCourse[] = $strategy->generateReport($course, $parameters);
            $this->logger->info('Itinerary_Course_generate_reports_course ' . $course->getId() . ' [Complete]');
        }

        return $baseDir;
    }

    private function evalParameters(array $params = []): array
    {
        $parameters = [
            'userStatus' => 'active',
            'courseDetails' => true,
            'chapters' => [],
            'announcementId' => null,
            'baseDir' => '',
            'source' => [
                'itinerary'
            ]
        ];

        return array_merge($parameters, $params);
    }

    public function generateInfoGeneralItinerary($user): Spreadsheet
    {
        $this->logger->info('Itinerary_generate_reports_course [Started]');
        // TODO: Implement generateInfoGeneralItinerary() method.

        $fileName = $this->getTransReport('itinerary.label_in_plural', 'messages');
        $dateFile = new \DateTimeImmutable();
        $fileName = $fileName . '_' . $dateFile->format('dmY');

        $report = $this->itineraryGeneralServices->createSpreadSheet(
            $fileName,
            $this->getTransReport('reports.headers.itinerary_general')
        );

        $this->courseInfoGeneralItinerary($report, $user);

        // $saveReport = $this->itineraryGeneralServices->getBaseDir();
        // $report->saveReport($saveReport);
        $this->logger->info('Itinerary_generate_reports_course [Complete]');

        return $report->spreadsheet;
    }

    private function headersInfo()
    {
        return [
            $this->getTransReport('reports.headers.id_itinerary'),
            $this->getTransReport('reports.headers.itinerary_name'),
            $this->getTransReport('reports.headers.course_number'),
            $this->getTransReport('reports.headers.total_person'),
            $this->getTransReport('report.excel.course_details.general.not_started'),
            '% ' . $this->getTransReport('report.excel.course_details.general.not_started'),
            $this->getTransReport('report.excel.course_details.general.in_progress'),
            '% ' . $this->getTransReport('report.excel.course_details.general.in_progress'),
            $this->getTransReport('report.excel.course_details.general.finalized'),
            '% ' . $this->getTransReport('report.excel.course_details.general.finalized'),
            $this->getTransReport('report.excel.course_details.general.total_time'),
            $this->getTransReport('report.headers.average_time_person'),
            $this->getTransReport('reports.headers.active'),
            $this->getTransReport('report.excel.course_details.general.authorship'),
            $this->getTransReport('report.excel.course_details.general.create_date')
        ];
    }

    private function fillsInfo()
    {
        return [
            'A' => 'f4cccc',
            'B' => 'a4c2f4',
            'C' => 'd9d2e9',
            'D' => 'fce5cd',
            'E' => 'e6b8af',
            'F' => 'e6b8af',
            'G' => 'c9daf8',
            'H' => 'c9daf8',
            'I' => 'd9d2e9',
            'J' => 'd9d2e9',
            'K' => 'd9edd3',
            'L' => 'b6d7a8',
            'M' => 'ea9999',
            'N' => 'fff2cc',
            'O' => 'f4cccc',
        ];
    }

    private function courseInfoGeneralItinerary(SpreadsheetUtil $report, $user)
    {
        $report->addSheet($this->getTransReport('reports.headers.itinerary_general'));
        $report->setHeaders(
            $this->headersInfo(),
            true,
            true,
            true,
            1,
            [
                'fontSize' => 14,
                'bold' => true,
                'color' => '000000',
                'fill' => $this->fillsInfo(),
            ]
        );

        $row = 2;

        $data = $this->itineraryGeneralServices->getItineraries($user);

        $report->setCellAlignmentHorizontal(['A', 'B'], Alignment::HORIZONTAL_LEFT);
        $report->setCellAlignmentHorizontal(['F', 'G', 'H', 'I', 'J', 'K', 'L'], Alignment::HORIZONTAL_CENTER);
        $report->fromArray($data, '--', "A$row");
    }

    private function getTransReport(string $wordTranslate, string $tag = 'reports'): string
    {
        return $this->itineraryGeneralServices->getTransReport($wordTranslate, $tag);
    }
}
