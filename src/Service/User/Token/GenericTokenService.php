<?php

namespace App\Service\User\Token;

use App\Entity\AnnouncementGroupSession;
use App\Entity\GenericToken;
use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Core\Security;

class GenericTokenService
{
    private EntityManagerInterface $em;
    private Security $security;

    public function __construct(EntityManagerInterface $em, Security $security)
    {
        $this->em = $em;
        $this->security = $security;
    }

    public function handleGenericToken(string $sToken) {
        /** @var User $user */
        $user = $this->security->getUser();
        $token = $this->em->getRepository(GenericToken::class)->findOneBy(['token' => $sToken]);
        if (!$token) return [
            'error' => true,
            'data' => 'Invalid token'
        ];

        if (!$token->isValid(false)) return [
            'error' => true,
            'data' => 'Invalid token'
        ];


        /**
         * Every procedure must return a 'error' field and data field indicating success or failure for the process
         * [
         *  'error': true|false,
         *  'data': [
         *      'type': 'redirect'|'message',
         *      'value': mixed
         *  ]
         * ]
         */

        $result = [];
        switch ($token->getType()) {
            case GenericToken::TYPE_ANNOUNCEMENT_SESSION_ENTRY_QR:case GenericToken::TYPE_ANNOUNCEMENT_SESSION_EXIT_QR:
                $result = $this->em->getRepository(AnnouncementGroupSession::class)->handleToken($token, $user);
                break;
            default:
                throw new \Exception('Not implemented');
        }

        return $result;
    }
}
