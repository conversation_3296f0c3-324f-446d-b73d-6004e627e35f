<?php

declare(strict_types=1);

namespace App\Service\Annoucement\Admin;

use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementUser;
use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Psr\Log\LoggerInterface;

class AnnouncementParticipantService
{
    private EntityManagerInterface $em;
    private LoggerInterface $massImportLogger;

    public function __construct(EntityManagerInterface $em, LoggerInterface $massImportLogger)
    {
        $this->em = $em;
        $this->massImportLogger = $massImportLogger;
    }

    public function processParticipants(Worksheet $participantsSheet, array $announcements): void
    {
        $lastParticipantsRow = $participantsSheet->getHighestDataRow();
        $processedParticipants = 0;
        $skippedParticipants = 0;
        $userNotFoundCount = 0;
        $courseNotFoundCount = 0;
        $emptyEmailCount = 0;

        $this->massImportLogger->info('Mass import: Starting participant processing', [
            'total_rows' => $lastParticipantsRow - 1,
            'available_announcements' => \count($announcements),
        ]);

        // Pre-collect all emails to batch query users (optimization for large datasets)
        $emails = [];
        for ($row = 2; $row <= $lastParticipantsRow; ++$row) {
            $email = trim($participantsSheet->getCell("F$row")->getValue() ?? '');
            if (!empty($email)) {
                $emails[] = $email;
            }
        }

        // Batch query all users by email for better performance
        $users = [];
        if (!empty($emails)) {
            $userEntities = $this->em->getRepository(User::class)->findBy(['email' => array_unique($emails)]);
            foreach ($userEntities as $user) {
                $users[$user->getEmail()] = $user;
            }
        }

        // Process participants with optimized lookups
        for ($row = 2; $row <= $lastParticipantsRow; ++$row) {
            $courseCode = trim($participantsSheet->getCell("A$row")->getValue() ?? '');

            // Skip empty rows
            if (empty($courseCode)) {
                ++$courseNotFoundCount;
                continue;
            }

            // Check if course exists in announcements (O(1) lookup)
            if (!isset($announcements[$courseCode])) {
                ++$skippedParticipants;
                ++$courseNotFoundCount;

                continue;
            }

            $email = trim($participantsSheet->getCell("F$row")->getValue() ?? '');

            if (empty($email)) {
                ++$skippedParticipants;
                ++$emptyEmailCount;
                continue;
            }

            // Check if user exists in pre-loaded users (O(1) lookup)
            if (isset($users[$email])) {
                $user = $users[$email];
                $this->assignUserToAnnouncementGroup($user, $announcements[$courseCode]['group']);
                ++$processedParticipants;
            } else {
                ++$skippedParticipants;
                ++$userNotFoundCount;
            }
        }

        $this->massImportLogger->info('Mass import: Participant processing completed', [
            'participants_assigned' => $processedParticipants,
            'participants_skipped' => $skippedParticipants,
            'user_not_found' => $userNotFoundCount,
            'course_not_found' => $courseNotFoundCount,
        ]);

        $this->handleEmptyGroups($announcements);
    }

    /**
     * Handle announcements without participants by changing status to CONFIGURATION instead of deleting.
     */
    public function handleEmptyGroups(array $announcements): void
    {
        $emptyAnnouncementsCount = 0;
        $preservedAnnouncementsCount = 0;

        foreach ($announcements as $announcementData) {
            $group = $announcementData['group'];
            $announcement = $group->getAnnouncement();

            $allAnnouncementParticipants = $this->em->getRepository(AnnouncementUser::class)->findBy(['announcement' => $announcement]);

            if (empty($allAnnouncementParticipants)) {
                // Only change to CONFIGURATION if the entire announcement has no participants
                $announcement->setStatus(\App\Entity\Announcement::STATUS_CONFIGURATION);
                $this->em->persist($announcement);
                ++$emptyAnnouncementsCount;

                $this->massImportLogger->warning('Mass import: Announcement without participants', [
                    'announcement_code' => $announcement->getCode(),
                    'action' => 'status_changed_to_configuration',
                ]);
            } else {
                // Announcement has participants in some group, keep current status
                ++$preservedAnnouncementsCount;

                $this->massImportLogger->info('Mass import: Announcement preserved with existing participants', [
                    'announcement_code' => $announcement->getCode(),
                    'total_participants' => \count($allAnnouncementParticipants),
                    'action' => 'status_preserved',
                ]);
            }
        }

        $this->massImportLogger->info('Mass import: Empty groups handled', [
            'announcements_without_participants' => $emptyAnnouncementsCount,
            'announcements_with_participants' => $preservedAnnouncementsCount,
        ]);

        $this->em->flush();
    }

    public function assignUserToAnnouncementGroup(User $user, AnnouncementGroup $group): void
    {
        $announcement = $group->getAnnouncement();
        $announcementUser = $this->em->getRepository(AnnouncementUser::class)->findOneBy([
            'announcement' => $announcement,
            'user' => $user,
        ]);

        if (!$announcementUser) {
            $announcementUser = (new AnnouncementUser())
                ->setAnnouncement($announcement)
                ->setAnnouncementGroup($group)
                ->setUser($user);
            $this->em->persist($announcementUser);
            $this->em->flush();
        }
    }
}
