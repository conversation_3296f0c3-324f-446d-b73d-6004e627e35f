<?php

declare(strict_types=1);

namespace App\Service\Annoucement\ReportPdf\User;

use App\Entity\AnnouncementUser;
use App\Entity\ConfigurationClientAnnouncement;
use App\Entity\TypeIdentification;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Entity\UserFieldsFundae;
use App\Service\Annoucement\Admin\AnnouncementGroupService;
use App\Service\Annoucement\Admin\AnnouncementUserService;
use App\Service\Annoucement\Admin\TaskUserService;
use App\Service\Annoucement\ReportPdf\BaseFundaeReportGenerator;
use App\Service\Traits\Announcement\TutorTrait;
use App\V2\Infrastructure\Utils\MpdfFactory;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Security\Core\Security;
use Twig\Environment as TwigEnvironment;

class ReportBaseService extends BaseFundaeReportGenerator
{
    use TutorTrait;
    public const SOURCE_COMMAND = 'command';
    public const SOURCE_REQUEST = 'request';

    protected EntityManagerInterface $em;
    protected MpdfFactory $mpdfFactory;
    protected TwigEnvironment $twig;
    protected LoggerInterface $logger;
    protected $announcementUserService;
    protected $announcementGroupService;
    protected string $source = self::SOURCE_REQUEST;
    protected TaskUserService $taskUserService;
    protected Security $security;

    public function __construct(
        EntityManagerInterface $em,
        MpdfFactory $mpdfFactory,
        TwigEnvironment $twig,
        LoggerInterface $logger,
        AnnouncementUserService $announcementUserService,
        AnnouncementGroupService $announcementGroupService,
        TaskUserService $taskUserService,
        Security $security
    ) {
        parent::__construct($em, $mpdfFactory, $twig, $logger);
        $this->announcementUserService = $announcementUserService;
        $this->announcementGroupService = $announcementGroupService;
        $this->taskUserService = $taskUserService;
        $this->security = $security;
    }

    public function headReportUser(AnnouncementUser $announcementUser, ?User $user = null)
    {
        $userCourseRepository = $this->em->getRepository(UserCourse::class);
        $userCourse = $userCourseRepository->findOneBy([
            'announcement' => $announcementUser->getAnnouncement(),
            'user' => $announcementUser->getUser(),
        ]);
        $certificate = $this->em->getRepository(ConfigurationClientAnnouncement::class)->find(2);

        $userChapters = [];

        if ($userCourse) {
            foreach ($userCourse->getChapters() as $chapter) {
                $userChapters[$chapter->getChapter()->getId()] = $chapter;
            }
        }

        $timeByDay = [];
        $sumTimeDay = [];
        if ($userCourse) {
            foreach ($userCourse->getChapters() as $chapter) {
                if (date_format($chapter->getStartedAt(), 'Y-m-d') == date_format($chapter->getStartedAt(), 'Y-m-d')) {
                    $timeByDay[] = date_format($chapter->getStartedAt(), 'Y-m-d');
                }
            }

            $unique = array_unique($timeByDay);

            $suma = 0;
            foreach ($unique as $uni) {
                foreach ($userCourse->getChapters() as $chapter) {
                    if ($uni === date_format($chapter->getStartedAt(), 'Y-m-d')) {
                        $suma += $chapter->getTimeSpent();
                    }
                }
                $elem['date'] = $uni;
                $elem['total'] = $suma;

                array_push($sumTimeDay, $elem);
                $suma = 0;
            }
        }

        $informationExtraUser[$announcementUser->getUser()->getId()] = $this->getInformationExtraUser($announcementUser);

        $idGroup = $announcementUser->getAnnouncementGroup() && $announcementUser->getAnnouncementGroup()->getId() ? $announcementUser->getAnnouncementGroup()->getId() : null;
        $announcement = $announcementUser->getAnnouncement();

        return [
            'announcementUser' => $announcementUser,
            'certificateAvailable' => $certificate ? $certificate->isActive() : 0,
            'userCourse' => $userCourse,
            'userChapters' => $userChapters,
            'announcement' => $announcementUser->getAnnouncement(),
            'userId' => $announcementUser->getUser()->getId(),
            'informationExtraUser' => $informationExtraUser,
            'informationGroup' => $this->announcementGroupService->getInformationGroup($announcement, $idGroup, $user ? '' : ReportBaseService::SOURCE_REQUEST),
            'mainIdentification' => $this->em->getRepository(TypeIdentification::class)->getMainIdentificationForThePlatform($user ? $user->getLocale() : $this->security->getUser()->getLocale()),
            'identificationUser' => $this->getIdentificationUserForCertificate($announcementUser->getUser()),
        ];
    }

    private function getIdentificationUserForCertificate($user)
    {
        $userExtraFundae = $this->em->getRepository(UserFieldsFundae::class)->findOneBy(['user' => $user->getId()]);

        return $userExtraFundae ? $userExtraFundae->getDni() : $user->getRegisterKey();
    }

    private function getInformationExtraUser(AnnouncementUser $announcementUser)
    {
        return [
            'progressTotalCourse' => $this->announcementUserService->getProgressTotal($announcementUser),
            'progressTask' => $this->announcementUserService->getProgressUserTask($announcementUser),
            'downloadDiploma' => $this->announcementUserService->confirmDownloadedDiploma($announcementUser),
            'timeSpent' => $this->announcementUserService->getTotalInTheCourse($announcementUser),
            'progressTotalHour' => $this->announcementUserService->getProgressTotalHour($announcementUser),
            'firstConexion' => $this->announcementUserService->getFirstConexion($announcementUser),
            'lastConexion' => $this->announcementUserService->getLastConexion($announcementUser),
        ];
    }
}
