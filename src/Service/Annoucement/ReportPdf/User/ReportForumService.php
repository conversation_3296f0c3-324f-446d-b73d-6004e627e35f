<?php

namespace App\Service\Annoucement\ReportPdf\User;

use App\Entity\ChatChannel;
use App\Entity\ChatMessage;

class ReportForumService extends ReportBaseService
{
    public function generatePdf(ChatChannel $forumChannel, ?string $fullPath = null) {
        $threads = $this->em->getRepository(ChatChannel::class)->findBy([
            'parent' => $forumChannel
        ]);

        $data = [];
        foreach ($threads as $thread) {
            $data[$thread->getId()] = [
                'name' => $thread->getName(),
                'messages' => $this->em->getRepository(ChatMessage::class)->getMessages($thread, null, null, null, -1)
            ];
        }

        $mpdf = $this->generate(
            'fundae/report_pdf_general/forum.html.twig',
            [
                'threads' => $data
            ]
        );

        if (!empty($fullPath)) $mpdf->Output($fullPath, 'F');
        return $mpdf;
    }
}
