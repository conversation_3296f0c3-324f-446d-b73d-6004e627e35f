<?php

declare(strict_types=1);

namespace App\Service\Nps;

use App\Admin\Traits\UserManagerTrait;
use App\Entity\Course;
use App\Entity\CourseStat;
use App\Entity\Nps;
use App\Entity\UserCourse;
use App\Repository\CourseStatRepository;
use App\Repository\NpsRepository;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;

class NpsService
{
    use UserManagerTrait;

    public function __construct(
        private readonly SettingsService $settings,
        private readonly EntityManagerInterface $em,
        private readonly CourseStatRepository $courseStatRepository,
        private readonly NpsRepository $npsRepository,
        private readonly NpsExtraAnswerUserService $NpsExtraAnswerUserService
    ) {
    }

    public function actionsNpsCourseStat(?UserCourse $userCourse = null): void
    {
        if (!$userCourse) {
            return;
        }

        $course = $userCourse->getCourse();

        // Obtener la valoracion actual del usuario en el curso
        $nps = $this->em->getRepository(Nps::class)->findOneBy(['course' => $userCourse, 'main' => 1, 'type' => 'nps']);
        if (!$nps) {
            return;
        }

        $valorationNps = $nps->getValue() ?? 0;

        // Obtener la valoracion total del curso
        $courseStat = $this->em->getRepository(CourseStat::class)->findOneBy(['course' => $course]);

        if (!$courseStat) {
            $courseStat = (new CourseStat())
                ->setCourse($course)
                ->setValorationNps((float) $valorationNps)
                ->setCountValorationNps(1.0);
        } else {
            $courseStat
                ->setValorationNps((float) ($courseStat->getValorationNps() + $valorationNps))
                ->setCountValorationNps((float) ($courseStat->getCountValorationNps() + 1));
        }

        $this->em->persist($courseStat);
        $this->em->flush();
    }

    public function initilizeAndNewRegisterTableCourseStat(): string
    {
        $courseStats = $this->courseStatRepository->findAll();

        if (empty($courseStats)) {
            return $this->initializeTable();
        }

        $message = $this->updateExistingRecords();
        $message .= $this->addNewRecords();

        return $message ?: 'No se realizaron cambios en la tabla Course_stat';
    }

    private function initializeTable(): string
    {
        $success = $this->courseStatRepository->initializeTableCourseStat();

        return $success
            ? 'Course_stat: Tabla inicializada correctamente'
            : 'Course_stat: Error al inicializar - revisar error-log';
    }

    private function updateExistingRecords(): string
    {
        if ($this->courseStatRepository->updateTableCourseStat()) {
            return 'Course_stat: Registros actualizados';
        }

        return '';
    }

    private function addNewRecords(): string
    {
        if ($this->courseStatRepository->initAndNewRegisterTableCourseStat()) {
            return "\nCourse_stat: Nuevos registros agregados correctamente";
        }

        return '';
    }

    public function statCourse(Course $course): float|int
    {
        $stat = $this->courseStatRepository->findOneBy(['course' => $course]);

        if (!$stat || 0 == $stat->getCountValorationNps()) {
            return 0;
        }

        $value_stat = $stat->getValorationNps() / (2 * $stat->getCountValorationNps());

        return round($value_stat * 2) / 2;
    }
}
