knpu_oauth2_client:
    clients:
        # configure your clients as described here: https://github.com/knpuniversity/oauth2-client-bundle#configuration
        factorial:
            type: generic
            provider_class: App\Security\OAuth2\Factorial\FactorialProvider

            # App basic credentials
            client_id: '%env(OAUTH2_FACTORIAL_CLIENT_ID)%'
            client_secret: '%env(OAUTH2_FACTORIAL_CLIENT_SECRET)%'

            # Read authentication result
            redirect_route: 'oauth2-authenticate'
            redirect_params: {}
