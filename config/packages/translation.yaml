
imports:
    - { resource: ../services/easylearning.yaml }

framework:
    default_locale: '%app.adminDefaultLanguage%'
    translator:
        default_path: '%kernel.project_dir%/translations'
        fallbacks: '%app.languages%'
        paths:
            - '%kernel.project_dir%/translations'            
        providers:
            loco:
                dsn: '%env(LOCO_DSN)%'
                domains: ['announcement', 'reports', 'chapters', 'messages', 'ranking', 'register_help', 'email', 'message_api', 'emailNotification', 'fixtures', 'translations', 'exceptions']
                locales: '%app.languages%'
