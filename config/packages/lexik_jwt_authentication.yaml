lexik_jwt_authentication:
    secret_key: '%kernel.project_dir%/%env(JWT_PRIVATE_KEY_PATH)%'
    public_key:  '%kernel.project_dir%/%env(JWT_PUBLIC_KEY_PATH)%'
    pass_phrase:      '%env(JWT_PASSPHRASE)%'
    token_ttl:        '%env(JWT_TOKENTTL)%'
    encoder:
        service: lexik_jwt_authentication.encoder.lcobucci
        signature_algorithm: RS512
    token_extractors:
        authorization_header:
            enabled: true
            name: Authorization
            prefix: Bearer
        cookie:
            enabled: true
            name: BEARER
    set_cookies:
        BEARER: ~