monolog:
    channels: ['mass_import']
    handlers:
        main:
            type: fingers_crossed
            action_level: error
            handler: nested
            excluded_http_codes: [404, 405]
            channels: ["!event", "!mass_import"]
        nested:
            type: stream
            path: "%kernel.logs_dir%/%kernel.environment%.log"
            level: debug
        mass_import:
            type: 'null'
            channels: ['mass_import']
