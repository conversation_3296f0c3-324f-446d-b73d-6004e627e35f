import { USER_API_ROUTES } from '@/contexts/users/constants/users.constants.js'
import { USER_ROLE_LIST } from '@/contexts/shared/constants/user.constants.js'

export default [
  {
    url: USER_API_ROUTES.HOME.LIST,
    callback: () => {
      const roles = Object.values(USER_ROLE_LIST)
      return {
        status: 200,
        error: false,
        data: {
          metadata: {
            total_pages: 1,
            total_items: 10,
          },
          data: new Array(10)
            .fill({
              name: 'test',
              lastName: 'test',
              email: '<EMAIL>',
              roles: [],
              points: 0,
              avatar: 'https://picsum.photos/200/300',
            })
            .map((user, index) => ({
              ...user,
              id: index + 1,
              avatar: `${user.avatar}`,
              roles: roles.filter(
                (_, index) => index > Math.min(Math.floor(Math.random() * roles.length) - 1, roles.length - 1)
              ),
              points: Math.floor(Math.random() * 9001),
            })),
        },
      }
    },
    method: 'post',
  },
  {
    url: USER_API_ROUTES.HOME.EXPORT,
    callback: () => ({
      status: 200,
      error: false,
      data: {},
    }),
    method: 'post',
  },
  {
    url: USER_API_ROUTES.HOME.IMPERSONATE,
    callback: () => ({
      status: 200,
      error: false,
      data: {
        url: window.location.origin + '/app/users',
      },
    }),
    method: 'post',
  },
  {
    url: USER_API_ROUTES.HOME.FILTER_LIST,
    callback: () => ({
      status: 200,
      error: false,
      data: [
        {
          id: 1,
          name: 'Deparment /ES)',
          filters: [
            {
              id: 1,
              name: 'Departamento 1 (ES)',
            },
            {
              id: 2,
              name: 'Departamento 2 (ES)',
            },
            {
              id: 11,
              name: 'Store Manager (ES)',
            },
          ],
        },
        {
          id: 2,
          name: 'País (ES)',
          filters: [
            {
              id: 3,
              name: 'ES',
            },
            {
              id: 4,
              name: 'EN',
            },
            {
              id: 5,
              name: 'PT',
            },
            {
              id: 6,
              name: 'FR',
            },
          ],
        },
        {
          id: 3,
          name: 'Zona (ES)',
          filters: [
            {
              id: 7,
              name: 'Norte',
            },
            {
              id: 8,
              name: 'Sur',
            },
            {
              id: 9,
              name: 'Centro',
            },
          ],
        },
        {
          id: 4,
          name: 'Certification process (ES)',
          filters: [
            {
              id: 10,
              name: 'Si',
            },
          ],
        },
        {
          id: 5,
          name: 'Tienda (ES)',
          filters: [
            {
              id: 12,
              name: 'Madrid',
            },
            {
              id: 13,
              name: 'Lisboa',
            },
          ],
        },
        {
          id: 6,
          name: 'zona (ES)',
          filters: [
            {
              id: null,
              name: null,
            },
          ],
        },
        {
          id: 7,
          name: 'País (ES)',
          filters: [
            {
              id: null,
              name: null,
            },
          ],
        },
        {
          id: 9,
          name: '✌️filtro✌️',
          filters: [
            {
              id: 15,
              name: '👍subfiltro👍',
            },
            {
              id: 16,
              name: '👍subfiltro👍',
            },
          ],
        },
      ],
    }),
    method: 'post',
  },
  {
    url: USER_API_ROUTES.HOME.TOGGLE_ACTIVE,
    callback: () => ({
      status: 200,
      error: false,
      data: {
        isActive: false,
      },
    }),
    method: 'post',
  },
  {
    url: USER_API_ROUTES.HOME.REMOVE,
    callback: () => ({
      status: 200,
      error: false,
      data: {},
    }),
    method: 'post',
  },
]
