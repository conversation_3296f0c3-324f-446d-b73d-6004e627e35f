import { INPUT_TYPES } from '@/contexts/shared/constants/input.constants.js'

export class UserListFilters {
  constructor({ filters = [], payload = {} } = {}) {
    this.filters = filters || []
    this.payload = payload || {}
  }

  getPayloadData() {
    const data = { ...this.payload.getPayloadData() }
    this.filters.forEach((filter) => {
      const payload = filter.getInputValue()
      if (filter.type === INPUT_TYPES.BOOLEAN || !!payload.value) {
        if (!data.filters) data.filters = []
        data.filters.push(payload)
      }
    })
    return data
  }
}
