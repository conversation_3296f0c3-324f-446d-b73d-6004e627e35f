import { USER_ROLE_NAMES } from '@/contexts/users/constants/users.constants.js'

export class UserHome {
  constructor({
    id = 0,
    name = '',
    lastName = '',
    email = '',
    roles = [],
    isActive = true,
    points = 0,
    avatar = '',
  } = {}) {
    this.id = id || 0
    this.key = `user_${this.id}`
    this.firstName = name || ''
    this.lastName = lastName || ''
    this.email = email || ''
    this.roles = (roles || []).map((roleCode, index) => {
      return {
        key: `${this.key}_role${index}`,
        code: USER_ROLE_NAMES[roleCode] || USER_ROLE_NAMES.default,
      }
    })
    this.isUpdating = false
    this.isActive = isActive
    this.score = points
    this.avatar = avatar || ''
  }
}
