<template>
  <div class="DynamicInput">
    <component
      :is="componentType"
      v-model="innerValue"
      :name="item.key"
      :label="item.name"
      :multiselect="item.type === INPUT_TYPES.MULTISELECT"
      :options="item.options"
      :error="item.error"
      track-by="id"
      text-by="name"
      v-bind="$attrs"
    />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { INPUT_TYPES } from '@/contexts/shared/constants/input.constants.js'
import { DynamicInputModel } from '@/contexts/shared/models/dynamicInput.model.js'
import BaseSwitch from '@/contexts/shared/components/BaseSwitch.vue'
import BaseInput from '@/contexts/shared/components/BaseInput.vue'
import BaseText from '@/contexts/shared/components/BaseText.vue'
import BaseSelect from '@/contexts/shared/components/BaseSelect.vue'
import BaseInputDate from '@/contexts/shared/components/BaseInputDate.vue'

const emit = defineEmits(['update:modelValue'])

const props = defineProps({
  item: {
    type: [DynamicInputModel, Object],
    default: () => new DynamicInputModel(),
  },
  modelValue: {
    type: [Array, Object, String, Number, Boolean],
    default: ' ',
  },
})

const componentType = computed(
  () =>
    ({
      [INPUT_TYPES.BOOLEAN]: BaseSwitch,
      [INPUT_TYPES.TEXTAREA]: BaseText,
      [INPUT_TYPES.SELECT]: BaseSelect,
      [INPUT_TYPES.MULTISELECT]: BaseSelect,
      [INPUT_TYPES.DATE]: BaseInputDate,
    })[props.item?.type] || BaseInput
)

const innerValue = computed({
  get: () => props.modelValue,
  set: (newValue) => emit('update:modelValue', newValue),
})
</script>
