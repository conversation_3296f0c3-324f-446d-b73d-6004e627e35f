<template>
  <span
    class="BaseBadge"
    :class="type"
  >
    <span
      v-show="label"
      class="badgeLabel"
      >{{ label }}</span
    >
    <span
      v-show="value"
      class="badgeValue"
      >{{ value }}</span
    >
  </span>
</template>

<script setup>
defineProps({
  label: { type: String, default: '' },
  value: { type: String, default: '' },
  type: {
    type: String,
    default: 'info',
    validator: (value) => ['info', 'primary', 'success', 'warning', 'danger'].includes(value),
  },
})
</script>

<style scoped lang="scss">
.BaseBadge {
  display: flex;
  width: fit-content;
  border-radius: 10000px;
  overflow: hidden;
  background-color: var(--color-neutral-mid-light);

  span {
    padding: 0.125rem 0.75rem;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 0.8rem;

    &.badgeLabel {
      color: var(--color-neutral-lightest);
      font-weight: bold;
    }
  }

  &.info {
    .badgeLabel {
      background-color: var(--color-neutral-dark);
    }
  }
  &.primary {
    .badgeLabel {
      background-color: var(--color-primary-darker);
    }
  }
  &.success {
    .badgeLabel {
      background-color: var(--color-success);
    }
  }
  &.warning {
    .badgeLabel {
      background-color: var(--color-warning);
    }
  }
  &.danger {
    .badgeLabel {
      background-color: var(--color-danger);
    }
  }
}
</style>
