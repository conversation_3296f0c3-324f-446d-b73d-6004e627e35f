<template>
  <div class="LTIDeploymentsTab">
    <header class="formButtonContainer">
      <BaseButton @click="emit('openForm')"><Icon icon="plus" /> {{ $t('LTI.HOME.NEW') }} </BaseButton>
    </header>
    <main>
      <div class="tableContainer">
        <table>
          <thead>
            <tr>
              <th class="id">{{ $t('LTI.HOME.HEADER1') }}</th>
              <th>{{ $t('SUBSCRIPTION.NAME') }}</th>
              <th>{{ $t('LTI.HOME.HEADER3') }}</th>
              <th>{{ $t('PAGES.ACTIONS') }}</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="item in innerValue"
              v-show="!isLoading"
              :key="item.id"
            >
              <td class="id">{{ item.id }}</td>
              <td>{{ item.name }}</td>
              <td>{{ item.deploymentId }}</td>
              <td>
                <DropdownMenu>
                  <span @click="emit('openForm', item)">{{ $t('EDIT') }}</span>
                  <span
                    class="danger"
                    @click="emit('openDialog', item)"
                    >{{ $t('DELETE') }}</span
                  >
                </DropdownMenu>
              </td>
            </tr>
            <tr v-if="isLoading || !innerValue.length">
              <td colspan="4">{{ $t(isLoading ? 'LOADING' : 'NO_DATA') }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </main>

    <BaseDialog
      :open-dialog="!!dialog?.open"
      :title="$t('LTI.HOME.REMOVE')"
      :content="`#${dialog?.data?.id}`"
      @confirm="emit('remove')"
      @close="emit('closeDialog')"
    />

    <BaseModal
      v-if="!!innerForm?.open"
      :title="$t(innerForm.data.id ? 'LTI.HOME.UPDATE' : 'LTI.HOME.NEW')"
      @close="emit('closeForm')"
    >
      <BaseInput
        v-if="innerForm.data.id"
        v-model="innerForm.data.id"
        name="id"
        class="idInput"
        :label="$t('LTI.HOME.HEADER1')"
        disabled
      />
      <div class="formData">
        <BaseInput
          v-model="innerForm.data.name"
          name="name"
          required
          :label="$t('SUBSCRIPTION.NAME')"
          :error="innerForm.data.errors?.name"
        />
        <BaseInput
          v-model="innerForm.data.deploymentId"
          name="deploymentId"
          required
          :label="$t('LTI.HOME.HEADER3')"
          :error="innerForm.data.errors?.deployment_id"
        />
      </div>
      <div class="formButtonContainer">
        <BaseButton @click="emit('submit')">{{ $t('ALERTIFY.OK') }}</BaseButton>
        <BaseButton
          type="danger"
          @click="emit('closeForm')"
        >
          {{ $t('ALERTIFY.CANCEL') }}
        </BaseButton>
      </div>
    </BaseModal>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import DropdownMenu from '@/contexts/shared/components/DropdownMenu.vue'
import BaseDialog from '@/contexts/shared/components/BaseDialog.vue'
import BaseInput from '@/contexts/shared/components/BaseInput.vue'
import BaseModal from '@/contexts/shared/components/BaseModal.vue'
import BaseButton from '@/contexts/shared/components/BaseButton.vue'

const emit = defineEmits([
  'update:content',
  'update:form',
  'openForm',
  'closeForm',
  'openDialog',
  'closeDialog',
  'submit',
  'remove',
])
const props = defineProps({
  content: { type: Array, default: () => [] },
  isLoading: { type: Boolean, default: false },
  form: { type: Object, default: () => ({}) },
  dialog: { type: Object, default: () => ({}) },
})
const innerValue = computed({
  get: () => props.content,
  set: (newValue) => {
    emit('update:content', newValue)
  },
})
const innerForm = computed({
  get: () => props.form,
  set: (newValue) => {
    emit('update:form', newValue)
  },
})
</script>
<style lang="scss" scoped>
.LTIDeploymentsTab {
  th,
  td {
    padding: 0.25rem 0.5rem;
    text-align: center;

    &.id {
      width: 400px;
    }
  }
  .idInput {
    margin-bottom: 1rem;
  }
  .DropdownMenu {
    margin: auto;
  }
  header .BaseButton {
    margin: 0 0 1rem auto;
  }
}
</style>
